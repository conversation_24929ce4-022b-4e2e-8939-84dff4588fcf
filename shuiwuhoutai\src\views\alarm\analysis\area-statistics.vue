<template>
  <div class="area-alarm-statistics">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-button type="primary" @click="refreshData">刷新</el-button>
        </div>
      </template>

      <!-- 区域选择 -->
      <el-form
        :model="searchForm"
        label-width="80px"
        inline
        style="margin-bottom: 20px"
      >
        <el-form-item label="选择区域">
          <el-select v-model="searchForm.area" placeholder="请选择区域">
            <el-option label="全部区域" value="" />
            <el-option label="A区" value="A" />
            <el-option label="B区" value="B" />
            <el-option label="C区" value="C" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </el-form-item>
      </el-form>

      <!-- 区域告警统计图表 -->
      <div id="areaChart" style="height: 400px; margin-bottom: 20px"></div>

      <!-- 区域告警统计表格 -->
      <el-table :data="areaStatistics" style="width: 100%" v-loading="loading">
        <el-table-column prop="areaName" label="区域名称" />
        <el-table-column prop="totalCount" label="总告警数" width="100" />
        <el-table-column prop="urgentCount" label="紧急" width="80">
          <template #default="scope">
            <span class="urgent">{{ scope.row.urgentCount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="importantCount" label="重要" width="80">
          <template #default="scope">
            <span class="important">{{ scope.row.importantCount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="normalCount" label="一般" width="80">
          <template #default="scope">
            <span class="normal">{{ scope.row.normalCount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="alarmRate" label="告警率" width="100">
          <template #default="scope">
            <span>{{ scope.row.alarmRate }}%</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="avgResponseTime"
          label="平均响应时间"
          width="120"
        />
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(scope.row)"
              >查看详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";

// 搜索表单
const searchForm = reactive({
  area: "",
  dateRange: [],
});

// 区域告警统计数据
const areaStatistics = ref([
  {
    areaName: "A区",
    totalCount: 78,
    urgentCount: 8,
    importantCount: 25,
    normalCount: 45,
    alarmRate: 15.6,
    avgResponseTime: "2.5小时",
  },
  {
    areaName: "B区",
    totalCount: 45,
    urgentCount: 3,
    importantCount: 12,
    normalCount: 30,
    alarmRate: 9.8,
    avgResponseTime: "1.8小时",
  },
  {
    areaName: "C区",
    totalCount: 33,
    urgentCount: 1,
    importantCount: 8,
    normalCount: 24,
    alarmRate: 7.2,
    avgResponseTime: "3.2小时",
  },
]);

const loading = ref(false);

// 查询
const handleSearch = () => {
  console.log("搜索条件:", searchForm);
  loadData();
};

// 刷新数据
const refreshData = () => {
  loadData();
};

// 查看详情
const handleViewDetail = (row: any) => {
  console.log("查看区域告警详情:", row);
};

// 加载数据
const loadData = () => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
    initChart();
  }, 1000);
};

// 初始化图表
const initChart = () => {
  // 这里可以使用ECharts等图表库来绘制区域告警统计图表
  console.log("初始化区域告警统计图表");
};

onMounted(() => {
  loadData();
});
</script>

<style scoped>
.area-alarm-statistics {
  padding: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.urgent {
  color: #f56c6c;
  font-weight: bold;
}
.important {
  color: #e6a23c;
  font-weight: bold;
}
.normal {
  color: #67c23a;
  font-weight: bold;
}
</style>
