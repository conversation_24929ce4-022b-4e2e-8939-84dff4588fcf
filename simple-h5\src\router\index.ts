import { createRouter, createWebHashHistory } from 'vue-router'
import StationSelectView from '../views/StationSelectView.vue'
import DeviceStatusView from '../views/DeviceStatusView.vue'
import HistoryView from '../views/HistoryView.vue'

const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      name: 'home',
      component: StationSelectView,
    },
    {
      path: '/stations',
      name: 'stations',
      component: StationSelectView,
    },
    {
      path: '/device/:stationId',
      name: 'device',
      component: DeviceStatusView,
      props: true
    },
    {
      path: '/history/:stationId?',
      name: 'history',
      component: HistoryView,
      props: true
    },
  ],
})

export default router
