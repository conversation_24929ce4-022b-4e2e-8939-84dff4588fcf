# /f:/水利站/backend/mysql_connection.py
from sqlalchemy import create_engine, Integer, String, DateTime, JSON, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session, Mapped, mapped_column
from sqlalchemy.sql import func
import datetime
from typing import Optional, Dict, Any

# MySQL连接配置
MYSQL_HOST = "localhost"
MYSQL_PORT = 3311  # Docker端口映射为3311
MYSQL_DATABASE = "water_station_config"
MYSQL_USERNAME = "water_user"
MYSQL_PASSWORD = "water123"

# MySQL连接URL
MYSQL_DATABASE_URL = f"mysql+pymysql://{MYSQL_USERNAME}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}"

# 创建MySQL引擎
mysql_engine = create_engine(
    MYSQL_DATABASE_URL,
    echo=False,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
    pool_recycle=3600
)

# 创建MySQL会话类
MySQLSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=mysql_engine)

# 创建MySQL Base类
MySQLBase = declarative_base()

class TaskMetadata(MySQLBase):
    """
    用于持久化存储定时任务元数据的表
    """
    __tablename__ = "task_metadata"

    # 任务的唯一ID (例如 cycle_id, sequence_id)
    task_id: Mapped[str] = mapped_column(String(255), primary_key=True, index=True)
    # 任务类型，用于恢复时区分
    task_type: Mapped[str] = mapped_column(
        String(50), nullable=False, comment="任务类型 (e.g., 'cycle', 'sequence')"
    )
    # 存储任务的所有详细信息
    task_data: Mapped[dict] = mapped_column(
        JSON, nullable=False, comment="任务的完整元数据"
    )
    # 创建时间
    created_at: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        comment="创建时间"
    )

class SystemKVStore(MySQLBase):
    """
    用于存储系统级别的键值对状态，例如水泵轮换的下一个目标。
    这确保了即使在多进程或服务重启后，状态也能保持一致。
    """
    __tablename__ = "system_kv_store"

    key: Mapped[str] = mapped_column(String(255), primary_key=True, comment="状态的唯一键")
    value: Mapped[str] = mapped_column(String(1000), nullable=False, comment="状态的值")
    # 更新时间
    updated_at: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间"
    )

def create_mysql_db_and_tables():
    """
    根据我们定义的模型，在MySQL数据库中创建所有表。
    如果表已存在，则不会重复创建。
    """
    print("[mysql] 正在创建MySQL数据库表...")
    MySQLBase.metadata.create_all(bind=mysql_engine)
    print("[mysql] MySQL数据库表创建完成。")

def get_mysql_session() -> Session:
    """获取MySQL数据库会话"""
    return MySQLSessionLocal()

# MySQL操作类
class MySQLOperations:
    """MySQL数据操作类"""
    
    @staticmethod
    def create_task_metadata(task_id: str, task_type: str, metadata: dict) -> TaskMetadata:
        """
        创建一条任务元数据记录
        """
        db = get_mysql_session()
        try:
            db_task = TaskMetadata(
                task_id=task_id, task_type=task_type, task_data=metadata
            )
            db.add(db_task)
            db.commit()
            db.refresh(db_task)
            return db_task
        finally:
            db.close()
    
    @staticmethod
    def get_all_task_metadata():
        """
        获取所有任务元数据记录
        """
        db = get_mysql_session()
        try:
            return db.query(TaskMetadata).all()
        finally:
            db.close()
    
    @staticmethod
    def delete_task_metadata(task_id: str) -> bool:
        """
        根据任务ID删除一条元数据记录
        """
        db = get_mysql_session()
        try:
            db_task = (
                db.query(TaskMetadata)
                .filter(TaskMetadata.task_id == task_id)
                .first()
            )
            if db_task:
                db.delete(db_task)
                db.commit()
                return True
            return False
        finally:
            db.close()
    
    @staticmethod
    def get_kv(key: str) -> Optional[str]:
        """
        从数据库中获取一个键值对的值
        """
        db = get_mysql_session()
        try:
            record = (
                db.query(SystemKVStore).filter(SystemKVStore.key == key).first()
            )
            return record.value if record else None
        finally:
            db.close()
    
    @staticmethod
    def set_kv(key: str, value: str) -> SystemKVStore:
        """
        在数据库中设置一个键值对的值 (如果存在则更新, 不存在则创建)
        """
        db = get_mysql_session()
        try:
            record = (
                db.query(SystemKVStore).filter(SystemKVStore.key == key).first()
            )
            if record:
                record.value = value
            else:
                record = SystemKVStore(key=key, value=value)
                db.add(record)
            db.commit()
            db.refresh(record)
            return record
        finally:
            db.close()
    
    @staticmethod
    def get_all_kv() -> Dict[str, str]:
        """
        获取所有键值对
        """
        db = get_mysql_session()
        try:
            records = db.query(SystemKVStore).all()
            return {record.key: record.value for record in records}
        finally:
            db.close()
    
    @staticmethod
    def delete_kv(key: str) -> bool:
        """
        删除指定键值对
        """
        db = get_mysql_session()
        try:
            record = (
                db.query(SystemKVStore).filter(SystemKVStore.key == key).first()
            )
            if record:
                db.delete(record)
                db.commit()
                return True
            return False
        finally:
            db.close()

def test_mysql_connection():
    """测试MySQL连接"""
    try:
        db = get_mysql_session()
        # 执行简单查询测试连接
        result = db.execute(text("SELECT 1"))
        result.fetchone()
        db.close()
        print("[mysql] MySQL连接测试成功")
        return True
    except Exception as e:
        print(f"[mysql] MySQL连接测试失败: {e}")
        return False