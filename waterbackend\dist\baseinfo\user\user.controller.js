"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const common_1 = require("@nestjs/common");
const user_service_1 = require("./user.service");
const md5_1 = require("ts-md5/dist/md5");
const swagger_1 = require("@nestjs/swagger");
const roles_decorator_1 = require("../../roles.decorator");
const role_enum_1 = require("../../role.enum");
const roles_guard_1 = require("../../roles.guard");
const user_entity_1 = require("./entities/user.entity");
const auth_JwtAuthGuard_1 = require("../../auth/auth.JwtAuthGuard");
let UserController = class UserController {
    constructor(userService) {
        this.userService = userService;
    }
    async create(user, req) {
        const currentUserRole = req.user.roles;
        const roleMapping = {
            普通用户: role_enum_1.Role.User,
            管理员: role_enum_1.Role.Admin,
            超级管理员: role_enum_1.Role.SuperAdmin,
        };
        if (roleMapping[user.roles]) {
            user.roles = roleMapping[user.roles];
        }
        if (user.roles === role_enum_1.Role.Admin || user.roles === role_enum_1.Role.SuperAdmin) {
            if (currentUserRole !== role_enum_1.Role.SuperAdmin) {
                return {
                    code: 403,
                    msg: '只有超级管理员可以创建管理员账号',
                };
            }
        }
        const saltOrRounds = '10';
        const pass = user.password + saltOrRounds;
        user.password = md5_1.Md5.hashStr(pass);
        const createdUser = await this.userService.create(user);
        return {
            code: 0,
            data: createdUser,
            msg: '用户创建成功',
        };
    }
    async findAll(pageno, pagesize, username, roles, id) {
        const pageNo = pageno || 0;
        const pageSize = pagesize || 10;
        const result = await this.userService.findAll({
            pageNo,
            pageSize,
            username,
            roles,
            id,
        });
        return {
            code: 0,
            data: result,
            msg: '获取用户列表成功',
        };
    }
    async findOne(id) {
        const user = await this.userService.findOne(id);
        if (user) {
            delete user.password;
            return {
                code: 0,
                data: user,
                msg: '获取用户信息成功',
            };
        }
        return {
            code: 404,
            msg: '用户不存在',
        };
    }
    getRoleOptions(req) {
        const currentUserRole = req.user.roles;
        const baseOptions = [
            {
                value: role_enum_1.Role.User,
                label: '普通用户',
            },
        ];
        if (Array.isArray(currentUserRole)
            ? currentUserRole.includes(role_enum_1.Role.SuperAdmin)
            : currentUserRole === role_enum_1.Role.SuperAdmin) {
            baseOptions.push({
                value: role_enum_1.Role.Admin,
                label: '管理员',
            }, {
                value: role_enum_1.Role.SuperAdmin,
                label: '超级管理员',
            });
        }
        return {
            code: 0,
            data: baseOptions,
            msg: 'ok',
        };
    }
    update(id, updateUserDto) {
        const userInfo = this.userService.update(id, updateUserDto);
        return { code: 0, data: userInfo, msg: 'ok' };
    }
    selfupdate(req, updateUserDto) {
        const id = req.user.userId;
        console.log('id', id);
        console.log('updateUserDto', updateUserDto);
        if (updateUserDto.username)
            delete updateUserDto.username;
        if (updateUserDto.roles)
            delete updateUserDto.roles;
        const userInfo = this.userService.update(id, updateUserDto);
        return { code: 0, data: userInfo, msg: 'ok' };
    }
    remove(id) {
        const userInfo = this.userService.delete(id);
        return { code: 0, data: userInfo, msg: 'ok' };
    }
    count() {
        return this.userService.count();
    }
    async putupdate(id, updateUserDto) {
        const userInfo = await this.userService.update(id, updateUserDto);
        return { code: 0, data: userInfo, msg: 'ok' };
    }
    async cancellation(req) {
        const id = req.user.userId;
        return this.userService.cancellation(id);
    }
    async changePassword(req, passwordData) {
        const userId = req.user.userId;
        const saltOrRounds = '10';
        const oldPasswordHash = md5_1.Md5.hashStr(passwordData.oldPassword + saltOrRounds);
        const user = await this.userService.findOne(userId);
        if (!user || user.password !== oldPasswordHash) {
            throw new common_1.HttpException('旧密码不正确', common_1.HttpStatus.BAD_REQUEST);
        }
        const newPasswordHash = md5_1.Md5.hashStr(passwordData.newPassword + saltOrRounds);
        await this.userService.update(userId, {
            password: newPasswordHash,
        });
        return {
            code: 0,
            msg: '密码修改成功',
        };
    }
};
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.Admin, role_enum_1.Role.SuperAdmin),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(auth_JwtAuthGuard_1.JwtAuthGuard),
    (0, swagger_1.ApiOperation)({ summary: '后台添加用户' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User, Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "create", null);
__decorate([
    (0, common_1.Get)('page'),
    (0, swagger_1.ApiOperation)({
        summary: '分页显示用户列表,可带参数pageSize 每页多少条,pageNo 第几页,默认0页,10条',
    }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.Admin, role_enum_1.Role.SuperAdmin),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(auth_JwtAuthGuard_1.JwtAuthGuard),
    __param(0, (0, common_1.Query)('pageNo')),
    __param(1, (0, common_1.Query)('pageSize')),
    __param(2, (0, common_1.Query)('username')),
    __param(3, (0, common_1.Query)('roles')),
    __param(4, (0, common_1.Query)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object, Object, Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id/form'),
    (0, swagger_1.ApiOperation)({ summary: '根据ID获取用户信息' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.Admin, role_enum_1.Role.SuperAdmin),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(auth_JwtAuthGuard_1.JwtAuthGuard),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('options'),
    (0, swagger_1.ApiOperation)({ summary: '获取角色选项' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.Admin, role_enum_1.Role.SuperAdmin),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(auth_JwtAuthGuard_1.JwtAuthGuard),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], UserController.prototype, "getRoleOptions", null);
__decorate([
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.Admin, role_enum_1.Role.SuperAdmin),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(auth_JwtAuthGuard_1.JwtAuthGuard),
    (0, swagger_1.ApiOperation)({ summary: '更新用户信息' }),
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], UserController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(auth_JwtAuthGuard_1.JwtAuthGuard),
    (0, swagger_1.ApiOperation)({ summary: '更新当前用户信息,如修改密码' }),
    (0, common_1.Put)('update'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], UserController.prototype, "selfupdate", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '删除某条人员' }),
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.Admin, role_enum_1.Role.SuperAdmin),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(auth_JwtAuthGuard_1.JwtAuthGuard),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], UserController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)('count'),
    (0, swagger_1.ApiOperation)({ summary: '显示用户数量' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], UserController.prototype, "count", null);
__decorate([
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.Admin, role_enum_1.Role.SuperAdmin),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(auth_JwtAuthGuard_1.JwtAuthGuard),
    (0, swagger_1.ApiOperation)({ summary: 'put更新用户信息' }),
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "putupdate", null);
__decorate([
    (0, common_1.Get)('cancellation'),
    (0, swagger_1.ApiOperation)({ summary: '注销用户,带上验证头请求后从数据库真删除本用户' }),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(auth_JwtAuthGuard_1.JwtAuthGuard),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "cancellation", null);
__decorate([
    (0, common_1.Post)('change-password'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(auth_JwtAuthGuard_1.JwtAuthGuard),
    (0, swagger_1.ApiOperation)({ summary: '修改用户自己的密码' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "changePassword", null);
UserController = __decorate([
    (0, swagger_1.ApiTags)('账户管理'),
    (0, common_1.Controller)('user'),
    __metadata("design:paramtypes", [user_service_1.UserService])
], UserController);
exports.UserController = UserController;
//# sourceMappingURL=user.controller.js.map