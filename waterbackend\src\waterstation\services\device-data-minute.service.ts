import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, MoreThan, LessThan } from 'typeorm';
import { DeviceDataMinute } from '../entities/device-data-minute.entity';

@Injectable()
export class DeviceDataMinuteService {
  constructor(
    @InjectRepository(DeviceDataMinute)
    private readonly deviceDataMinuteRepository: Repository<DeviceDataMinute>,
  ) {}

  async findAll(): Promise<DeviceDataMinute[]> {
    return this.deviceDataMinuteRepository.find({
      order: { timestamp: 'DESC' }
    });
  }

  async findByDeviceSn(deviceSn: string): Promise<DeviceDataMinute[]> {
    return this.deviceDataMinuteRepository.find({
      where: { deviceSn },
      order: { timestamp: 'DESC' }
    });
  }

  async findByTimeRange(
    startTime: Date, 
    endTime: Date, 
    deviceSn?: string
  ): Promise<DeviceDataMinute[]> {
    const where: any = {
      timestamp: Between(startTime, endTime)
    };
    
    if (deviceSn) {
      where.deviceSn = deviceSn;
    }

    return this.deviceDataMinuteRepository.find({
      where,
      order: { timestamp: 'ASC' }
    });
  }

  async getLatestData(deviceSn?: string): Promise<DeviceDataMinute[]> {
    const where: any = {};
    if (deviceSn) {
      where.deviceSn = deviceSn;
    }

    return this.deviceDataMinuteRepository.find({
      where,
      order: { timestamp: 'DESC' },
      take: 100
    });
  }

  async getDataStatistics(deviceSn?: string) {
    const where: any = {};
    if (deviceSn) {
      where.deviceSn = deviceSn;
    }

    const [count, avgSampleCount] = await Promise.all([
      this.deviceDataMinuteRepository.count({ where }),
      this.deviceDataMinuteRepository
        .createQueryBuilder('data')
        .select('AVG(data.sample_count)', 'avg')
        .where(deviceSn ? 'data.device_sn = :deviceSn' : '1=1', deviceSn ? { deviceSn } : {})
        .getRawOne()
    ]);

    return {
      totalRecords: count,
      averageSampleCount: parseFloat(avgSampleCount.avg || '0')
    };
  }
}