<template>
  <div class="water-quality-test-plan">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <el-button type="primary" @click="handleCreatePlan"
            >制定计划</el-button
          >
        </div>
      </template>

      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="计划名称">
          <el-input
            v-model="searchForm.planName"
            placeholder="请输入计划名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="检测类型">
          <el-select
            v-model="searchForm.testType"
            placeholder="请选择类型"
            clearable
          >
            <el-option label="常规检测" value="routine" />
            <el-option label="全面检测" value="comprehensive" />
            <el-option label="专项检测" value="special" />
          </el-select>
        </el-form-item>
        <el-form-item label="计划状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="草稿" value="draft" />
            <el-option label="执行中" value="active" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="planData" stripe>
        <el-table-column prop="planNo" label="计划编号" width="120" />
        <el-table-column prop="planName" label="计划名称" />
        <el-table-column prop="testType" label="检测类型" width="120">
          <template #default="{ row }">
            {{ getTestTypeText(row.testType) }}
          </template>
        </el-table-column>
        <el-table-column prop="frequency" label="检测频率" width="120" />
        <el-table-column prop="startDate" label="开始时间" width="120" />
        <el-table-column prop="endDate" label="结束时间" width="120" />
        <el-table-column prop="monitorPoints" label="监测点数" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">{{
              getStatusText(row.status)
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="创建人" width="100" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)"
              >查看</el-button
            >
            <el-button type="success" size="small" @click="handleEdit(row)"
              >编辑</el-button
            >
            <el-button type="danger" size="small" @click="handleDelete(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";

const searchForm = reactive({
  planName: "",
  testType: "",
  status: "",
});

const planData = ref([
  {
    id: 1,
    planNo: "TP202401001",
    planName: "2024年第一季度水质检测计划",
    testType: "routine",
    frequency: "每日",
    startDate: "2024-01-01",
    endDate: "2024-03-31",
    monitorPoints: 8,
    status: "active",
    creator: "管理员",
  },
]);

const getTestTypeText = (type: string) => {
  const texts: Record<string, string> = {
    routine: "常规检测",
    comprehensive: "全面检测",
    special: "专项检测",
  };
  return texts[type] || "其他";
};

const getStatusType = (status: string): "success" | "primary" | "warning" | "info" | "danger" => {
  const types: Record<string, "success" | "primary" | "warning" | "info" | "danger"> = { draft: "info", active: "success", completed: "warning" };
  return types[status] || "info";
};

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    draft: "草稿",
    active: "执行中",
    completed: "已完成",
  };
  return texts[status] || "未知";
};

const handleCreatePlan = () => console.log("制定计划");
const handleSearch = () => console.log("搜索", searchForm);
const handleReset = () =>
  Object.assign(searchForm, { planName: "", testType: "", status: "" });
const handleView = (row: any) => console.log("查看", row);
const handleEdit = (row: any) => console.log("编辑", row);
const handleDelete = (row: any) => console.log("删除", row);

onMounted(() => {
  // 初始化
});
</script>

<style scoped>
.water-quality-test-plan {
  padding: 20px;
}

.page-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}
</style>
