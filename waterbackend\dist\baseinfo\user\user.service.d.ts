import { DeleteResult, Repository, UpdateResult } from 'typeorm';
import { User } from './entities/user.entity';
import { UpdateUserDto } from './dto/update-user.dto';
import { JwtService } from '@nestjs/jwt';
export declare class UserService {
    private readonly jwtService;
    private usersRepository;
    constructor(jwtService: JwtService, usersRepository: Repository<User>);
    create(user: User): Promise<User>;
    findUser(username: any): Promise<User>;
    findOne(id: any): Promise<User>;
    findAll({ pageNo, pageSize, username, roles, id }: {
        pageNo: any;
        pageSize: any;
        username: any;
        roles: any;
        id: any;
    }): Promise<any>;
    update(id: string, user: UpdateUserDto): Promise<UpdateResult>;
    delete(id: any): Promise<DeleteResult>;
    getuserbyopenid(openid: any): Promise<any>;
    count(): Promise<number>;
    openid(id: any, openid: string): Promise<UpdateResult>;
    cancellation(id: any): Promise<UpdateResult>;
    clearopenid(id: any): Promise<UpdateResult>;
}
