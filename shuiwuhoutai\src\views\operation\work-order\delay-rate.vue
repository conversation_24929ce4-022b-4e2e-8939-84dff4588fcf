<template>
  <div class="work-order-delay-rate">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <el-button @click="handleRefresh">刷新数据</el-button>
        </div>
      </template>

      <!-- 统计概览 -->
      <el-row :gutter="20" class="overview-row">
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-item">
              <div class="overview-value total">
                {{ delayStats.totalOrders }}
              </div>
              <div class="overview-label">总工单数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-item">
              <div class="overview-value delayed">
                {{ delayStats.delayedOrders }}
              </div>
              <div class="overview-label">延期工单</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-item">
              <div class="overview-value rate">{{ delayStats.delayRate }}%</div>
              <div class="overview-label">延期率</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-item">
              <div class="overview-value avg">
                {{ delayStats.avgDelayDays }}天
              </div>
              <div class="overview-label">平均延期天数</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 筛选条件 -->
      <el-form :model="filterForm" :inline="true" class="filter-form">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item label="工单类型">
          <el-select
            v-model="filterForm.type"
            placeholder="请选择类型"
            clearable
          >
            <el-option label="设备维修" value="equipment" />
            <el-option label="管道维护" value="pipeline" />
            <el-option label="水质检测" value="quality" />
          </el-select>
        </el-form-item>
        <el-form-item label="延期状态">
          <el-select
            v-model="filterForm.delayStatus"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="正常" value="normal" />
            <el-option label="轻微延期" value="slight" />
            <el-option label="严重延期" value="severe" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 延期率趋势图 -->
      <el-card class="chart-card">
        <template #header>
          <span>延期率趋势分析</span>
        </template>
        <div id="delayTrendChart" class="chart-container">
          延期率趋势图表区域
        </div>
      </el-card>

      <!-- 延期工单列表 -->
      <el-card class="table-card">
        <template #header>
          <div class="table-header">
            <span>延期工单详情</span>
            <el-button type="primary" @click="handleExport">导出数据</el-button>
          </div>
        </template>
        <el-table :data="delayedOrders" stripe>
          <el-table-column prop="orderNo" label="工单编号" width="120" />
          <el-table-column prop="title" label="工单标题" />
          <el-table-column prop="type" label="类型" width="100">
            <template #default="{ row }">
              {{ getTypeText(row.type) }}
            </template>
          </el-table-column>
          <el-table-column prop="assignee" label="处理人" width="100" />
          <el-table-column
            prop="expectedDate"
            label="预期完成时间"
            width="150"
          />
          <el-table-column prop="actualDate" label="实际完成时间" width="150">
            <template #default="{ row }">
              {{ row.actualDate || "未完成" }}
            </template>
          </el-table-column>
          <el-table-column prop="delayDays" label="延期天数" width="100">
            <template #default="{ row }">
              <el-tag :type="getDelayType(row.delayDays)">
                {{ row.delayDays }}天
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="delayReason" label="延期原因" />
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleView(row)"
                >查看</el-button
              >
              <el-button type="warning" size="small" @click="handleUrge(row)"
                >催办</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-card>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";

// 延期统计数据
const delayStats = reactive({
  totalOrders: 256,
  delayedOrders: 38,
  delayRate: 14.8,
  avgDelayDays: 3.2,
});

// 筛选表单
const filterForm = reactive({
  dateRange: [] as string[],
  type: "",
  delayStatus: "",
});

// 延期工单数据
const delayedOrders = ref([
  {
    id: 1,
    orderNo: "WO202401001",
    title: "1号泵站设备维修",
    type: "equipment",
    assignee: "张三",
    expectedDate: "2024-01-05",
    actualDate: "2024-01-08",
    delayDays: 3,
    delayReason: "配件采购延误",
  },
  {
    id: 2,
    orderNo: "WO202401002",
    title: "主管道漏水处理",
    type: "pipeline",
    assignee: "李四",
    expectedDate: "2024-01-03",
    actualDate: null,
    delayDays: 5,
    delayReason: "天气原因无法施工",
  },
]);

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0,
});

// 获取类型文本
const getTypeText = (type: string) => {
  const texts: Record<string, string> = {
    equipment: "设备维修",
    pipeline: "管道维护",
    quality: "水质检测",
  };
  return texts[type] || "其他";
};

// 获取延期类型
const getDelayType = (
  days: number
): "success" | "primary" | "warning" | "info" | "danger" => {
  if (days <= 1) return "success";
  if (days <= 3) return "warning";
  return "danger";
};

// 日期改变
const handleDateChange = (dates: any) => {
  console.log("日期范围改变", dates);
  loadDelayData();
};

// 搜索
const handleSearch = () => {
  console.log("搜索", filterForm);
  loadDelayData();
};

// 重置
const handleReset = () => {
  Object.assign(filterForm, {
    dateRange: [],
    type: "",
    delayStatus: "",
  });
  loadDelayData();
};

// 刷新数据
const handleRefresh = () => {
  loadDelayData();
};

// 导出数据
const handleExport = () => {
  console.log("导出延期工单数据");
};

// 查看工单
const handleView = (row: any) => {
  console.log("查看工单", row);
};

// 催办工单
const handleUrge = (row: any) => {
  console.log("催办工单", row);
};

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size;
  loadDelayData();
};

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page;
  loadDelayData();
};

// 加载延期数据
const loadDelayData = () => {
  console.log("加载延期数据");
  pagination.total = delayedOrders.value.length;
};

onMounted(() => {
  // 设置默认时间范围（最近30天）
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - 29);

  filterForm.dateRange = [
    startDate.toISOString().split("T")[0],
    endDate.toISOString().split("T")[0],
  ];

  loadDelayData();
});
</script>

<style scoped>
.work-order-delay-rate {
  padding: 20px;
}

.page-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.overview-row {
  margin-bottom: 20px;
}

.overview-card {
  text-align: center;
  border-radius: 8px;
}

.overview-item {
  padding: 20px;
}

.overview-value {
  font-size: 32px;
  font-weight: bold;
  line-height: 1;
}

.overview-value.total {
  color: #409eff;
}

.overview-value.delayed {
  color: #f56c6c;
}

.overview-value.rate {
  color: #e6a23c;
}

.overview-value.avg {
  color: #909399;
}

.overview-label {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.filter-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  border-radius: 4px;
  color: #999;
}

.table-card {
  margin-top: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
