import { Controller, Get, Query, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiQuery, ApiParam } from '@nestjs/swagger';
import { DeviceDataHourService } from '../services/device-data-hour.service';
import { DeviceDataHour } from '../entities/device-data-hour.entity';

@ApiTags('水利站小时级数据')
@Controller('device-data-hour')
export class DeviceDataHourController {
  constructor(
    private readonly deviceDataHourService: DeviceDataHourService
  ) {}

  @Get()
  @ApiOperation({ summary: '获取所有小时级设备数据' })
  @ApiQuery({ name: 'deviceSn', required: false, description: '设备序列号' })
  @ApiQuery({ name: 'startTime', required: false, description: '开始时间 ISO格式' })
  @ApiQuery({ name: 'endTime', required: false, description: '结束时间 ISO格式' })
  async findAll(
    @Query('deviceSn') deviceSn?: string,
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string
  ): Promise<DeviceDataHour[]> {
    if (startTime && endTime) {
      return this.deviceDataHourService.findByTimeRange(
        new Date(startTime), 
        new Date(endTime), 
        deviceSn
      );
    }

    if (deviceSn) {
      return this.deviceDataHourService.findByDeviceSn(deviceSn);
    }

    return this.deviceDataHourService.findAll();
  }

  @Get('latest')
  @ApiOperation({ summary: '获取最新的小时级数据' })
  @ApiQuery({ name: 'deviceSn', required: false, description: '设备序列号' })
  async getLatestData(
    @Query('deviceSn') deviceSn?: string
  ): Promise<DeviceDataHour[]> {
    return this.deviceDataHourService.getLatestData(deviceSn);
  }

  @Get('statistics')
  @ApiOperation({ summary: '获取小时级数据统计信息' })
  @ApiQuery({ name: 'deviceSn', required: false, description: '设备序列号' })
  async getStatistics(@Query('deviceSn') deviceSn?: string) {
    return this.deviceDataHourService.getDataStatistics(deviceSn);
  }

  @Get(':deviceSn')
  @ApiOperation({ summary: '根据设备序列号获取小时级数据' })
  @ApiParam({ name: 'deviceSn', description: '设备序列号' })
  async findByDeviceSn(
    @Param('deviceSn') deviceSn: string
  ): Promise<DeviceDataHour[]> {
    return this.deviceDataHourService.findByDeviceSn(deviceSn);
  }
}