# /f:/水利站/backend/data_downsampling_service.py
import logging
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from collections import defaultdict
import statistics
import schedule

from mongodb_models import get_mongodb_manager, MongoDBManager
from mysql_models import get_mysql_manager, MySQLManager

logger = logging.getLogger(__name__)

class DataDownsamplingService:
    """
    数据降采样服务
    负责将MongoDB中的5秒级数据降采样为分钟级数据并存储到MySQL
    """
    
    def __init__(self):
        self.mongodb_manager = get_mongodb_manager()
        self.mysql_manager = get_mysql_manager()
        self.is_running = False
        self._stop_event = threading.Event()
        self._thread = None
        
    def start(self):
        """启动降采样服务"""
        if self.is_running:
            logger.warning("数据降采样服务已在运行")
            return
        
        self.is_running = True
        self._stop_event.clear()
        
        # 设置定时任务：每分钟执行一次降采样
        schedule.every().minute.at(":05").do(self._run_downsampling_job)
        
        # 启动后台线程
        self._thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self._thread.start()
        
        logger.info("数据降采样服务已启动")
    
    def stop(self):
        """停止降采样服务"""
        if not self.is_running:
            return
            
        self.is_running = False
        self._stop_event.set()
        
        if self._thread and self._thread.is_alive():
            self._thread.join(timeout=30)
        
        schedule.clear()
        logger.info("数据降采样服务已停止")
    
    def _scheduler_loop(self):
        """调度器循环"""
        while self.is_running and not self._stop_event.is_set():
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                logger.error(f"调度器循环异常: {e}")
                time.sleep(5)
    
    def _run_downsampling_job(self):
        """执行降采样任务"""
        try:
            # 计算上一分钟的时间范围
            now = datetime.utcnow()
            end_time = now.replace(second=0, microsecond=0)  # 当前分钟的开始
            start_time = end_time - timedelta(minutes=1)     # 上一分钟的开始
            
            logger.info(f"开始降采样任务: {start_time} 到 {end_time}")
            
            # 获取所有设备的数据并进行降采样
            processed_count = self._process_minute_downsampling(start_time, end_time)
            
            if processed_count > 0:
                logger.info(f"降采样任务完成，处理了 {processed_count} 个设备的数据")
            
        except Exception as e:
            logger.error(f"降采样任务执行失败: {e}")
    
    def _process_minute_downsampling(self, start_time: datetime, end_time: datetime) -> int:
        """
        处理分钟级降采样
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            int: 处理的设备数量
        """
        # 从MongoDB获取指定时间范围的数据
        raw_data = self.mongodb_manager.get_device_data_for_downsampling(start_time, end_time)
        
        if not raw_data:
            logger.debug(f"时间范围 {start_time} 到 {end_time} 内没有数据")
            return 0
        
        # 按设备分组数据
        device_groups = defaultdict(list)
        for record in raw_data:
            device_groups[record["device_sn"]].append(record)
        
        processed_devices = 0
        
        # 为每个设备计算降采样数据
        for device_sn, device_records in device_groups.items():
            try:
                downsampled_data = self._calculate_minute_aggregates(device_records)
                
                if downsampled_data:
                    # 保存降采样数据到MySQL
                    success = self._save_minute_data(
                        device_sn=device_sn,
                        timestamp=start_time,  # 使用分钟开始时间作为时间戳
                        **downsampled_data
                    )
                    
                    if success:
                        processed_devices += 1
                        logger.debug(f"设备 {device_sn} 降采样数据已保存")
                    
            except Exception as e:
                logger.error(f"设备 {device_sn} 降采样失败: {e}")
        
        # 删除已处理的原始数据
        if processed_devices > 0:
            try:
                deleted_count = self.mongodb_manager.delete_processed_data(end_time)
                logger.debug(f"已删除 {deleted_count} 条已处理的原始数据")
            except Exception as e:
                logger.error(f"删除已处理数据失败: {e}")
        
        return processed_devices
    
    def _calculate_minute_aggregates(self, records: List[Dict]) -> Optional[Dict]:
        """
        计算分钟级聚合数据
        
        Args:
            records: 5秒级数据记录列表
            
        Returns:
            Dict: 包含avg_data, max_data, min_data, sample_count的字典
        """
        if not records:
            return None
        
        try:
            # 提取所有数值字段
            numeric_fields = self._extract_numeric_fields(records)
            
            if not numeric_fields:
                logger.warning("没有找到可聚合的数值字段")
                return None
            
            # 计算统计数据
            avg_data = {}
            max_data = {}
            min_data = {}
            
            for field_name, values in numeric_fields.items():
                if values:  # 确保有值
                    avg_data[field_name] = round(statistics.mean(values), 4)
                    max_data[field_name] = max(values)
                    min_data[field_name] = min(values)
            
            return {
                "avg_data": avg_data,
                "max_data": max_data,
                "min_data": min_data,
                "sample_count": len(records)
            }
            
        except Exception as e:
            logger.error(f"计算聚合数据失败: {e}")
            return None
    
    def _extract_numeric_fields(self, records: List[Dict]) -> Dict[str, List[float]]:
        """
        从记录中提取数值字段
        
        Args:
            records: 数据记录列表
            
        Returns:
            Dict[str, List[float]]: 字段名到数值列表的映射
        """
        numeric_fields = defaultdict(list)
        
        for record in records:
            raw_data = record.get("raw_data", {})
            self._extract_numeric_from_dict(raw_data, numeric_fields)
        
        return dict(numeric_fields)
    
    def _extract_numeric_from_dict(self, data: Dict, field_collector: Dict[str, List[float]], prefix: str = ""):
        """
        递归提取字典中的数值字段
        
        Args:
            data: 数据字典
            field_collector: 字段收集器
            prefix: 字段名前缀
        """
        for key, value in data.items():
            field_name = f"{prefix}.{key}" if prefix else key
            
            if isinstance(value, (int, float)):
                field_collector[field_name].append(float(value))
            elif isinstance(value, dict):
                # 递归处理嵌套字典
                self._extract_numeric_from_dict(value, field_collector, field_name)
            elif isinstance(value, list):
                # 处理数值数组（如果需要的话）
                for i, item in enumerate(value):
                    if isinstance(item, (int, float)):
                        field_collector[f"{field_name}[{i}]"].append(float(item))
    
    def _save_minute_data(self, device_sn: str, timestamp: datetime, avg_data: Dict,
                         max_data: Dict, min_data: Dict, sample_count: int) -> bool:
        """
        保存分钟级数据到MySQL
        
        Args:
            device_sn: 设备序列号
            timestamp: 时间戳
            avg_data: 平均值数据
            max_data: 最大值数据
            min_data: 最小值数据
            sample_count: 样本数量
            
        Returns:
            bool: 保存是否成功
        """
        try:
            record_id = self.mysql_manager.insert_minute_data(
                device_sn=device_sn,
                timestamp=timestamp,
                avg_data=avg_data,
                max_data=max_data,
                min_data=min_data,
                sample_count=sample_count
            )
            
            return record_id is not None
            
        except Exception as e:
            logger.error(f"保存分钟级数据失败: {e}")
            return False
    
    def manual_downsample_range(self, start_time: datetime, end_time: datetime) -> Dict[str, int]:
        """
        手动执行指定时间范围的降采样
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            Dict[str, int]: 处理结果统计
        """
        logger.info(f"开始手动降采样: {start_time} 到 {end_time}")
        
        processed_devices = 0
        processed_minutes = 0
        
        # 按分钟处理
        current_time = start_time.replace(second=0, microsecond=0)
        
        while current_time < end_time:
            minute_end = current_time + timedelta(minutes=1)
            
            devices_count = self._process_minute_downsampling(current_time, minute_end)
            
            if devices_count > 0:
                processed_devices += devices_count
                processed_minutes += 1
            
            current_time = minute_end
        
        result = {
            "processed_devices": processed_devices,
            "processed_minutes": processed_minutes
        }
        
        logger.info(f"手动降采样完成: {result}")
        return result


class HourlyArchiveService:
    """
    小时级数据归档服务
    负责将分钟级数据进一步聚合为小时级数据，并删除超过7天的分钟级数据
    """
    
    def __init__(self):
        self.mysql_manager = get_mysql_manager()
        self.is_running = False
        self._stop_event = threading.Event()
        self._thread = None
    
    def start(self):
        """启动小时级归档服务"""
        if self.is_running:
            logger.warning("小时级归档服务已在运行")
            return
        
        self.is_running = True
        self._stop_event.clear()
        
        # 设置定时任务：每小时执行一次归档
        schedule.every().hour.at(":05").do(self._run_hourly_archive_job)
        
        # 设置定时任务：每天清理一次过期数据
        schedule.every().day.at("02:00").do(self._run_cleanup_job)
        
        # 启动后台线程
        self._thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self._thread.start()
        
        logger.info("小时级归档服务已启动")
    
    def stop(self):
        """停止归档服务"""
        if not self.is_running:
            return
            
        self.is_running = False
        self._stop_event.set()
        
        if self._thread and self._thread.is_alive():
            self._thread.join(timeout=30)
        
        schedule.clear()
        logger.info("小时级归档服务已停止")
    
    def _scheduler_loop(self):
        """调度器循环"""
        while self.is_running and not self._stop_event.is_set():
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                logger.error(f"归档服务调度器循环异常: {e}")
                time.sleep(5)
    
    def _run_hourly_archive_job(self):
        """执行小时级归档任务"""
        try:
            # 计算上一小时的时间范围
            now = datetime.utcnow()
            end_time = now.replace(minute=0, second=0, microsecond=0)  # 当前小时开始
            start_time = end_time - timedelta(hours=1)                 # 上一小时开始
            
            logger.info(f"开始小时级归档任务: {start_time} 到 {end_time}")
            
            # 处理小时级聚合
            processed_count = self._process_hourly_aggregation(start_time, end_time)
            
            if processed_count > 0:
                logger.info(f"小时级归档任务完成，处理了 {processed_count} 个设备的数据")
            
        except Exception as e:
            logger.error(f"小时级归档任务执行失败: {e}")
    
    def _run_cleanup_job(self):
        """执行清理任务，删除超过7天的分钟级数据"""
        try:
            cutoff_time = datetime.utcnow() - timedelta(days=7)
            deleted_count = self.mysql_manager.delete_old_minute_data(cutoff_time)
            
            if deleted_count > 0:
                logger.info(f"清理任务完成，删除了 {deleted_count} 条过期的分钟级数据")
            
        except Exception as e:
            logger.error(f"清理任务执行失败: {e}")
    
    def _process_hourly_aggregation(self, start_time: datetime, end_time: datetime) -> int:
        """
        处理小时级聚合
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            int: 处理的设备数量
        """
        # 获取分钟级数据
        minute_records = self.mysql_manager.get_minute_data_for_hour_aggregation(start_time, end_time)
        
        if not minute_records:
            logger.debug(f"时间范围 {start_time} 到 {end_time} 内没有分钟级数据")
            return 0
        
        # 按设备分组
        device_groups = defaultdict(list)
        for record in minute_records:
            device_groups[record.device_sn].append(record)
        
        processed_devices = 0
        
        # 为每个设备计算小时级聚合
        for device_sn, device_records in device_groups.items():
            try:
                hour_data = self._calculate_hourly_aggregates(device_records)
                
                if hour_data:
                    # 保存小时级数据
                    success = self._save_hourly_data(
                        device_sn=device_sn,
                        timestamp=start_time,  # 使用小时开始时间作为时间戳
                        **hour_data
                    )
                    
                    if success:
                        processed_devices += 1
                        logger.debug(f"设备 {device_sn} 小时级数据已保存")
                    
            except Exception as e:
                logger.error(f"设备 {device_sn} 小时级聚合失败: {e}")
        
        return processed_devices
    
    def _calculate_hourly_aggregates(self, minute_records: List) -> Optional[Dict]:
        """
        计算小时级聚合数据
        
        Args:
            minute_records: 分钟级记录列表
            
        Returns:
            Dict: 包含聚合数据的字典
        """
        if not minute_records:
            return None
        
        try:
            # 收集所有字段的数据
            avg_fields = defaultdict(list)
            max_fields = defaultdict(list)
            min_fields = defaultdict(list)
            
            total_sample_count = 0
            minute_sample_count = len(minute_records)
            
            for record in minute_records:
                total_sample_count += record.sample_count
                
                # 收集平均值数据用于重新计算加权平均
                for field, value in record.avg_data.items():
                    avg_fields[field].append((value, record.sample_count))
                
                # 收集最大值和最小值
                for field, value in record.max_data.items():
                    max_fields[field].append(value)
                
                for field, value in record.min_data.items():
                    min_fields[field].append(value)
            
            # 计算加权平均值
            hour_avg = {}
            for field, value_weight_pairs in avg_fields.items():
                weighted_sum = sum(value * weight for value, weight in value_weight_pairs)
                total_weight = sum(weight for _, weight in value_weight_pairs)
                if total_weight > 0:
                    hour_avg[field] = round(weighted_sum / total_weight, 4)
            
            # 计算最大值和最小值
            hour_max = {field: max(values) for field, values in max_fields.items() if values}
            hour_min = {field: min(values) for field, values in min_fields.items() if values}
            
            return {
                "avg_data": hour_avg,
                "max_data": hour_max,
                "min_data": hour_min,
                "minute_sample_count": minute_sample_count,
                "original_sample_count": total_sample_count
            }
            
        except Exception as e:
            logger.error(f"计算小时级聚合数据失败: {e}")
            return None
    
    def _save_hourly_data(self, device_sn: str, timestamp: datetime, avg_data: Dict,
                         max_data: Dict, min_data: Dict, minute_sample_count: int,
                         original_sample_count: int) -> bool:
        """
        保存小时级数据到MySQL
        
        Args:
            device_sn: 设备序列号
            timestamp: 时间戳
            avg_data: 平均值数据
            max_data: 最大值数据
            min_data: 最小值数据
            minute_sample_count: 分钟级样本数量
            original_sample_count: 原始样本总数
            
        Returns:
            bool: 保存是否成功
        """
        try:
            record_id = self.mysql_manager.insert_hour_data(
                device_sn=device_sn,
                timestamp=timestamp,
                avg_data=avg_data,
                max_data=max_data,
                min_data=min_data,
                minute_sample_count=minute_sample_count,
                original_sample_count=original_sample_count
            )
            
            return record_id is not None
            
        except Exception as e:
            logger.error(f"保存小时级数据失败: {e}")
            return False


# 全局服务实例
downsampling_service = None
archive_service = None

def start_data_services():
    """启动数据服务"""
    global downsampling_service, archive_service
    
    if downsampling_service is None:
        downsampling_service = DataDownsamplingService()
    
    if archive_service is None:
        archive_service = HourlyArchiveService()
    
    downsampling_service.start()
    archive_service.start()
    
    logger.info("数据降采样和归档服务已启动")

def stop_data_services():
    """停止数据服务"""
    global downsampling_service, archive_service
    
    if downsampling_service:
        downsampling_service.stop()
    
    if archive_service:
        archive_service.stop()
    
    logger.info("数据降采样和归档服务已停止")

def get_downsampling_service() -> Optional[DataDownsamplingService]:
    """获取降采样服务实例"""
    return downsampling_service

def get_archive_service() -> Optional[HourlyArchiveService]:
    """获取归档服务实例"""
    return archive_service