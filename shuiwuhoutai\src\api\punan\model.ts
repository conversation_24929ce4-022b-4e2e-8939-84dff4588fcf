/**
 * 浦南基本信息查询对象类型
 */
export interface PunanBasicInfoQuery extends PageQuery {
  keywords?: string;
  village?: string;
  startTime?: string;
  endTime?: string;
}

/**
 * 浦南基本信息分页对象
 */
export interface PunanBasicInfoPageVO {
  /**
   * ID
   */
  id: number;
  /**
   * 村
   */
  village: string;
  /**
   * 农户数
   */
  farmHouseholds: number;
  /**
   * 人口
   */
  population: number;
  /**
   * 自然村个数
   */
  naturalVillageCount: number;
  /**
   * 其中三星康居及特田
   */
  threeStarKangju: number;
  /**
   * 2021年稳定性收入万元
   */
  stableIncome2021: number;
  /**
   * 耕地总面积
   */
  totalCultivatedArea: number;
  /**
   * 水稻田总面积
   */
  ricePaddyTotalArea: number;
  /**
   * 水稻田其中已公司名义承包面积
   */
  ricePaddyCompanyContractArea: number;
  /**
   * 水稻田公司承包个数
   */
  ricePaddyCompanyContractCount: number;
  /**
   * 果园面积
   */
  orchardArea: number;
  /**
   * 果园经营品种
   */
  orchardVarieties: string;
  /**
   * 蔬菜地面积
   */
  vegetableArea: number;
  /**
   * 蔬菜地经营品种
   */
  vegetableVarieties: string;
  /**
   * 外荡水面养殖面积
   */
  aquacultureArea: number;
  /**
   * 专业合作社个数
   */
  professionalCooperativeCount: number;
  /**
   * 社区股份合作社
   */
  communityShareCooperativeCount: number;
  /**
   * 家庭农场个数
   */
  familyFarmCount: number;
}

/**
 * 浦南基本信息表单类型
 */
export interface PunanBasicInfoForm {
  /**
   * ID
   */
  id?: number | null;
  /**
   * 村
   */
  village: string;
  /**
   * 农户数
   */
  farmHouseholds: number;
  /**
   * 人口
   */
  population: number;
  /**
   * 自然村个数
   */
  naturalVillageCount: number;
  /**
   * 其中三星康居及特田
   */
  threeStarKangju: number;
  /**
   * 2021年稳定性收入万元
   */
  stableIncome2021: number;
  /**
   * 耕地总面积
   */
  totalCultivatedArea: number;
  /**
   * 水稻田总面积
   */
  ricePaddyTotalArea: number;
  /**
   * 水稻田其中已公司名义承包面积
   */
  ricePaddyCompanyContractArea: number;
  /**
   * 水稻田公司承包个数
   */
  ricePaddyCompanyContractCount: number;
  /**
   * 果园面积
   */
  orchardArea: number;
  /**
   * 果园经营品种
   */
  orchardVarieties: string;
  /**
   * 蔬菜地面积
   */
  vegetableArea: number;
  /**
   * 蔬菜地经营品种
   */
  vegetableVarieties: string;
  /**
   * 外荡水面养殖面积
   */
  aquacultureArea: number;
  /**
   * 专业合作社个数
   */
  professionalCooperativeCount: number;
  /**
   * 社区股份合作社
   */
  communityShareCooperativeCount: number;
  /**
   * 家庭农场个数
   */
  familyFarmCount: number;
}

/**
 * 浦南企业信息查询对象类型
 */
export interface PunanEnterpriseInfoQuery extends PageQuery {
  keywords?: string;
  name?: string;
  type?: string;
  startTime?: string;
  endTime?: string;
}

/**
 * 浦南企业信息分页对象
 */
export interface PunanEnterpriseInfoPageVO {
  /**
   * ID
   */
  id: number;
  /**
   * 企业名称
   */
  name: string;
  /**
   * 企业位置
   */
  location: string;
  /**
   * 企业经纬度坐标
   */
  coordinates: string;
  /**
   * 企业类型
   */
  type: string;
  /**
   * 企业规模
   */
  scale: string;
  /**
   * 企业文字性介绍
   */
  description: string;
}

/**
 * 浦南企业信息表单类型
 */
export interface PunanEnterpriseInfoForm {
  /**
   * ID
   */
  id?: number | null;
  /**
   * 企业名称
   */
  name: string;
  /**
   * 企业位置
   */
  location: string;
  /**
   * 企业经纬度坐标
   */
  coordinates: string;
  /**
   * 企业类型
   */
  type: string;
  /**
   * 企业规模
   */
  scale: string;
  /**
   * 企业文字性介绍
   */
  description: string;
}

/**
 * 浦南耕地信息查询对象类型
 */
export interface PunanFarmlandInfoQuery extends PageQuery {
  keywords?: string;
  landName?: string;
  crop?: string;
  startTime?: string;
  endTime?: string;
}

/**
 * 浦南耕地信息分页对象
 */
export interface PunanFarmlandInfoPageVO {
  /**
   * ID
   */
  id: number;
  /**
   * 耕地所属地域
   */
  landName: string;
  /**
   * 地域经纬度
   */
  coordinates: string;
  /**
   * 耕地面积（单位：亩）
   */
  landArea: string;
  /**
   * 作物
   */
  crop: string;
  /**
   * 作物产量（单位：公斤）
   */
  cropYield: string;
}

/**
 * 浦南耕地信息表单类型
 */
export interface PunanFarmlandInfoForm {
  /**
   * ID
   */
  id?: number | null;
  /**
   * 耕地所属地域
   */
  landName: string;
  /**
   * 地域经纬度
   */
  coordinates: string;
  /**
   * 耕地面积（单位：亩）
   */
  landArea: string;
  /**
   * 作物
   */
  crop: string;
  /**
   * 作物产量（单位：公斤）
   */
  cropYield: string;
}

/**
 * 浦南果篮子信息查询对象类型
 */
export interface PunanFruitBasketInfoQuery extends PageQuery {
  keywords?: string;
  Area?: string;
  fruitName?: string;
  startTime?: string;
  endTime?: string;
}

/**
 * 浦南果篮子信息分页对象
 */
export interface PunanFruitBasketInfoPageVO {
  /**
   * ID
   */
  id: number;
  /**
   * 果园所属区域
   */
  Area: string;
  /**
   * 区域经纬度
   */
  coordinates: string;
  /**
   * 果园面积（亩）
   */
  fruitArea: string;
  /**
   * 水果名称
   */
  fruitName: string;
  /**
   * 水果总产量（公斤）
   */
  fruitYield: string;
}

/**
 * 浦南果篮子信息表单类型
 */
export interface PunanFruitBasketInfoForm {
  /**
   * ID
   */
  id?: number | null;
  /**
   * 果园所属区域
   */
  Area: string;
  /**
   * 区域经纬度
   */
  coordinates: string;
  /**
   * 果园面积（亩）
   */
  fruitArea: string;
  /**
   * 水果名称
   */
  fruitName: string;
  /**
   * 水果总产量（公斤）
   */
  fruitYield: string;
}

/**
 * 浦南菜篮子信息查询对象类型
 */
export interface PunanVegetableBasketInfoQuery extends PageQuery {
  keywords?: string;
  Area?: string;
  vegetableName?: string;
  startTime?: string;
  endTime?: string;
}

/**
 * 浦南菜篮子信息分页对象
 */
export interface PunanVegetableBasketInfoPageVO {
  /**
   * ID
   */
  id: number;
  /**
   * 蔬菜所属区域
   */
  Area: string;
  /**
   * 区域经纬度
   */
  coordinates: string;
  /**
   * 蔬菜基地面积（亩）
   */
  vegetableArea: string;
  /**
   * 种植蔬菜名字
   */
  vegetableName: string;
  /**
   * 蔬菜产量（公斤）
   */
  vegetableYield: string;
}

/**
 * 浦南菜篮子信息表单类型
 */
export interface PunanVegetableBasketInfoForm {
  /**
   * ID
   */
  id?: number | null;
  /**
   * 蔬菜所属区域
   */
  Area: string;
  /**
   * 区域经纬度
   */
  coordinates: string;
  /**
   * 蔬菜基地面积（亩）
   */
  vegetableArea: string;
  /**
   * 种植蔬菜名字
   */
  vegetableName: string;
  /**
   * 蔬菜产量（公斤）
   */
  vegetableYield: string;
}
