import { Modu<PERSON> } from '@nestjs/common';
import { LoginlogService } from './loginlog.service';
import { LoginlogController } from './loginlog.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Loginlog, LoginlogSchema } from './loginlog.schema';
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Loginlog.name, schema: LoginlogSchema },
    ]),
  ],
  controllers: [LoginlogController],
  providers: [LoginlogService],
})
export class LoginlogModule {}
