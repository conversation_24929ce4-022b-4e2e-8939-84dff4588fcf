import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { DeviceData5s, DeviceData5sDocument } from '../schemas/device-data-5s.schema';

export interface StationRuntimeInfo {
  deviceSn: string;
  date: string;
  theoreticalDataPoints: number;
  actualDataPoints: number;
  runningTimeSeconds: number;
  runningTimeFormatted: {
    hours: number;
    minutes: number;
    seconds: number;
  };
  uptimePercentage: number;
}

@Injectable()
export class StationRuntimeService {
  constructor(
    @InjectModel(DeviceData5s.name)
    private readonly deviceData5sModel: Model<DeviceData5sDocument>,
  ) {}

  /**
   * 计算站点当天运行时间
   * @param deviceSn 设备序列号，可选
   * @param targetDate 目标日期，可选，默认为今天
   */
  async calculateStationRuntime(
    deviceSn?: string, 
    targetDate?: string
  ): Promise<StationRuntimeInfo[]> {
    // 确定目标日期
    const date = targetDate ? new Date(targetDate) : new Date();
    const dateStr = date.toISOString().split('T')[0];
    
    // 计算当天的开始和结束时间
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    
    const now = new Date();
    const endTime = date.toDateString() === now.toDateString() ? now : new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000 - 1);
    
    // 计算从零时到现在的秒数
    const secondsSinceStartOfDay = Math.floor((endTime.getTime() - startOfDay.getTime()) / 1000);
    
    // 理论数据点数量 = 秒数 / 5
    const theoreticalDataPoints = Math.floor(secondsSinceStartOfDay / 5);

    let devices: string[] = [];
    
    if (deviceSn) {
      devices = [deviceSn];
    } else {
      // 获取当天有数据的所有设备
      devices = await this.deviceData5sModel.distinct('device_sn', {
        timestamp: { $gte: startOfDay, $lte: endTime }
      });
    }

    const results: StationRuntimeInfo[] = [];

    for (const device of devices) {
      // 统计当天实际接收到的数据数量
      const actualDataPoints = await this.deviceData5sModel.countDocuments({
        device_sn: device,
        timestamp: { $gte: startOfDay, $lte: endTime }
      });

      // 计算实际运行时间（秒）= 实际数据点数 * 5
      const runningTimeSeconds = actualDataPoints * 5;
      
      // 转换为小时和分钟
      const hours = Math.floor(runningTimeSeconds / 3600);
      const minutes = Math.floor((runningTimeSeconds % 3600) / 60);
      const seconds = runningTimeSeconds % 60;
      
      // 计算运行时间百分比
      const uptimePercentage = theoreticalDataPoints > 0 
        ? Math.round((actualDataPoints / theoreticalDataPoints) * 100 * 100) / 100 
        : 0;

      results.push({
        deviceSn: device,
        date: dateStr,
        theoreticalDataPoints,
        actualDataPoints,
        runningTimeSeconds,
        runningTimeFormatted: {
          hours,
          minutes,
          seconds
        },
        uptimePercentage
      });
    }

    return results;
  }

  /**
   * 获取指定时间段内的运行时间统计
   */
  async getStationRuntimeBetweenDates(
    startDate: string,
    endDate: string,
    deviceSn?: string
  ): Promise<StationRuntimeInfo[]> {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const results: StationRuntimeInfo[] = [];

    // 循环每一天
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      const dayResults = await this.calculateStationRuntime(
        deviceSn, 
        d.toISOString().split('T')[0]
      );
      results.push(...dayResults);
    }

    return results;
  }

  /**
   * 获取站点运行状态摘要
   */
  async getStationStatusSummary(deviceSn?: string) {
    const todayRuntime = await this.calculateStationRuntime(deviceSn);
    
    return {
      date: new Date().toISOString().split('T')[0],
      totalDevices: todayRuntime.length,
      averageUptimePercentage: todayRuntime.length > 0 
        ? Math.round(todayRuntime.reduce((sum, device) => sum + device.uptimePercentage, 0) / todayRuntime.length * 100) / 100
        : 0,
      devices: todayRuntime.map(device => ({
        deviceSn: device.deviceSn,
        runningTime: `${device.runningTimeFormatted.hours}小时${device.runningTimeFormatted.minutes}分钟`,
        uptimePercentage: device.uptimePercentage
      }))
    };
  }
}