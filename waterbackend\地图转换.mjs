// 标准经纬度转换成腾讯经纬度

import  axios  from  'axios';
let  type=2
export  function  wxConvert(lng,lat){
    let url=`https://apis.map.qq.com/ws/coord/v1/translate?locations=${lat},${lng}&type=${type}&key=DPKBZ-ISUK3-2DH3B-YEGSZ-AXOU5-V5F2F`
    // console.log(url)
    return  axios.get(url)
}

let gpsstr=`120.485428,31.440849
120.485613,31.440707
120.485401,31.441103
120.486399,31.441412
120.48696,31.441407
120.486726,31.441212
120.486677,31.441089
120.485496,31.439186
120.485496,31.439186
120.485312,31.438697
120.484602,31.438766
120.485608,31.438732
120.485864,31.438508
120.48453,31.438786
120.485981,31.438289
120.486039,31.438185
120.484611,31.4379
120.48594,31.440126
120.483524,31.452237
120.474078,31.446688
120.4742,31.446468
120.483668,31.440241
120.484818,31.439298
120.484283,31.439652
120.484831,31.439771
120.484719,31.435612
120.483241,31.4352
120.483838,31.435593
120.483838,31.435593
120.476648,31.446233
120.482001,31.445263
120.483955,31.445667
120.476329,31.439321
120.47548,31.43909
120.476895,31.438847
120.474941,31.445313
120.474941,31.445313
120.478776,31.442124
120.478776,31.442124
120.478776,31.442124
120.480281,31.438304
120.479684,31.438073
120.480286,31.437988
120.475287,31.442972
120.475129,31.442375
120.482001,31.437803
120.480816,31.440191
120.482289,31.437103
120.482662,31.439371
120.482226,31.438647
120.479468,31.43929
120.47839,31.439459
120.480631,31.436344
120.478992,31.434773
120.478426,31.435304
120.47601,31.435639
120.475821,31.435327
120.475897,31.43582
120.476275,31.433879
120.47791,31.437099
120.484517,31.443557
120.474797,31.436471
120.474797,31.436471
120.477654,31.441651
`

export  function  delay(ms){
    return  new  Promise((resolve)=>{
        setTimeout(resolve,ms)
    }
    )
}
    


(async ()=>{
    let list= gpsstr.split('\n').filter(x=>x)



    for(let i=0;i< list.length;i++){
        let [lng,lat]=list[i].split(',')
        let {data}= await wxConvert(lng,lat)
        let latlng= data.locations[0]
        console.log(`${latlng.lat},${latlng.lng}` )
        await delay(100)
    }

})()
