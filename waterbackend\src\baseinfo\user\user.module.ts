import { Module, OnModuleInit } from '@nestjs/common';
import { UserService } from './user.service';
import { UserController } from './user.controller';

import { User } from './entities/user.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { jwtConstants } from '../../auth/constants';
import { JwtStrategy } from '../../auth/jwt.strategy';
import { UserSeedService } from './user.seed';

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'your-secret-key',
      signOptions: { expiresIn: '24h' },
    }),
  ],
  controllers: [UserController],
  providers: [
    JwtStrategy,
    UserService,
    UserSeedService,
    // {
    //   provide: APP_GUARD,
    //   useClass: JwtAuthGuard,
    // },
  ],
  exports: [UserService],
})
export class UserModule implements OnModuleInit {
  constructor(private readonly userSeedService: UserSeedService) {}

  async onModuleInit() {
    // 在模块初始化时运行种子服务
    await this.userSeedService.seed();
  }
}
