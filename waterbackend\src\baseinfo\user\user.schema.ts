import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { Role } from 'src/role.enum';

export type UserDocument = User & Document;

@Schema()
export class User extends Document {
  @Prop()
  username: string;

  @Prop()
  password: string;

  @Prop()
  lastip: string;
  @Prop()
  lasttime: number;

  @Prop()
  masterid: string;

  @Prop()
  people: string;

  @Prop()
  phone: string;

  @Prop()
  openid: string;
  @Prop()
  roles: Role[];
}

export const UserSchema = SchemaFactory.createForClass(User);
