/**
 * 党支部查询对象类型
 */
export interface PartyBranchQuery extends PageQuery {
  keywords?: string;
  name?: string;
  coordinates?: string;
  startTime?: string;
  endTime?: string;
}

/**
 * 党支部分页对象
 */
export interface PartyBranchPageVO {
  /**
   * 党支部ID
   */
  id: number;
  /**
   * 党支部名称
   */
  name: string;
  /**
   * 党支部驻地
   */
  coordinates: string;
  /**
   * 添加时间
   */
  addTime: string;
}

/**
 * 党支部表单类型
 */
export interface PartyBranchForm {
  /**
   * 党支部ID
   */
  id?: number | null;
  /**
   * 党支部名称
   */
  name: string;
  /**
   * 党支部驻地
   */
  coordinates?: string;
}

/**
 * 党员信息查询对象类型
 */
export interface PartyMemberQuery extends PageQuery {
  keywords?: string;
  name?: string;
  branchId?: number;
  startTime?: string;
  endTime?: string;
}

/**
 * 党员信息分页对象
 */
export interface PartyMemberPageVO {
  id: number;
  name?: string;
  sex?: string;
  native?: string;
  age?: string;
  education?: string;
  partytime?: string;
  partyage?: string;
  photo?: string;
  branch?: string;
  branchId?: number;
  branchName?: string;
  addTime?: string;
}

/**
 * 党员信息表单类型
 */
export interface PartyMemberForm {
  id?: number | null;
  name?: string;
  sex?: string;
  native?: string;
  age?: string;
  education?: string;
  partytime?: string;
  partyage?: string;
  photo?: string;
  branch?: string;
  branchId?: number;
}

/**
 * 活动类型查询对象类型
 */
export interface ActivityTypeQuery extends PageQuery {
  keywords?: string;
  name?: string;
}

/**
 * 活动类型分页对象
 */
export interface ActivityTypePageVO {
  id: number;
  name: string;
  description?: string;
  addTime?: string;
}

/**
 * 活动类型表单类型
 */
export interface ActivityTypeForm {
  id?: number | null;
  type: string;
  description?: string;
}

/**
 * 党建活动查询对象类型
 */
export interface PartyActivityQuery extends PageQuery {
  keywords?: string;
  title?: string;
  typeId?: number;
  startTime?: string;
  endTime?: string;
}

/**
 * 党建活动分页对象
 */
export interface PartyActivityPageVO {
  id: number;
  title: string;
  description?: string;
  activityTime?: string;
  type?: string;
  branch?: string;
  number?: string;
  images?: string[];
  addTime?: string;
}

/**
 * 党建活动表单类型
 */
export interface PartyActivityForm {
  id?: number | null;
  title: string;
  description?: string;
  activityTime?: string;
  type?: string;
  branch?: string;
  number?: string;
  images?: string[];
}
