# /f:/水利站/backend/api_server.py
"""
精简版API服务器 - 只保留设备控制接口
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

# 导入设备控制功能
try:
    from device_control import create_do_command, send_command_to_device
except ImportError:
    def create_do_command(do_name, value):
        return f"{do_name}={value}"
    def send_command_to_device(sn, command):
        return False, None

# 创建 FastAPI 应用实例
app = FastAPI(
    title="水利站设备控制API",
    description="精简版设备控制接口",
    version="2.0.0",
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 定义API请求体的数据模型
class DOControlRequest(BaseModel):
    do_name: str = Field(..., description="要控制的DO名称, 例如 'DO21', 'DO22' 等。")
    value: int = Field(
        ..., ge=0, le=1, description="目标状态, 0 表示断开, 1 表示闭合。"
    )

@app.post("/control/{sn}", tags=["设备控制"], summary="立即控制设备DO输出")
async def control_do(sn: str, request: DOControlRequest):
    """
    立即控制指定设备的单个DO（数字输出）。

    - **sn**: 目标设备的序列号。
    - **request body**:
        - **do_name**: 要控制的DO名称 (e.g., "DO21", "DO22", "DO23", "DO24").
        - **value**: 目标状态 (0: 断开, 1: 闭合).
    """
    # 创建DO控制命令并发送到设备
    command = create_do_command(
        do_name=request.do_name, value=request.value
    )
    success = send_command_to_device(sn=sn, command=command)

    if not success:
        raise HTTPException(
            status_code=404,
            detail=f"指令发送失败。请确认设备 '{sn}' 是否已连接到服务器。",
        )
    return {"message": f"指令已成功发送到设备 {sn}。"}
