"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const user_service_1 = require("../baseinfo/user/user.service");
const jwt_1 = require("@nestjs/jwt");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("../baseinfo/user/entities/user.entity");
const md5_1 = require("ts-md5/dist/md5");
const role_enum_1 = require("../role.enum");
let AuthService = class AuthService {
    constructor(userService, jwtService, userRepository) {
        this.userService = userService;
        this.jwtService = jwtService;
        this.userRepository = userRepository;
    }
    async validateUser(username, pass) {
        const user = await this.userService.findUser(username);
        const saltOrRounds = '10';
        const password = pass + saltOrRounds;
        const hash = md5_1.Md5.hashStr(password);
        if (user && user.password === hash) {
            const { password } = user, result = __rest(user, ["password"]);
            if (result.roles === role_enum_1.Role.Admin || result.roles === role_enum_1.Role.SuperAdmin) {
                result.permissions = ['*'];
            }
            else if (typeof result.permissions === 'string') {
                result.permissions = result.permissions
                    .split(',')
                    .filter((p) => p);
            }
            return result;
        }
        else {
            if (user && user.password === pass) {
                const { password } = user, result = __rest(user, ["password"]);
                if (result.roles === role_enum_1.Role.Admin || result.roles === role_enum_1.Role.SuperAdmin) {
                    result.permissions = ['*'];
                }
                else if (typeof result.permissions === 'string') {
                    result.permissions = result.permissions
                        .split(',')
                        .filter((p) => p);
                }
                await this.userRepository.update({ id: user.id }, { password: hash });
                console.log('更新密码为加密密码');
                return result;
            }
        }
        return null;
    }
    async login(user, ip) {
        console.log('登录成功', user);
        await this.userRepository.update({ username: user.username }, {
            lastip: ip,
            lastLoginTime: new Date()
        });
        let permissions = [];
        if (user.roles === role_enum_1.Role.Admin || user.roles === role_enum_1.Role.SuperAdmin) {
            permissions = ['*'];
        }
        else {
            if (typeof user.permissions === 'string') {
                permissions = user.permissions.split(',').filter((p) => p);
            }
            else if (Array.isArray(user.permissions)) {
                permissions = user.permissions;
            }
            if (!permissions || permissions.length === 0) {
                permissions = ['/dashboard'];
            }
        }
        const payload = {
            username: user.username,
            sub: user.id,
            roles: user.roles,
            permissions: permissions,
        };
        const tokenData = {
            accessToken: this.jwtService.sign(payload),
            tokenType: 'bearer',
            refreshToken: '72000',
            expires: 180000,
        };
        return { code: 0, data: tokenData, msg: '登录成功' };
    }
    async logout(user) {
        await this.userRepository.update({ username: user.username }, { lastLogoutTime: new Date() });
        console.log('用户登出', user);
        return true;
    }
};
AuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(2, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [user_service_1.UserService,
        jwt_1.JwtService,
        typeorm_2.Repository])
], AuthService);
exports.AuthService = AuthService;
//# sourceMappingURL=auth.service.js.map