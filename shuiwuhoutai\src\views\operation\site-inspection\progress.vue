<template>
  <div class="site-inspection-progress">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <el-button @click="handleRefresh">刷新数据</el-button>
        </div>
      </template>

      <!-- 进度概览 -->
      <el-row :gutter="20" class="progress-overview">
        <el-col :span="8">
          <el-card class="progress-card">
            <div class="progress-item">
              <div class="progress-header">
                <span class="progress-title">总体进度</span>
                <span class="progress-percentage">{{ overallProgress }}%</span>
              </div>
              <el-progress
                :percentage="overallProgress"
                :color="getProgressColor(overallProgress)"
                :stroke-width="8"
              />
              <div class="progress-desc">
                已完成 {{ completedCount }} / {{ totalCount }} 个站点巡检
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="progress-card">
            <div class="progress-item">
              <div class="progress-header">
                <span class="progress-title">今日进度</span>
                <span class="progress-percentage">{{ todayProgress }}%</span>
              </div>
              <el-progress
                :percentage="todayProgress"
                :color="getProgressColor(todayProgress)"
                :stroke-width="8"
              />
              <div class="progress-desc">
                今日已完成 {{ todayCompleted }} / {{ todayTotal }} 个站点
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="progress-card">
            <div class="progress-item">
              <div class="progress-header">
                <span class="progress-title">本月进度</span>
                <span class="progress-percentage">{{ monthProgress }}%</span>
              </div>
              <el-progress
                :percentage="monthProgress"
                :color="getProgressColor(monthProgress)"
                :stroke-width="8"
              />
              <div class="progress-desc">
                本月已完成 {{ monthCompleted }} / {{ monthTotal }} 个站点
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 进度详情表格 -->
      <el-card class="table-card">
        <template #header>
          <span>站点运维进度详情</span>
        </template>

        <el-table :data="progressData" stripe>
          <el-table-column prop="siteType" label="站点类型" width="120">
            <template #default="{ row }">
              {{ getSiteTypeText(row.siteType) }}
            </template>
          </el-table-column>
          <el-table-column prop="totalSites" label="站点总数" width="100" />
          <el-table-column prop="inspectedCount" label="已巡检" width="100" />
          <el-table-column prop="pendingCount" label="待巡检" width="100" />
          <el-table-column prop="progress" label="进度" width="150">
            <template #default="{ row }">
              <div class="progress-cell">
                <el-progress
                  :percentage="row.progress"
                  :color="getProgressColor(row.progress)"
                  :stroke-width="6"
                />
                <span class="progress-text">{{ row.progress }}%</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="lastInspectionDate"
            label="最后巡检时间"
            width="150"
          />
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="handleViewDetail(row)"
                >查看详情</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";

// 进度数据
const overallProgress = ref(78);
const todayProgress = ref(65);
const monthProgress = ref(85);
const completedCount = ref(66);
const totalCount = ref(85);
const todayCompleted = ref(13);
const todayTotal = ref(20);
const monthCompleted = ref(68);
const monthTotal = ref(80);

// 进度详情数据
const progressData = ref([
  {
    id: 1,
    siteType: "pump_station",
    totalSites: 25,
    inspectedCount: 22,
    pendingCount: 3,
    progress: 88,
    lastInspectionDate: "2024-01-01",
  },
  {
    id: 2,
    siteType: "treatment_plant",
    totalSites: 15,
    inspectedCount: 10,
    pendingCount: 5,
    progress: 67,
    lastInspectionDate: "2023-12-28",
  },
]);

// 获取站点类型文本
const getSiteTypeText = (type: string) => {
  const texts: Record<string, string> = {
    pump_station: "水泵站",
    treatment_plant: "处理厂",
    distribution_station: "配水站",
    monitoring_station: "监测站",
  };
  return texts[type] || "其他";
};

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage >= 80) return "#67C23A";
  if (percentage >= 60) return "#E6A23C";
  return "#F56C6C";
};

const handleRefresh = () => console.log("刷新数据");
const handleViewDetail = (row: any) => console.log("查看详情", row);

onMounted(() => {
  // 初始化数据
});
</script>

<style scoped>
.site-inspection-progress {
  padding: 20px;
}

.page-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.progress-overview {
  margin-bottom: 20px;
}

.progress-card {
  border-radius: 8px;
}

.progress-item {
  padding: 20px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.progress-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.progress-percentage {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.progress-desc {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
  text-align: center;
}

.table-card {
  margin-top: 20px;
}

.progress-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  min-width: 35px;
}
</style>
