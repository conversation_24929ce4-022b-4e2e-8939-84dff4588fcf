# 软件部分纪要整理

## 一、总体目标与约束
- 试点规模：计划 50 个点位（新建 40、利旧 10），总价控制 20 万以内。
- 在线率要求：≥99%，对断电等客观因素预留容错，参照公安部门时效性要求组织响应与处置。
- 设计原则：
  - 以用户需求为向导，界面简洁易用；
  - 采集、控制、巡检与日常管理在数据层统一管理与交互，确保数据流完整与实时。



## 二、整体架构说明
- 分层与角色：
  - 站点侧（硬件与边缘）：以 USR-300 为核心，配合网关扩展机与采集器（电表、互感器、温湿度）完成数据采集与本地自动化控制；旋钮与接触器实现现场操作与物理档位回传。
  - 平台后端（数据与业务）：统一接入与存储设备数据，提供告警与工单、站点与设备台账、人员与巡检、历史查询与报表等能力。
  - 移动端前端（使用与交互）：面向运维与管理人员，提供监控、告警确认、任务调度、工单与巡检等操作入口。

- 数据流程：
  1) 采集：站点侧通过 RS485 将三相四线电参、电量与温湿度等数据采集至 USR-300；DI/DO 状态同步；按固定周期（如 5 秒）更新。
  2) 上传：USR-300 通过扩展机流量卡将数据上送平台，平台侧进行实时缓存与历史入库，支持分页与时间区间查询。
  3) 展示与分析：前端按站点/设备查询实时与历史数据，生成报表与趋势图，用于运行分析与问题追踪。

- 控制流程：
  1) 策略与触发：控制逻辑烧录在 USR-300 的 Node-RED 程序中，由定时/条件/状态驱动自动执行。
  2) 本地执行：遵循自动档与互锁规则在站点侧本地执行，不依赖平台网络，不存在远程下发控制。
  3) 回传与留痕：仅将执行状态与关键事件回传平台，用于展示、告警与追溯，可与工单流程关联。

- 告警流程：
  - 分级：告警分为一级（最高）、二级、三级；支持仅最高级即时推送，其它等级记录与检索的策略。
  - 触发与分发：边缘对关键阈值快速判定并上报；平台按策略分发短信/微信/企业 IM 等通知；所有事件持久化并支持统计。
  - 处置闭环：告警可联动工单，实现触发→派单→处理→复核→归档的闭环管理，并纳入人员与设备考核。

- 管理与运维：
  - 站点与设备：按站点聚合设备，维护台账、分组与生命周期；支持批量导入导出与状态总览。
  - 人员与巡检：支持网格化分区、任务下发、签到留痕与考核指标（响应及时率、解决率、任务完成率等）。
  - 数据与备份：关键指标看板、报表导出与数据备份，支撑稽核与决策。

- 可靠性与安全：
  - 可靠性：断电/断链容错；数据超时清理；现场安装与联调保障稳定。
  - 安全性：按角色与区域进行访问控制；关键操作二次确认并留痕；敏感数据脱敏、导出受控。


## 三、硬件说明
- 控制核心：采用多功能 PLC 控制主机（有人 USR-300）与两台网关扩展机（4DI+4DO、8DO）配合，多功能网关电源（有人 USR-DR）供电；扩展机使用流量卡（1G/月，2 年）将设备参数与数据上送平台。
- 数据采集：工业级电表采集器（正泰）、交流电量互感器模块（泰华仪表，0–10A）、温湿度采集器（译石 RS485）通过 RS485 与 USR-300 通信，采集三相四线电参数、电量与温湿度等数据。
- 开关与保护：
  - 运行/手动控制：前端旋钮、单触点与双触点接触器（长开/长闭）组成旋钮开关，实现设备自动化运行并支持手动操作，物理档位可回传主机；
  - 电气保护：交流接触器（正泰 NXC-12/220V）、小型漏电断路器（正泰 4P C32）、小型断路器（正泰 NXB-63/2P C16）、马达保护器（正泰 4–6A）；
  - 指示与信号：220V 运行指示灯、24V 故障信号灯；220V 接线端子、信号线端子、浮球信号端（5 米）。
- 安装与联调：底版及线材（底版加导轨 600×600、电源线、信号线、线槽等，以及设备与底版组合）；现场安装及联调测试（底板更换安装、PLC 现场端联调测试、开关按钮调试）确保系统稳定运行。

## 四、功能模块

### 4.1 设备数据采集
- 实时采集：设备运行状态、关键参数、开关（DI/DO）等；
- 存储与查询：支持历史数据存储与查询；
- 报表与可视化：提供曲线图、折线图、柱状图等；
- 历史查询粒度：支持分页与时间区间筛选；
- 数据要素：设备标识（SN）、运行档位、电流/电能、温湿度、浮球水位、时间戳等；
- 数据可靠性：超时/过期数据清理策略，确保视图可信。

### 4.2 设备控制
- 控制逻辑：全部在 USR-300 内的 Node-RED 脚本中实现，按定时/条件/状态自动执行。
- 互锁与安全：遵循自动档与互锁规则，本地执行，避免远程误操作。
- 事件留痕：本地执行事件与结果上报平台，留痕用于展示、告警与追溯；平台不下发控制命令。

### 4.3 站点管理
- 可视化：地图分区展示站点分布，支持缩放、点位标记、区域统计；
- 站点/设备台账：按站点聚合设备，维护基础信息、分组、生命周期；
- 批量能力：导入/导出与批量变更（如启停用、分组调整）；
- 状态总览：站点健康度、在线率、关键告警概览。

### 4.4 设备管理
- 状态监控：实时展示运行/故障状态，提供设备详情页与实时看板；
- 历史趋势：关键参数的历史曲线与对比；
- 工单联动：在设备详情中关联历史告警与工单处理结果。

### 4.5 故障分级与告警
- 分级：一级（最高）、二级、三级，支持自定义分级规则；
- 推送策略：
  - 一级（如断电）：短信提醒，并推送至指定负责人；
  - 二级：通过微信通知相关人员；
  - 三级：后台记录；
- 可配置策略：支持仅最高级即时推送，其它等级只记录的模式；
- 通知渠道：可接入企业 IM（企业微信/钉钉等）作为可选渠道；
- 事件管理：告警事件持久化、检索与统计，支持合并去重与抖动抑制（规划）。

### 4.6 故障工单管理
- 记录维度：处理时间、处理人、解决结果；
- 考核纳入：并入设备与人员的考核指标（后期细化）；
- 闭环流程：触发→派单→处理→复核→归档，过程留痕与 SLA 对齐校验。

### 4.7 人员管理与巡检管理
- 巡检记录：支持打卡、点位与任务完成情况记录，包含时间戳、位置信息与任务状态；
- 巡检分配：按区域划分责任区，支持“网格员”角色分配与任务下发；
- 考核体系：指定时限内响应与解决（如 2 小时内响应、24 小时内解决），系统自动记录与统计；
- 人员与权限：人员基本信息与角色权限（管理员/运维/观察者等）；
- 记录与导出：巡检/检修记录的新增、查看与导出，支撑稽核与报销。

### 4.8 数据管理
- 报表与分析：巡检统计、故障分析、趋势分析等；
- 备份：支持数据备份；
- 指标看板：在线率、告警统计、任务执行等关键指标总览；
- 历史服务能力：分页与时间区间查询的服务接口规范化（便于外部系统对接）。

### 4.9 安全与权限
- 防火墙方案后续讨论；
- 访问控制：按角色与站点/区域授权访问；
- 操作留痕：关键操作二次确认与日志留痕；
- 数据安全：敏感数据脱敏展示，数据导出权限受控。

### 4.10 关键术语调整
- 文案统一：将“浮球高水位激活”统一为“浮球高水位运行”。

### 4.11 技术升级与流量计算（后期）
- 流量换算：可基于水泵功率与运行时间进行流量估算；
- AI 接入：数据积累后，可接入大模型能力进行数据分析与辅助决策（如千问、DeepSeek 等）。

## 五、边缘侧本地自动化
- 周期巡检：固定周期检查站点状态，可手动触发用于调试与应急；
- 自动档位依赖：设备处于自动模式时按逻辑启停，避免手动档误动作；
- 水泵轮换：浮球触发时在两台水泵间交替运行，降低单泵负荷；
- 气泵策略：正常时定时交替，单侧故障时切换为另一侧持续运行；
- 告警检测：关键阈值快速判定，触发最高级告警上报平台；
- DI/DO 管理：统一维护数字输入/输出的状态，对外提供一致的数据视图；
- 初始化与恢复：重启/异常后统一初始化，保证逻辑连贯与可预测。



