import router from "@/router";
import { useUserStoreHook } from "@/store/modules/user";
import { usePermissionStoreHook } from "@/store/modules/permission";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { RouteRecordRaw } from "vue-router";
import { TOKEN_KEY } from "@/enums/CacheEnum";

NProgress.configure({ showSpinner: false });

// 白名单路由
const whiteList = ["/login", "/auth-redirect"];

/**
 * 获取用户第一个可访问的页面路由
 */
function getFirstAccessibleRoute(routes: RouteRecordRaw[]): string {
  for (const route of routes) {
    // 跳过隐藏的路由
    if (route.meta?.hidden) {
      continue;
    }

    // 如果是叶子节点且不是特殊路由，直接返回
    if (!route.children || route.children.length === 0) {
      if (
        route.path &&
        route.path !== "/login" &&
        route.path !== "/401" &&
        route.path !== "/404"
      ) {
        return route.path;
      }
      continue;
    }

    // 如果有子路由，递归查找第一个可访问的子路由
    const firstChildPath = getFirstAccessibleRoute(route.children);
    if (firstChildPath) {
      // 如果子路由路径是以/开头，直接返回
      if (firstChildPath.startsWith("/")) {
        return firstChildPath;
      }
      // 否则拼接父路由路径
      const fullPath =
        route.path === "/" ? firstChildPath : `${route.path}/${firstChildPath}`;
      return fullPath;
    }
  }
  return "";
}

/**
 * 检查用户是否有权限访问指定路由
 */
function hasRoutePermission(
  routes: RouteRecordRaw[],
  targetPath: string
): boolean {
  for (const route of routes) {
    // 检查当前路由
    if (route.path === targetPath) {
      return true;
    }

    // 检查子路由
    if (route.children) {
      for (const child of route.children) {
        const fullPath =
          route.path === "/" ? child.path : `${route.path}/${child.path}`;
        if (fullPath === targetPath || child.path === targetPath) {
          return true;
        }
      }
    }
  }
  return false;
}

router.beforeEach(async (to, from, next) => {
  NProgress.start();
  const userStore = useUserStoreHook();
  const permissionStore = usePermissionStoreHook();
  const hasToken = localStorage.getItem(TOKEN_KEY);

  if (hasToken) {
    if (to.path === "/login") {
      next({ path: "/" });
      NProgress.done();
    } else {
      try {
        // 如果没有用户信息，先获取用户信息
        if (!userStore.user.perms || userStore.user.perms.length === 0) {
          const { perms = [], roles } = await userStore.getUserInfo();
          // 设置路由权限
          permissionStore.setRoutes(roles, perms);
        }

        // 如果是根路径或登录后的首次访问
        if (to.path === "/" || to.path === "/login") {
          const firstPath = getFirstAccessibleRoute(permissionStore.routes);
          if (firstPath) {
            next({ path: firstPath, replace: true });
          } else {
            next("/401");
          }
        } else {
          // 检查用户是否有权限访问目标页面
          const hasPermission = hasRoutePermission(
            permissionStore.routes,
            to.path
          );

          if (hasPermission) {
            next();
          } else {
            // 如果没有权限，重定向到第一个有权限的页面
            const firstPath = getFirstAccessibleRoute(permissionStore.routes);
            if (firstPath) {
              next({ path: firstPath, replace: true });
            } else {
              next("/401");
            }
          }
        }
      } catch (error) {
        // 移除 token 并跳转登录页
        await userStore.resetToken();
        // 不保留之前的页面记录，直接跳转到登录页
        next("/login");
        NProgress.done();
      }
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      next();
    } else {
      // 不保留之前的页面记录，直接跳转到登录页
      next("/login");
      NProgress.done();
    }
  }
});

router.afterEach(() => {
  NProgress.done();
});
