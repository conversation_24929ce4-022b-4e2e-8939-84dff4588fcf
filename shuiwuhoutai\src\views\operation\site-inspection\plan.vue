<template>
  <div class="site-inspection-plan">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <el-button type="primary" @click="handleCreatePlan"
            >制定计划</el-button
          >
        </div>
      </template>

      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="计划名称">
          <el-input
            v-model="searchForm.planName"
            placeholder="请输入计划名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="站点类型">
          <el-select
            v-model="searchForm.siteType"
            placeholder="请选择类型"
            clearable
          >
            <el-option label="水泵站" value="pump_station" />
            <el-option label="处理厂" value="treatment_plant" />
            <el-option label="配水站" value="distribution_station" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="planData" stripe>
        <el-table-column prop="planNo" label="计划编号" width="120" />
        <el-table-column prop="planName" label="计划名称" />
        <el-table-column prop="siteCount" label="站点数量" width="100" />
        <el-table-column prop="startDate" label="开始时间" width="150" />
        <el-table-column prop="endDate" label="结束时间" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">{{
              getStatusText(row.status)
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="progress" label="进度" width="150">
          <template #default="{ row }">
            <el-progress
              :percentage="row.progress"
              :color="getProgressColor(row.progress)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)"
              >查看</el-button
            >
            <el-button type="success" size="small" @click="handleEdit(row)"
              >编辑</el-button
            >
            <el-button type="danger" size="small" @click="handleDelete(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";

const searchForm = reactive({
  planName: "",
  siteType: "",
});

const planData = ref([
  {
    id: 1,
    planNo: "SP202401001",
    planName: "第一季度站点巡检计划",
    siteCount: 25,
    startDate: "2024-01-01",
    endDate: "2024-03-31",
    status: "in_progress",
    progress: 65,
  },
]);

const getStatusType = (
  status: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const types: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    pending: "warning",
    in_progress: "primary",
    completed: "success",
  };
  return types[status] || "info";
};

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: "未开始",
    in_progress: "进行中",
    completed: "已完成",
  };
  return texts[status] || "未知";
};

const getProgressColor = (percentage: number) => {
  if (percentage >= 80) return "#67C23A";
  if (percentage >= 60) return "#E6A23C";
  return "#F56C6C";
};

const handleCreatePlan = () => console.log("制定计划");
const handleSearch = () => console.log("搜索计划", searchForm);
const handleReset = () =>
  Object.assign(searchForm, { planName: "", siteType: "" });
const handleView = (row: any) => console.log("查看计划", row);
const handleEdit = (row: any) => console.log("编辑计划", row);
const handleDelete = (row: any) => console.log("删除计划", row);

onMounted(() => {
  // 初始化
});
</script>

<style scoped>
.site-inspection-plan {
  padding: 20px;
}

.page-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}
</style>
