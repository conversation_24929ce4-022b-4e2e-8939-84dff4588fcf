import { forwardRef, Module } from '@nestjs/common';
import { MasterService } from './master.service';
import { MasterController } from './master.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Master, MasterSchema } from './master.schema';
import { APP_GUARD } from '@nestjs/core';
import { RolesGuard } from 'src/roles.guard';
import { JwtAuthGuard } from 'src/auth/auth.JwtAuthGuard';
// import { AuthModule } from '../../auth/auth.module';
// import { AuthService } from '../../auth/auth.service';
@Module({
  imports: [
    MongooseModule.forFeature([{ name: Master.name, schema: MasterSchema }]),
  ],
  controllers: [MasterController],
  providers: [
    MasterService,
    // {
    //   provide: APP_GUARD,
    //   useClass: JwtAuthGuard,
    // },
  ],
  exports: [MasterService],
})
export class MasterModule {}
