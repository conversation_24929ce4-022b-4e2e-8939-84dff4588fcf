import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseG<PERSON>s,
  CacheKey,
  CacheTTL,
} from '@nestjs/common';
import { LoginlogService } from './loginlog.service';
import { CreateLoginlogDto } from './dto/create-loginlog.dto';
import { UpdateLoginlogDto } from './dto/update-loginlog.dto';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';

@ApiBearerAuth()
@ApiTags('登录日志管理')
@Controller('loginlog')
export class LoginlogController {
  constructor(private readonly loginlogService: LoginlogService) {}

  @UseGuards(AuthGuard('jwt'))
  @ApiOperation({ summary: '获取登录日志' })
  @Get()
  @CacheKey('loginlog')
  @CacheTTL(1)
  findAll() {
    return this.loginlogService.findAll();
  }
}
