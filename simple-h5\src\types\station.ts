// 站点信息相关类型定义
export interface DeviceStatus {
  deviceNo: string
  version: string
  onlineOffline: number
}

export interface StationDevice {
  deviceNo: string
  projectId: number
  projectName: string
  deviceName: string
  deviceAddress: string
  deviceCreateTime: number
  deviceUpdateTime: number
  productModel: number
  deviceModelName: string
  productModelAbbreviation: string
  devicePosition: string
  devicePositionType: number
  deviceMac: string
  deviceImei: string
  deviceNid: string
  sn: string
  deviceEnableStatus: number
  deviceWeight: number
  deviceFuncMonitor: number
  deviceIsTransProtocol: number
  deviceVerifyCode: string
  deviceStatus: DeviceStatus
  webConfig: number
  active: number
  loraDeviceType: number
  deviceParamLock: number
  cloudSetDevice: number
  m300Type: number
  relCusDeviceNum: number
  ownerUid: number
  devicePass: string
  isBindAlarmConfig: number
  isOldDevice: number
  seriesName: string
  deviceDetailsType: number
  deviceDetailsId: number
  modelType: number
}

export interface StationListResponse {
  status: number
  data: {
    total: number
    list: StationDevice[]
    pageNum: number
    pageSize: number
    size: number
    startRow: number
    endRow: number
    pages: number
    prePage: number
    nextPage: number
    isFirstPage: boolean
    isLastPage: boolean
    hasPreviousPage: boolean
    hasNextPage: boolean
    navigatePages: number
    navigateFirstPage: number
    navigateLastPage: number
    firstPage: number
    lastPage: number
  }
  info: string
  account: string
}

export interface LoginResponse {
  status: number
  info: string
  data: {
    id: number
    account: string
    tel: string
    email: string
    wechatNickName: string
    addTime: string
    company: string
    address: string
    fullName: string
    buyProduct: string
    remark: string
    headImg: string
    headImgFull: string
    loginTime: string
    loginIp: string
    loginCount: number
    status: number
    type: number
    platformBinds: string[]
    token: string
    platformId: string
  }
}