/**
 * 特色农产品查询对象类型
 */
export interface AgriculturalProductQuery extends PageQuery {
  keywords?: string;
  name?: string;
  startTime?: string;
  endTime?: string;
}

/**
 * 特色农产品分页对象
 */
export interface AgriculturalProductPageVO {
  /**
   * 农产品ID
   */
  id: number;
  /**
   * 农作物名称
   */
  name: string;
  /**
   * 产量
   */
  yield: string;
  /**
   * 添加时间
   */
  addTime: string;
}

/**
 * 特色农产品表单类型
 */
export interface AgriculturalProductForm {
  /**
   * 农产品ID
   */
  id?: number | null;
  /**
   * 农作物名称
   */
  name: string;
  /**
   * 产量
   */
  yield: string;
}

/**
 * 特色农文旅景点查询对象类型
 */
export interface AttractionQuery extends PageQuery {
  keywords?: string;
  name?: string;
  startTime?: string;
  endTime?: string;
}

/**
 * 特色农文旅景点分页对象
 */
export interface AttractionPageVO {
  /**
   * 景点ID
   */
  id: number;
  /**
   * 景点名称
   */
  name: string;
  /**
   * 景点经纬度
   */
  coordinate: string;
  /**
   * 景点图片
   */
  images: string;
  /**
   * 景点720全景图链接
   */
  panorama: string;
  /**
   * 景点简介
   */
  introduction: string;
  /**
   * 备注1
   */
  remark1: string;
  /**
   * 备注2
   */
  remark2: string;
  /**
   * 添加时间
   */
  addTime: string;
}

/**
 * 特色农文旅景点表单类型
 */
export interface AttractionForm {
  /**
   * 景点ID
   */
  id?: number | null;
  /**
   * 景点名称
   */
  name: string;
  /**
   * 景点经纬度
   */
  coordinate: string;
  /**
   * 景点图片
   */
  images: string;
  /**
   * 景点720全景图链接
   */
  panorama: string;
  /**
   * 景点简介
   */
  introduction: string;
  /**
   * 备注1
   */
  remark1: string;
  /**
   * 备注2
   */
  remark2: string;
}

/**
 * 抖音链接查询对象类型
 */
export interface TiktokLinkQuery extends PageQuery {
  name?: string;
}

/**
 * 抖音链接分页对象
 */
export interface TiktokLinkPageVO {
  /**
   * 链接ID
   */
  id: number;
  /**
   * 链接名称
   */
  name: string;
  /**
   * 链接地址
   */
  link: string;
  /**
   * 添加时间
   */
  addTime: string;
}

/**
 * 抖音链接表单类型
 */
export interface TiktokLinkForm {
  /**
   * 链接ID
   */
  id?: number | null;
  /**
   * 链接名称
   */
  name: string;
  /**
   * 链接地址
   */
  link: string;
}

/**
 * 直播间场景查询对象类型
 */
export interface LiveroomQuery extends PageQuery {
  keywords?: string;
}

/**
 * 直播间场景分页对象
 */
export interface LiveroomPageVO {
  /**
   * 场景ID
   */
  id: number;
  /**
   * 抖音图片
   */
  images: string;
  /**
   * 备注1
   */
  remark1: string;
  /**
   * 备注2
   */
  remark2: string;
  /**
   * 添加时间
   */
  addTime: string;
}

/**
 * 直播间场景表单类型
 */
export interface LiveroomForm {
  /**
   * 场景ID
   */
  id?: number | null;
  /**
   * 抖音图片
   */
  images: string;
  /**
   * 备注1
   */
  remark1: string;
  /**
   * 备注2
   */
  remark2: string;
}

/**
 * 抖音精彩直播瞬间查询对象类型
 */
export interface LivemomentQuery extends PageQuery {
  keywords?: string;
}

/**
 * 抖音精彩直播瞬间分页对象
 */
export interface LivemomentPageVO {
  /**
   * 瞬间ID
   */
  id: number;
  /**
   * 图片
   */
  images: string;
  /**
   * 备注1
   */
  remark1: string;
  /**
   * 备注2
   */
  remark2: string;
  /**
   * 添加时间
   */
  addTime: string;
}

/**
 * 抖音精彩直播瞬间表单类型
 */
export interface LivemomentForm {
  /**
   * 瞬间ID
   */
  id?: number | null;
  /**
   * 图片
   */
  images: string;
  /**
   * 备注1
   */
  remark1: string;
  /**
   * 备注2
   */
  remark2: string;
}
