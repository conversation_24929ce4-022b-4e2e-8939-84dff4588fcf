import { Injectable } from '@nestjs/common';
import { UserService } from '../baseinfo/user/user.service';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../baseinfo/user/entities/user.entity';
import { Md5 } from 'ts-md5/dist/md5';
import { Role } from '../role.enum';

@Injectable()
export class AuthService {
  constructor(
    private readonly userService: UserService,
    private readonly jwtService: JwtService,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}
  async validateUser(username: string, pass: string): Promise<any> {
    const user = await this.userService.findUser(username);
    //20220402 修改密码验证方式,密码改成加密密码
    const saltOrRounds = '10';
    const password = pass + saltOrRounds;
    const hash = Md5.hashStr(password);
    if (user && user.password === hash) {
      const { password, ...result } = user;
      // 设置权限
      if (result.roles === Role.Admin || result.roles === Role.SuperAdmin) {
        result.permissions = ['*'];
      } else if (typeof result.permissions === 'string') {
        result.permissions = (result.permissions as string)
          .split(',')
          .filter((p) => p);
      }
      return result;
    } else {
      if (user && user.password === pass) {
        const { password, ...result } = user;
        // 设置权限
        if (result.roles === Role.Admin || result.roles === Role.SuperAdmin) {
          result.permissions = ['*'];
        } else if (typeof result.permissions === 'string') {
          result.permissions = (result.permissions as string)
            .split(',')
            .filter((p) => p);
        }
        //更新密码为加密密码
        await this.userRepository.update(
          { id: user.id },
          { password: hash }
        );
        console.log('更新密码为加密密码');
        return result;
      }
    }
    return null;
  }

  async login(user: any, ip: string) {
    console.log('登录成功', user);
    
    // 更新最后登录时间和IP
    await this.userRepository.update(
      { username: user.username },
      { 
        lastip: ip, 
        lastLoginTime: new Date() 
      }
    );

    // 根据角色设置权限
    let permissions = [];
    if (user.roles === Role.Admin || user.roles === Role.SuperAdmin) {
      permissions = ['*'];
    } else {
      // 对于普通用户，使用数据库中的权限
      if (typeof user.permissions === 'string') {
        permissions = user.permissions.split(',').filter((p) => p);
      } else if (Array.isArray(user.permissions)) {
        permissions = user.permissions;
      }
      // 如果没有权限，设置默认权限
      if (!permissions || permissions.length === 0) {
        permissions = ['/dashboard'];
      }
    }

    const payload = {
      username: user.username,
      sub: user.id,
      roles: user.roles,
      permissions: permissions,
    };
    const tokenData = {
      accessToken: this.jwtService.sign(payload),
      tokenType: 'bearer',
      refreshToken: '72000',
      expires: 180000,
    };
    return { code: 0, data: tokenData, msg: '登录成功' };
  }

  async logout(user: any) {
    await this.userRepository.update(
      { username: user.username },
      { lastLogoutTime: new Date() }
    );
    console.log('用户登出', user);
    return true;
  }
}
