<template>
  <div class="pipeline-inspection-plan">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <el-button type="primary" @click="handleCreatePlan"
            >制定计划</el-button
          >
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="计划名称">
          <el-input
            v-model="searchForm.planName"
            placeholder="请输入计划名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="计划状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="未开始" value="pending" />
            <el-option label="进行中" value="in_progress" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </el-form-item>
        <el-form-item label="计划时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 计划列表 -->
      <el-table :data="planData" stripe>
        <el-table-column prop="planNo" label="计划编号" width="120" />
        <el-table-column prop="planName" label="计划名称" />
        <el-table-column prop="pipelineCount" label="管网数量" width="100" />
        <el-table-column prop="startDate" label="开始时间" width="150" />
        <el-table-column prop="endDate" label="结束时间" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="progress" label="进度" width="150">
          <template #default="{ row }">
            <el-progress
              :percentage="row.progress"
              :color="getProgressColor(row.progress)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="创建人" width="100" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)"
              >查看</el-button
            >
            <el-button type="success" size="small" @click="handleEdit(row)"
              >编辑</el-button
            >
            <el-button type="danger" size="small" @click="handleDelete(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";

// 搜索表单
const searchForm = reactive({
  planName: "",
  status: "",
  dateRange: [],
});

// 计划数据
const planData = ref([
  {
    id: 1,
    planNo: "PP202401001",
    planName: "第一季度管网巡检计划",
    pipelineCount: 45,
    startDate: "2024-01-01",
    endDate: "2024-03-31",
    status: "in_progress",
    progress: 65,
    creator: "张三",
  },
]);

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0,
});

// 获取状态类型和文本
const getStatusType = (status: string): "success" | "primary" | "warning" | "info" | "danger" => {
  const types: Record<string, "success" | "primary" | "warning" | "info" | "danger"> = {
    pending: "warning",
    in_progress: "primary",
    completed: "success",
  };
  return types[status] || "info";
};

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: "未开始",
    in_progress: "进行中",
    completed: "已完成",
  };
  return texts[status] || "未知";
};

const getProgressColor = (percentage: number) => {
  if (percentage >= 80) return "#67C23A";
  if (percentage >= 60) return "#E6A23C";
  return "#F56C6C";
};

// 事件处理函数
const handleCreatePlan = () => console.log("制定计划");
const handleSearch = () => console.log("搜索计划", searchForm);
const handleReset = () =>
  Object.assign(searchForm, { planName: "", status: "", dateRange: [] });
const handleView = (row: any) => console.log("查看计划", row);
const handleEdit = (row: any) => console.log("编辑计划", row);
const handleDelete = (row: any) => console.log("删除计划", row);
const handleSizeChange = (size: number) => {
  pagination.size = size;
};
const handleCurrentChange = (page: number) => {
  pagination.page = page;
};

onMounted(() => {
  pagination.total = planData.value.length;
});
</script>

<style scoped>
.pipeline-inspection-plan {
  padding: 20px;
}

.page-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
