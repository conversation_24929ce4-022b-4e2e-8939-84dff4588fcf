<template>
  <div class="pipeline-inspection-progress">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <el-button @click="handleRefresh">刷新数据</el-button>
        </div>
      </template>

      <!-- 进度概览 -->
      <el-row :gutter="20" class="progress-overview">
        <el-col :span="8">
          <el-card class="progress-card">
            <div class="progress-item">
              <div class="progress-header">
                <span class="progress-title">总体进度</span>
                <span class="progress-percentage">{{ overallProgress }}%</span>
              </div>
              <el-progress
                :percentage="overallProgress"
                :color="getProgressColor(overallProgress)"
                :stroke-width="8"
              />
              <div class="progress-desc">
                已完成 {{ completedCount }} / {{ totalCount }} 个巡检任务
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="progress-card">
            <div class="progress-item">
              <div class="progress-header">
                <span class="progress-title">今日进度</span>
                <span class="progress-percentage">{{ todayProgress }}%</span>
              </div>
              <el-progress
                :percentage="todayProgress"
                :color="getProgressColor(todayProgress)"
                :stroke-width="8"
              />
              <div class="progress-desc">
                今日已完成 {{ todayCompleted }} / {{ todayTotal }} 个任务
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="progress-card">
            <div class="progress-item">
              <div class="progress-header">
                <span class="progress-title">本周进度</span>
                <span class="progress-percentage">{{ weekProgress }}%</span>
              </div>
              <el-progress
                :percentage="weekProgress"
                :color="getProgressColor(weekProgress)"
                :stroke-width="8"
              />
              <div class="progress-desc">
                本周已完成 {{ weekCompleted }} / {{ weekTotal }} 个任务
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 筛选条件 -->
      <el-form :model="filterForm" :inline="true" class="filter-form">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item label="巡检员">
          <el-select
            v-model="filterForm.inspector"
            placeholder="请选择巡检员"
            clearable
          >
            <el-option label="张三" value="张三" />
            <el-option label="李四" value="李四" />
            <el-option label="王五" value="王五" />
          </el-select>
        </el-form-item>
        <el-form-item label="管网区域">
          <el-select
            v-model="filterForm.area"
            placeholder="请选择区域"
            clearable
          >
            <el-option label="市中心区域" value="center" />
            <el-option label="工业开发区" value="industrial" />
            <el-option label="住宅区域" value="residential" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 进度图表 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <span>巡检进度趋势</span>
            </template>
            <div id="progressTrendChart" class="chart-container">
              巡检进度趋势图表
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <span>巡检员工作量分布</span>
            </template>
            <div id="workloadChart" class="chart-container">
              巡检员工作量分布图表
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 进度详情表格 -->
      <el-card class="table-card">
        <template #header>
          <div class="table-header">
            <span>巡检进度详情</span>
            <el-button @click="handleExport">导出报表</el-button>
          </div>
        </template>

        <el-table :data="progressData" stripe>
          <el-table-column prop="area" label="管网区域" width="120" />
          <el-table-column prop="totalPipelines" label="管网总数" width="100" />
          <el-table-column prop="inspectedCount" label="已巡检" width="100" />
          <el-table-column prop="pendingCount" label="待巡检" width="100" />
          <el-table-column prop="progress" label="进度" width="150">
            <template #default="{ row }">
              <div class="progress-cell">
                <el-progress
                  :percentage="row.progress"
                  :color="getProgressColor(row.progress)"
                  :stroke-width="6"
                />
                <span class="progress-text">{{ row.progress }}%</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="inspector" label="负责巡检员" width="120" />
          <el-table-column
            prop="lastInspectionDate"
            label="最后巡检时间"
            width="150"
          />
          <el-table-column
            prop="nextInspectionDate"
            label="下次巡检时间"
            width="150"
          />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="handleViewDetail(row)"
                >查看详情</el-button
              >
              <el-button type="warning" size="small" @click="handleAssign(row)"
                >分配任务</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-card>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";

// 进度数据
const overallProgress = ref(75);
const todayProgress = ref(60);
const weekProgress = ref(82);

const completedCount = ref(186);
const totalCount = ref(248);
const todayCompleted = ref(15);
const todayTotal = ref(25);
const weekCompleted = ref(123);
const weekTotal = ref(150);

// 筛选表单
const filterForm = reactive({
  dateRange: [],
  inspector: "",
  area: "",
});

// 进度详情数据
const progressData = ref([
  {
    id: 1,
    area: "市中心区域",
    totalPipelines: 45,
    inspectedCount: 38,
    pendingCount: 7,
    progress: 84,
    inspector: "张三",
    lastInspectionDate: "2024-01-01",
    nextInspectionDate: "2024-01-15",
  },
  {
    id: 2,
    area: "工业开发区",
    totalPipelines: 32,
    inspectedCount: 20,
    pendingCount: 12,
    progress: 63,
    inspector: "李四",
    lastInspectionDate: "2023-12-28",
    nextInspectionDate: "2024-01-12",
  },
  {
    id: 3,
    area: "住宅区域",
    totalPipelines: 58,
    inspectedCount: 52,
    pendingCount: 6,
    progress: 90,
    inspector: "王五",
    lastInspectionDate: "2024-01-02",
    nextInspectionDate: "2024-01-16",
  },
]);

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0,
});

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage >= 80) return "#67C23A";
  if (percentage >= 60) return "#E6A23C";
  return "#F56C6C";
};

// 日期改变
const handleDateChange = (dates) => {
  console.log("日期范围改变", dates);
  loadProgressData();
};

// 搜索
const handleSearch = () => {
  console.log("搜索进度数据", filterForm);
  loadProgressData();
};

// 重置
const handleReset = () => {
  Object.assign(filterForm, {
    dateRange: [],
    inspector: "",
    area: "",
  });
  loadProgressData();
};

// 刷新数据
const handleRefresh = () => {
  loadProgressData();
};

// 导出报表
const handleExport = () => {
  console.log("导出进度报表");
};

// 查看详情
const handleViewDetail = (row) => {
  console.log("查看区域详情", row);
};

// 分配任务
const handleAssign = (row) => {
  console.log("分配巡检任务", row);
};

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.size = size;
  loadProgressData();
};

// 当前页改变
const handleCurrentChange = (page) => {
  pagination.page = page;
  loadProgressData();
};

// 加载进度数据
const loadProgressData = () => {
  console.log("加载进度数据");
  pagination.total = progressData.value.length;
};

onMounted(() => {
  // 设置默认时间范围（最近30天）
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - 29);

  filterForm.dateRange = [
    startDate.toISOString().split("T")[0],
    endDate.toISOString().split("T")[0],
  ];

  loadProgressData();
});
</script>

<style scoped>
.pipeline-inspection-progress {
  padding: 20px;
}

.page-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.progress-overview {
  margin-bottom: 20px;
}

.progress-card {
  border-radius: 8px;
}

.progress-item {
  padding: 20px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.progress-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.progress-percentage {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.progress-desc {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
  text-align: center;
}

.filter-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-container {
  height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  border-radius: 4px;
  color: #999;
}

.table-card {
  margin-top: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  min-width: 35px;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
