<template>
  <div class="sms-log">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-button type="primary" @click="refreshData">刷新</el-button>
        </div>
      </template>

      <!-- 搜索条件 -->
      <el-form :model="searchForm" label-width="80px" inline>
        <el-form-item label="发送状态">
          <el-select v-model="searchForm.status" placeholder="请选择发送状态">
            <el-option label="全部" value="" />
            <el-option label="发送成功" value="success" />
            <el-option label="发送失败" value="failed" />
            <el-option label="待发送" value="pending" />
          </el-select>
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="searchForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 短信日志表格 -->
      <el-table :data="smsLogList" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="日志ID" width="100" />
        <el-table-column prop="phone" label="接收手机号" width="150" />
        <el-table-column
          prop="content"
          label="短信内容"
          show-overflow-tooltip
        />
        <el-table-column prop="status" label="发送状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sendTime" label="发送时间" width="180" />
        <el-table-column
          prop="errorMsg"
          label="错误信息"
          show-overflow-tooltip
          width="200"
        />
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleView(scope.row)"
              >查看</el-button
            >
            <el-button
              type="warning"
              size="small"
              @click="handleResend(scope.row)"
              v-if="scope.row.status === 'failed'"
              >重发</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        style="margin-top: 20px"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";

// 搜索表单
const searchForm = reactive({
  status: "",
  phone: "",
  dateRange: [],
});

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0,
});

// 短信日志列表数据
const smsLogList = ref([
  {
    id: "SMS001",
    phone: "138****1234",
    content: "告警通知：泵站001压力异常，请及时处理",
    status: "success",
    sendTime: "2024-01-15 10:31:00",
    errorMsg: "",
  },
  {
    id: "SMS002",
    phone: "139****5678",
    content: "告警通知：管网002流量超限，请关注",
    status: "failed",
    sendTime: "2024-01-15 09:16:00",
    errorMsg: "手机号码格式错误",
  },
]);

const loading = ref(false);

// 获取状态类型
const getStatusType = (
  status: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const typeMap: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    success: "success",
    failed: "danger",
    pending: "warning",
  };
  return typeMap[status] || "info";
};

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    success: "发送成功",
    failed: "发送失败",
    pending: "待发送",
  };
  return textMap[status] || "未知";
};

// 查询
const handleSearch = () => {
  console.log("搜索条件:", searchForm);
  loadData();
};

// 重置搜索
const resetSearch = () => {
  searchForm.status = "";
  searchForm.phone = "";
  searchForm.dateRange = [];
  loadData();
};

// 刷新数据
const refreshData = () => {
  loadData();
};

// 查看详情
const handleView = (row: any) => {
  console.log("查看短信详情:", row);
};

// 重新发送
const handleResend = (row: any) => {
  console.log("重新发送短信:", row);
};

// 分页大小改变
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  loadData();
};

// 当前页改变
const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  loadData();
};

// 加载数据
const loadData = () => {
  loading.value = true;
  // 模拟API调用
  setTimeout(() => {
    pagination.total = 100;
    loading.value = false;
  }, 1000);
};

onMounted(() => {
  loadData();
});
</script>

<style scoped>
.sms-log {
  padding: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
