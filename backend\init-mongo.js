// /f:/水利站/backend/init-mongo.js
// MongoDB初始化脚本

// 切换到目标数据库
db = db.getSiblingDB('water_station_realtime');

// 创建应用用户
db.createUser({
  user: 'water_user',
  pwd: 'water123',
  roles: [
    {
      role: 'readWrite',
      db: 'water_station_realtime'
    }
  ]
});

// 创建设备数据集合
db.createCollection('device_data_5s', {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["device_sn", "timestamp", "raw_data"],
      properties: {
        device_sn: {
          bsonType: "string",
          description: "设备序列号"
        },
        timestamp: {
          bsonType: "date",
          description: "数据时间戳"
        },
        raw_data: {
          bsonType: "object",
          description: "原始设备数据"
        }
      }
    }
  }
});

// 创建索引
db.device_data_5s.createIndex({ "device_sn": 1, "timestamp": -1 });
db.device_data_5s.createIndex({ "timestamp": 1 }, { expireAfterSeconds: 86400 }); // TTL索引：24小时过期

// 插入测试数据
db.device_data_5s.insertOne({
  device_sn: "INIT_TEST_001",
  timestamp: new Date(),
  raw_data: {
    float1: 25.5,
    float2: 30.2,
    water_pump1: { status: 1 },
    water_pump2: { status: 0 },
    init: true
  }
});

print("MongoDB初始化完成");
print("- 数据库: water_station_realtime");
print("- 用户: water_user");
print("- 集合: device_data_5s");
print("- 索引: device_sn+timestamp, TTL索引(24小时)");
print("- 测试数据: 1条初始化记录");