<template>
  <div class="event-center">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-button type="primary" @click="refreshData">刷新</el-button>
        </div>
      </template>

      <!-- 搜索条件 -->
      <el-form :model="searchForm" label-width="80px" inline>
        <el-form-item label="事件类型">
          <el-select v-model="searchForm.type" placeholder="请选择事件类型">
            <el-option label="全部" value="" />
            <el-option label="设备维护" value="maintenance" />
            <el-option label="故障处理" value="repair" />
            <el-option label="巡检记录" value="inspection" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态">
            <el-option label="全部" value="" />
            <el-option label="进行中" value="ongoing" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="success" @click="handleCreate">新增事件</el-button>
        </el-form-item>
      </el-form>

      <!-- 事件列表 -->
      <el-table :data="eventList" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="事件ID" width="100" />
        <el-table-column prop="title" label="事件标题" />
        <el-table-column prop="type" label="事件类型" width="100">
          <template #default="scope">
            <el-tag :type="getTypeColor(scope.row.type)">
              {{ getTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="scope">
            <el-tag :type="getPriorityColor(scope.row.priority)">
              {{ getPriorityText(scope.row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="assignee" label="负责人" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleView(scope.row)"
              >查看</el-button
            >
            <el-button
              type="warning"
              size="small"
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        style="margin-top: 20px"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";

// 搜索表单
const searchForm = reactive({
  type: "",
  status: "",
});

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0,
});

// 事件列表数据
const eventList = ref([
  {
    id: "EVT001",
    title: "泵站001定期维护",
    type: "maintenance",
    priority: "normal",
    assignee: "张三",
    status: "ongoing",
    createTime: "2024-01-15 08:00:00",
  },
  {
    id: "EVT002",
    title: "管网002压力异常处理",
    type: "repair",
    priority: "high",
    assignee: "李四",
    status: "completed",
    createTime: "2024-01-14 15:30:00",
  },
]);

const loading = ref(false);

// 获取类型颜色
const getTypeColor = (
  type: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const colorMap: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    maintenance: "info",
    repair: "warning",
    inspection: "success",
  };
  return colorMap[type] || "info";
};

// 获取类型文本
const getTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    maintenance: "设备维护",
    repair: "故障处理",
    inspection: "巡检记录",
  };
  return textMap[type] || "未知";
};

// 获取优先级颜色
const getPriorityColor = (
  priority: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const colorMap: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    high: "danger",
    normal: "warning",
    low: "info",
  };
  return colorMap[priority] || "info";
};

// 获取优先级文本
const getPriorityText = (priority: string) => {
  const textMap: Record<string, string> = {
    high: "高",
    normal: "中",
    low: "低",
  };
  return textMap[priority] || "中";
};

// 获取状态颜色
const getStatusColor = (
  status: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const colorMap: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    ongoing: "warning",
    completed: "success",
    cancelled: "info",
  };
  return colorMap[status] || "info";
};

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    ongoing: "进行中",
    completed: "已完成",
    cancelled: "已取消",
  };
  return textMap[status] || "未知";
};

// 查询
const handleSearch = () => {
  console.log("搜索条件:", searchForm);
  loadData();
};

// 重置搜索
const resetSearch = () => {
  searchForm.type = "";
  searchForm.status = "";
  loadData();
};

// 刷新数据
const refreshData = () => {
  loadData();
};

// 新增事件
const handleCreate = () => {
  console.log("新增事件");
};

// 查看详情
const handleView = (row: any) => {
  console.log("查看事件详情:", row);
};

// 编辑事件
const handleEdit = (row: any) => {
  console.log("编辑事件:", row);
};

// 分页大小改变
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  loadData();
};

// 当前页改变
const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  loadData();
};

// 加载数据
const loadData = () => {
  loading.value = true;
  setTimeout(() => {
    pagination.total = 50;
    loading.value = false;
  }, 1000);
};

onMounted(() => {
  loadData();
});
</script>

<style scoped>
.event-center {
  padding: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
