import { CacheInterceptor, CacheModule, Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';

import { BaseinfoModule } from './baseinfo/baseinfo.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';
import { AuthModule } from './auth/auth.module';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { FileModule } from './file/file.module';
import { WaterstationModule } from './waterstation/waterstation.module';
import { MongooseModule } from '@nestjs/mongoose';

@Module({
  imports: [
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '.', 'client'),
    }),
    CacheModule.register({
      ttl: 1,
      max: 1000, //缓存中最大和最小数量
    }),
    TypeOrmModule.forRoot({
      type: 'mysql',
      host: 'localhost',
      port: 3311,
      username: 'water_user',
      password: 'water123',
      database: 'water_station_config',
      entities: [__dirname + '/**/entities/*.entity{.ts,.js}'],
      synchronize: true,
      autoLoadEntities: true,
      cache: false,
    }),
    MongooseModule.forRoot('********************************************************************************'),
    BaseinfoModule,
    AuthModule,
    FileModule,
    WaterstationModule,
    TypeOrmModule.forFeature(),
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: CacheInterceptor,
    },
  ],
})
export class AppModule {}
