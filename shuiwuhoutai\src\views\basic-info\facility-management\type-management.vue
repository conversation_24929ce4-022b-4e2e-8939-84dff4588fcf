<template>
  <div class="facility-type-management-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="header-actions">
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              新增类型
            </el-button>
            <el-button @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form">
        <el-form :model="searchForm" inline>
          <el-form-item label="类型名称">
            <el-input
              v-model="searchForm.typeName"
              placeholder="请输入类型名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="类型编码">
            <el-input
              v-model="searchForm.typeCode"
              placeholder="请输入类型编码"
              clearable
              style="width: 150px"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 120px"
            >
              <el-option label="启用" value="1" />
              <el-option label="禁用" value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 类型统计 -->
      <div class="type-statistics">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon total">
                <el-icon><DataBoard /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.total }}</div>
                <div class="stat-label">类型总数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon enabled">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.enabled }}</div>
                <div class="stat-label">启用类型</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon disabled">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.disabled }}</div>
                <div class="stat-label">禁用类型</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon used">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.used }}</div>
                <div class="stat-label">已使用</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table
          :data="tableData"
          style="width: 100%"
          stripe
          border
          v-loading="tableLoading"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column
            prop="typeCode"
            label="类型编码"
            width="120"
            sortable
          />
          <el-table-column
            prop="typeName"
            label="类型名称"
            width="150"
            sortable
          />
          <el-table-column prop="parentTypeName" label="上级类型" width="150" />
          <el-table-column prop="icon" label="图标" width="80">
            <template #default="{ row }">
              <el-icon v-if="row.icon" size="24">
                <component :is="row.icon" />
              </el-icon>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="color" label="颜色" width="100">
            <template #default="{ row }">
              <div v-if="row.color" class="color-display">
                <div
                  class="color-block"
                  :style="{ backgroundColor: row.color }"
                ></div>
                <span>{{ row.color }}</span>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="facilityCount"
            label="设施数量"
            width="100"
            sortable
          />
          <el-table-column prop="sort" label="排序" width="80" sortable />
          <el-table-column prop="status" label="状态" width="80">
            <template #default="{ row }">
              <el-tag
                :type="row.status === '1' ? 'success' : 'danger'"
                size="small"
              >
                {{ row.status === "1" ? "启用" : "禁用" }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            label="创建时间"
            width="180"
            sortable
          />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                size="small"
                type="primary"
                text
                @click="handleEdit(row)"
              >
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                size="small"
                :type="row.status === '1' ? 'warning' : 'success'"
                text
                @click="handleToggleStatus(row)"
              >
                <el-icon><Switch /></el-icon>
                {{ row.status === "1" ? "禁用" : "启用" }}
              </el-button>
              <el-button
                size="small"
                type="danger"
                text
                @click="handleDelete(row)"
                :disabled="row.facilityCount > 0"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="类型编码" prop="typeCode">
              <el-input
                v-model="formData.typeCode"
                placeholder="请输入类型编码"
                maxlength="20"
                :disabled="!!formData.id"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型名称" prop="typeName">
              <el-input
                v-model="formData.typeName"
                placeholder="请输入类型名称"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="上级类型" prop="parentId">
              <el-tree-select
                v-model="formData.parentId"
                :data="typeTreeData"
                :props="{ label: 'typeName', value: 'id' }"
                placeholder="请选择上级类型"
                clearable
                check-strictly
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="图标" prop="icon">
              <el-select
                v-model="formData.icon"
                placeholder="请选择图标"
                style="width: 100%"
              >
                <el-option label="水泵" value="Promotion">
                  <el-icon><Promotion /></el-icon>
                  <span style="margin-left: 8px">水泵</span>
                </el-option>
                <el-option label="监测" value="View">
                  <el-icon><View /></el-icon>
                  <span style="margin-left: 8px">监测</span>
                </el-option>
                <el-option label="管道" value="Connection">
                  <el-icon><Connection /></el-icon>
                  <span style="margin-left: 8px">管道</span>
                </el-option>
                <el-option label="闸门" value="Switch">
                  <el-icon><Switch /></el-icon>
                  <span style="margin-left: 8px">闸门</span>
                </el-option>
                <el-option label="其他" value="More">
                  <el-icon><More /></el-icon>
                  <span style="margin-left: 8px">其他</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="颜色" prop="color">
              <el-color-picker v-model="formData.color" show-alpha />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sort">
              <el-input-number
                v-model="formData.sort"
                :min="0"
                :max="999"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="formData.status">
                <el-radio label="1">启用</el-radio>
                <el-radio label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="类型描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入类型描述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="属性配置" prop="attributes">
          <div class="attributes-config">
            <div
              v-for="(attr, index) in formData.attributes"
              :key="index"
              class="attribute-item"
            >
              <el-input
                v-model="attr.name"
                placeholder="属性名称"
                style="width: 120px; margin-right: 10px"
              />
              <el-select
                v-model="attr.type"
                placeholder="数据类型"
                style="width: 100px; margin-right: 10px"
              >
                <el-option label="文本" value="text" />
                <el-option label="数字" value="number" />
                <el-option label="日期" value="date" />
                <el-option label="布尔" value="boolean" />
              </el-select>
              <el-checkbox v-model="attr.required" style="margin-right: 10px"
                >必填</el-checkbox
              >
              <el-button
                size="small"
                type="danger"
                text
                @click="removeAttribute(index)"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
            <el-button
              type="primary"
              text
              @click="addAttribute"
              style="margin-top: 10px"
            >
              <el-icon><Plus /></el-icon>
              添加属性
            </el-button>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleSubmit"
            :loading="submitLoading"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Plus,
  Refresh,
  Search,
  Edit,
  Delete,
  Switch,
  DataBoard,
  CircleCheck,
  CircleClose,
  Connection,
  Promotion,
  View,
  More,
} from "@element-plus/icons-vue";

// 搜索表单
const searchForm = reactive({
  typeName: "",
  typeCode: "",
  status: "",
});

// 表格数据
const tableLoading = ref(false);
const tableData = ref([
  {
    id: "1",
    typeCode: "PUMP",
    typeName: "水泵设施",
    parentId: "0",
    parentTypeName: "-",
    icon: "Promotion",
    color: "#409eff",
    facilityCount: 15,
    sort: 1,
    status: "1",
    createTime: "2023-01-01 10:00:00",
    description: "各类水泵设施",
  },
  {
    id: "2",
    typeCode: "MONITOR",
    typeName: "监测设施",
    parentId: "0",
    parentTypeName: "-",
    icon: "View",
    color: "#67c23a",
    facilityCount: 8,
    sort: 2,
    status: "1",
    createTime: "2023-01-02 11:00:00",
    description: "水质监测等设施",
  },
  {
    id: "3",
    typeCode: "PIPELINE",
    typeName: "管道设施",
    parentId: "0",
    parentTypeName: "-",
    icon: "Connection",
    color: "#e6a23c",
    facilityCount: 25,
    sort: 3,
    status: "1",
    createTime: "2023-01-03 12:00:00",
    description: "输水管道设施",
  },
  {
    id: "4",
    typeCode: "GATE",
    typeName: "闸门设施",
    parentId: "0",
    parentTypeName: "-",
    icon: "Switch",
    color: "#f56c6c",
    facilityCount: 5,
    sort: 4,
    status: "1",
    createTime: "2023-01-04 13:00:00",
    description: "各类闸门控制设施",
  },
  {
    id: "5",
    typeCode: "CENTRI_PUMP",
    typeName: "离心泵",
    parentId: "1",
    parentTypeName: "水泵设施",
    icon: "Promotion",
    color: "#409eff",
    facilityCount: 3,
    sort: 1,
    status: "1",
    createTime: "2023-01-05 14:00:00",
    description: "离心式水泵",
  },
  {
    id: "6",
    typeCode: "SUBMERS_PUMP",
    typeName: "潜水泵",
    parentId: "1",
    parentTypeName: "水泵设施",
    icon: "Promotion",
    color: "#409eff",
    facilityCount: 0,
    sort: 2,
    status: "0",
    createTime: "2023-01-06 15:00:00",
    description: "潜水式水泵",
  },
]);

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 6,
});

// 统计数据
const statistics = computed(() => {
  const total = tableData.value.length;
  const enabled = tableData.value.filter((item) => item.status === "1").length;
  const disabled = tableData.value.filter((item) => item.status === "0").length;
  const used = tableData.value.filter((item) => item.facilityCount > 0).length;

  return { total, enabled, disabled, used };
});

// 类型树数据（用于选择上级类型）
const typeTreeData = computed(() => {
  const buildTree = (items: any[], parentId = "0"): any[] => {
    return items
      .filter((item) => item.parentId === parentId && item.id !== formData.id)
      .map((item) => ({
        id: item.id,
        typeName: item.typeName,
        children: buildTree(items, item.id),
      }));
  };

  return [
    { id: "0", typeName: "根类型", children: buildTree(tableData.value) },
  ];
});

// 对话框
const dialogVisible = ref(false);
const dialogTitle = ref("");
const submitLoading = ref(false);
const formRef = ref();

const formData = reactive({
  id: "",
  typeCode: "",
  typeName: "",
  parentId: "0",
  icon: "",
  color: "#409eff",
  sort: 0,
  status: "1",
  description: "",
  attributes: [] as Array<{ name: string; type: string; required: boolean }>,
});

const formRules = {
  typeCode: [
    { required: true, message: "请输入类型编码", trigger: "blur" },
    {
      pattern: /^[A-Z_]+$/,
      message: "编码只能包含大写字母和下划线",
      trigger: "blur",
    },
  ],
  typeName: [
    { required: true, message: "请输入类型名称", trigger: "blur" },
    { min: 2, max: 50, message: "长度在 2 到 50 个字符", trigger: "blur" },
  ],
};

// 方法
const handleSearch = () => {
  tableLoading.value = true;
  // TODO: 实现搜索逻辑
  setTimeout(() => {
    tableLoading.value = false;
    ElMessage.success("搜索完成");
  }, 1000);
};

const handleReset = () => {
  searchForm.typeName = "";
  searchForm.typeCode = "";
  searchForm.status = "";
  ElMessage.success("搜索条件已重置");
};

const refreshData = () => {
  tableLoading.value = true;
  // TODO: 实现数据刷新
  setTimeout(() => {
    tableLoading.value = false;
    ElMessage.success("数据已刷新");
  }, 1000);
};

const handleAdd = () => {
  dialogTitle.value = "新增类型";
  resetFormData();
  dialogVisible.value = true;
};

const handleEdit = (row: any) => {
  dialogTitle.value = "编辑类型";
  Object.assign(formData, {
    ...row,
    attributes: row.attributes || [],
  });
  dialogVisible.value = true;
};

const handleToggleStatus = (row: any) => {
  const action = row.status === "1" ? "禁用" : "启用";
  ElMessageBox.confirm(`确认${action}类型 "${row.typeName}" 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // TODO: 实现状态切换逻辑
      row.status = row.status === "1" ? "0" : "1";
      ElMessage.success(`${action}成功`);
    })
    .catch(() => {
      ElMessage.info(`已取消${action}`);
    });
};

const handleDelete = (row: any) => {
  if (row.facilityCount > 0) {
    ElMessage.warning("该类型下还有设施，无法删除");
    return;
  }

  ElMessageBox.confirm(`确认删除类型 "${row.typeName}" 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // TODO: 实现删除逻辑
      ElMessage.success("删除成功");
    })
    .catch(() => {
      ElMessage.info("已取消删除");
    });
};

const addAttribute = () => {
  formData.attributes.push({
    name: "",
    type: "text",
    required: false,
  });
};

const removeAttribute = (index: number) => {
  formData.attributes.splice(index, 1);
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitLoading.value = true;

    // TODO: 实现提交逻辑
    await new Promise((resolve) => setTimeout(resolve, 1000));

    ElMessage.success(
      dialogTitle.value.includes("新增") ? "新增成功" : "修改成功"
    );
    dialogVisible.value = false;
    refreshData();
  } catch (error) {
    console.error("表单验证失败:", error);
  } finally {
    submitLoading.value = false;
  }
};

const handleDialogClose = () => {
  resetFormData();
  formRef.value?.clearValidate();
};

const resetFormData = () => {
  formData.id = "";
  formData.typeCode = "";
  formData.typeName = "";
  formData.parentId = "0";
  formData.icon = "";
  formData.color = "#409eff";
  formData.sort = 0;
  formData.status = "1";
  formData.description = "";
  formData.attributes = [];
};

const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  refreshData();
};

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page;
  refreshData();
};

onMounted(() => {
  refreshData();
});
</script>

<style scoped>
.facility-type-management-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.type-statistics {
  margin: 20px 0;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #409eff;
}

.stat-icon {
  font-size: 36px;
  margin-right: 16px;
  color: #409eff;
}

.stat-icon.enabled {
  color: #67c23a;
}

.stat-icon.disabled {
  color: #f56c6c;
}

.stat-icon.used {
  color: #e6a23c;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.table-container {
  margin-top: 20px;
}

.color-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-block {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.attributes-config {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background-color: #f8f9fa;
}

.attribute-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.attribute-item:last-child {
  margin-bottom: 0;
}

.dialog-footer {
  text-align: right;
}
</style>
