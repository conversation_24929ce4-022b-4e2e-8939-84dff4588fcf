<template>
  <div class="work-order-evaluation">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <el-button type="primary" @click="handleCreateModel"
            >创建模型</el-button
          >
        </div>
      </template>

      <!-- 评价模型配置 -->
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="config-card">
            <template #header>
              <span>评价维度配置</span>
            </template>
            <div class="dimension-list">
              <div
                v-for="dimension in evaluationDimensions"
                :key="dimension.id"
                class="dimension-item"
              >
                <div class="dimension-header">
                  <span class="dimension-name">{{ dimension.name }}</span>
                  <span class="dimension-weight"
                    >权重: {{ dimension.weight }}%</span
                  >
                </div>
                <div class="dimension-desc">{{ dimension.description }}</div>
                <el-slider
                  v-model="dimension.weight"
                  :max="100"
                  :min="0"
                  @change="updateWeights"
                />
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card class="config-card">
            <template #header>
              <span>评分规则</span>
            </template>
            <div class="rules-list">
              <div
                v-for="rule in scoringRules"
                :key="rule.id"
                class="rule-item"
              >
                <div class="rule-header">
                  <el-tag :type="getRuleTagType(rule.type)">{{
                    rule.name
                  }}</el-tag>
                  <span class="rule-score">{{ rule.score }}分</span>
                </div>
                <div class="rule-condition">{{ rule.condition }}</div>
              </div>
            </div>
            <el-button type="primary" size="small" @click="handleAddRule"
              >添加规则</el-button
            >
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card class="config-card">
            <template #header>
              <span>评价等级</span>
            </template>
            <div class="grade-list">
              <div
                v-for="grade in evaluationGrades"
                :key="grade.id"
                class="grade-item"
              >
                <div class="grade-header">
                  <el-tag :type="getRuleTagType(grade.type)" size="large">{{
                    grade.name
                  }}</el-tag>
                </div>
                <div class="grade-range">
                  {{ grade.minScore }} - {{ grade.maxScore }}分
                </div>
                <div class="grade-desc">{{ grade.description }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 评价结果统计 -->
      <el-row :gutter="20" class="stats-section">
        <el-col :span="12">
          <el-card class="stats-card">
            <template #header>
              <span>评价结果分布</span>
            </template>
            <div id="evaluationChart" class="chart-container">
              评价结果分布图表
            </div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card class="stats-card">
            <template #header>
              <span>评价趋势</span>
            </template>
            <div id="trendChart" class="chart-container">评价趋势图表</div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 工单评价列表 -->
      <el-card class="table-card">
        <template #header>
          <div class="table-header">
            <span>工单评价记录</span>
            <el-button @click="handleBatchEvaluate">批量评价</el-button>
          </div>
        </template>

        <!-- 搜索表单 -->
        <el-form :model="searchForm" :inline="true" class="search-form">
          <el-form-item label="工单编号">
            <el-input
              v-model="searchForm.orderNo"
              placeholder="请输入工单编号"
              clearable
            />
          </el-form-item>
          <el-form-item label="评价等级">
            <el-select
              v-model="searchForm.grade"
              placeholder="请选择等级"
              clearable
            >
              <el-option
                v-for="grade in evaluationGrades"
                :key="grade.id"
                :label="grade.name"
                :value="grade.name"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="评价时间">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table :data="evaluationRecords" stripe>
          <el-table-column prop="orderNo" label="工单编号" width="120" />
          <el-table-column prop="title" label="工单标题" />
          <el-table-column prop="assignee" label="处理人" width="100" />
          <el-table-column prop="completedDate" label="完成时间" width="150" />
          <el-table-column prop="totalScore" label="总分" width="80">
            <template #default="{ row }">
              <span class="score-text">{{ row.totalScore }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="grade" label="评价等级" width="100">
            <template #default="{ row }">
              <el-tag :type="getGradeType(row.grade)">{{ row.grade }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="evaluateDate" label="评价时间" width="150" />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="handleViewDetail(row)"
                >查看详情</el-button
              >
              <el-button
                type="warning"
                size="small"
                @click="handleReEvaluate(row)"
                >重新评价</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-card>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";

// 评价维度
const evaluationDimensions = ref([
  {
    id: 1,
    name: "完成时效",
    weight: 30,
    description: "工单按时完成的程度",
  },
  {
    id: 2,
    name: "处理质量",
    weight: 25,
    description: "工单处理的质量和效果",
  },
  {
    id: 3,
    name: "用户满意度",
    weight: 20,
    description: "用户对处理结果的满意程度",
  },
  {
    id: 4,
    name: "沟通效果",
    weight: 15,
    description: "处理过程中的沟通效果",
  },
  {
    id: 5,
    name: "问题解决",
    weight: 10,
    description: "问题是否得到彻底解决",
  },
]);

// 评分规则
const scoringRules = ref([
  {
    id: 1,
    name: "按时完成",
    type: "success",
    score: 100,
    condition: "在预期时间内完成",
  },
  {
    id: 2,
    name: "轻微延期",
    type: "warning",
    score: 80,
    condition: "延期1-2天完成",
  },
  {
    id: 3,
    name: "严重延期",
    type: "danger",
    score: 60,
    condition: "延期3天以上完成",
  },
]);

// 评价等级
const evaluationGrades = ref([
  {
    id: 1,
    name: "优秀",
    type: "success",
    minScore: 90,
    maxScore: 100,
    description: "工单处理优秀，值得表扬",
  },
  {
    id: 2,
    name: "良好",
    type: "primary",
    minScore: 80,
    maxScore: 89,
    description: "工单处理良好，符合要求",
  },
  {
    id: 3,
    name: "一般",
    type: "warning",
    minScore: 70,
    maxScore: 79,
    description: "工单处理一般，有改进空间",
  },
  {
    id: 4,
    name: "较差",
    type: "danger",
    minScore: 0,
    maxScore: 69,
    description: "工单处理较差，需要改进",
  },
]);

// 搜索表单
const searchForm = reactive({
  orderNo: "",
  grade: "",
  dateRange: [],
});

// 评价记录
const evaluationRecords = ref([
  {
    id: 1,
    orderNo: "WO202401001",
    title: "1号泵站设备维修",
    assignee: "张三",
    completedDate: "2024-01-08",
    totalScore: 85,
    grade: "良好",
    evaluateDate: "2024-01-09",
  },
  {
    id: 2,
    orderNo: "WO202401002",
    title: "主管道漏水处理",
    assignee: "李四",
    completedDate: "2024-01-10",
    totalScore: 92,
    grade: "优秀",
    evaluateDate: "2024-01-11",
  },
]);

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0,
});

// 更新权重
const updateWeights = () => {
  const totalWeight = evaluationDimensions.value.reduce(
    (sum, dim) => sum + dim.weight,
    0
  );
  console.log("总权重:", totalWeight);
};

// 获取等级类型
const getGradeType = (
  grade: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const gradeObj = evaluationGrades.value.find((g) => g.name === grade);
  return gradeObj ? getRuleTagType(gradeObj.type) : "info";
};

// 获取规则标签类型
const getRuleTagType = (
  type: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const validTypes: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    success: "success",
    primary: "primary",
    warning: "warning",
    info: "info",
    danger: "danger",
  };
  return validTypes[type] || "info";
};

// 创建模型
const handleCreateModel = () => {
  console.log("创建评价模型");
};

// 添加规则
const handleAddRule = () => {
  console.log("添加评分规则");
};

// 批量评价
const handleBatchEvaluate = () => {
  console.log("批量评价工单");
};

// 搜索
const handleSearch = () => {
  console.log("搜索评价记录", searchForm);
};

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    orderNo: "",
    grade: "",
    dateRange: [],
  });
};

// 查看详情
const handleViewDetail = (row: any) => {
  console.log("查看评价详情", row);
};

// 重新评价
const handleReEvaluate = (row: any) => {
  console.log("重新评价", row);
};

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size;
};

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page;
};

onMounted(() => {
  pagination.total = evaluationRecords.value.length;
});
</script>

<style scoped>
.work-order-evaluation {
  padding: 20px;
}

.page-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.config-card {
  margin-bottom: 20px;
  height: 400px;
}

.dimension-list,
.rules-list,
.grade-list {
  max-height: 300px;
  overflow-y: auto;
}

.dimension-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.dimension-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.dimension-name {
  font-weight: bold;
}

.dimension-weight {
  font-size: 12px;
  color: #409eff;
}

.dimension-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 10px;
}

.rule-item {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.rule-score {
  font-weight: bold;
  color: #409eff;
}

.rule-condition {
  font-size: 12px;
  color: #666;
}

.grade-item {
  margin-bottom: 15px;
  padding: 10px;
  border-left: 4px solid #409eff;
  background-color: #f9f9f9;
}

.grade-header {
  margin-bottom: 5px;
}

.grade-range {
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.grade-desc {
  font-size: 12px;
  color: #666;
}

.stats-section {
  margin: 20px 0;
}

.stats-card {
  margin-bottom: 20px;
}

.chart-container {
  height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  border-radius: 4px;
  color: #999;
}

.table-card {
  margin-top: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.score-text {
  font-weight: bold;
  font-size: 16px;
  color: #409eff;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
