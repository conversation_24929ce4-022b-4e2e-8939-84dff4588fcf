import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { Md5 } from 'ts-md5/dist/md5';
import { Role } from 'src/role.enum';

@Injectable()
export class UserSeedService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async seed() {
    // 检查是否已存在superadmin账号
    const existingSuperAdmin = await this.userRepository.findOne({
      where: { username: 'superadmin' },
    });

    if (!existingSuperAdmin) {
      // 创建superadmin账号
      const superAdmin = new User();
      superAdmin.username = 'superadmin';
      const saltOrRounds = '10';
      const pass = 'xpm@********' + saltOrRounds;
      superAdmin.password = Md5.hashStr(pass);
      superAdmin.roles = Role.SuperAdmin;

      await this.userRepository.save(superAdmin);
      console.log('Superadmin account created successfully');
    }
  }
}
