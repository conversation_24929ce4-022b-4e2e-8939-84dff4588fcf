"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const baseinfo_module_1 = require("./baseinfo/baseinfo.module");
const typeorm_1 = require("@nestjs/typeorm");
const serve_static_1 = require("@nestjs/serve-static");
const path_1 = require("path");
const auth_module_1 = require("./auth/auth.module");
const core_1 = require("@nestjs/core");
const file_module_1 = require("./file/file.module");
const waterstation_module_1 = require("./waterstation/waterstation.module");
const mongoose_1 = require("@nestjs/mongoose");
let AppModule = class AppModule {
};
AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            serve_static_1.ServeStaticModule.forRoot({
                rootPath: (0, path_1.join)(__dirname, '.', 'client'),
            }),
            common_1.CacheModule.register({
                ttl: 1,
                max: 1000,
            }),
            typeorm_1.TypeOrmModule.forRoot({
                type: 'mysql',
                host: 'localhost',
                port: 3311,
                username: 'water_user',
                password: 'water123',
                database: 'water_station_config',
                entities: [__dirname + '/**/entities/*.entity{.ts,.js}'],
                synchronize: true,
                autoLoadEntities: true,
                cache: false,
            }),
            mongoose_1.MongooseModule.forRoot('********************************************************************************'),
            baseinfo_module_1.BaseinfoModule,
            auth_module_1.AuthModule,
            file_module_1.FileModule,
            waterstation_module_1.WaterstationModule,
            typeorm_1.TypeOrmModule.forFeature(),
        ],
        controllers: [app_controller_1.AppController],
        providers: [
            app_service_1.AppService,
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: common_1.CacheInterceptor,
            },
        ],
    })
], AppModule);
exports.AppModule = AppModule;
//# sourceMappingURL=app.module.js.map