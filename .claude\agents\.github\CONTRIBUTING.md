# Contributing to Agents

Thank you for your interest in contributing to this collection of Claude Code subagents! This guide will help you contribute effectively while maintaining a positive community environment.

## Before You Contribute

1. **Read our [Code of Conduct](.github/CODE_OF_CONDUCT.md)** - All interactions must follow our community standards
2. **Search existing issues** - Check if your suggestion or bug report already exists
3. **Use appropriate templates** - Follow the provided issue and PR templates

## Types of Contributions

### Subagent Improvements
- Bug fixes in existing agent prompts
- Performance optimizations
- Enhanced capabilities or instructions
- Documentation improvements

### New Subagents
- Well-defined specialized agents for specific domains
- Clear use cases and examples
- Comprehensive documentation
- Integration with existing workflows

### Infrastructure
- GitHub Actions improvements
- Template enhancements
- Community tooling

## Contribution Process

### 1. Issues First
- **Always create an issue before starting work** on significant changes
- Use the appropriate issue template
- Provide clear, detailed descriptions
- Include relevant examples or use cases

### 2. Pull Requests
- Fork the repository and create a feature branch
- Follow existing code style and formatting
- Include tests or examples where appropriate
- Reference the related issue in your PR description
- Use clear, descriptive commit messages

### 3. Review Process
- All PRs require review from maintainers
- Address feedback promptly and professionally
- Be patient - reviews may take time

## Content Guidelines

### What We Accept
- ✅ Constructive feedback and suggestions
- ✅ Well-researched feature requests
- ✅ Clear bug reports with reproduction steps
- ✅ Professional, respectful communication
- ✅ Documentation improvements
- ✅ Specialized domain expertise

### What We Don't Accept
- ❌ Hate speech, discrimination, or harassment
- ❌ Spam, promotional content, or off-topic posts
- ❌ Personal attacks or inflammatory language
- ❌ Duplicate or low-effort submissions
- ❌ Requests for malicious or harmful capabilities
- ❌ Copyright infringement

## Quality Standards

### For Subagents
- Clear, specific domain expertise
- Well-structured prompt engineering
- Practical use cases and examples
- Appropriate safety considerations
- Integration with existing patterns

### For Documentation
- Clear, concise writing
- Accurate technical information
- Consistent formatting and style
- Practical examples

## Community Guidelines

### Communication
- **Be respectful** - Treat all community members with dignity
- **Be constructive** - Focus on improving the project
- **Be patient** - Allow time for responses and reviews
- **Be helpful** - Share knowledge and assist others

### Collaboration
- **Give credit** - Acknowledge others' contributions
- **Share knowledge** - Help others learn and grow
- **Stay focused** - Keep discussions on topic
- **Follow up** - Respond to feedback and requests

## Getting Help

- 📖 **Documentation**: Check existing README files and agent descriptions
- 💬 **Discussions**: Use GitHub Discussions for questions and brainstorming
- 🐛 **Issues**: Report bugs or request features through issue templates
- 📧 **Direct Contact**: Reach out to maintainers for sensitive matters

## Recognition

Contributors who consistently provide high-quality submissions and maintain professional conduct will be:
- Acknowledged in release notes
- Given priority review for future contributions
- Potentially invited to become maintainers

## Enforcement

Violations of these guidelines may result in:
1. **Warning** - First offense or minor issues
2. **Temporary restrictions** - Suspension of contribution privileges
3. **Permanent ban** - Severe or repeated violations

Reports of violations should be made through:
- GitHub's built-in reporting tools
- Issues tagged with `moderation`
- Direct contact with maintainers

---

Thank you for helping make this project a welcoming, productive environment for everyone!