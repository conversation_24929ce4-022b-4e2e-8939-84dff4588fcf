<template>
  <div class="multi-site-plan-report">
    <el-card shadow="hover" class="page-header">
      <template #header>
        <div class="card-header">
          <div class="header-actions">
            <el-button type="primary" size="small" @click="generatePlanReport"
              >生成计划报表</el-button
            >
            <el-button type="success" size="small" @click="syncPlans"
              >同步计划</el-button
            >
          </div>
        </div>
      </template>

      <!-- 计划配置表单 -->
      <el-form :inline="true" :model="planForm" class="plan-form">
        <el-form-item label="选择站点">
          <el-select
            v-model="planForm.siteIds"
            placeholder="请选择站点"
            multiple
            clearable
          >
            <el-option label="浦南一号泵站" value="pn001" />
            <el-option label="浦南二号泵站" value="pn002" />
            <el-option label="史北一号泵站" value="sb001" />
            <el-option label="史北二号泵站" value="sb002" />
            <el-option label="东部一号泵站" value="eb001" />
            <el-option label="西部一号泵站" value="wb001" />
          </el-select>
        </el-form-item>

        <el-form-item label="计划周期">
          <el-select v-model="planForm.planPeriod" placeholder="请选择计划周期">
            <el-option label="本周计划" value="week" />
            <el-option label="本月计划" value="month" />
            <el-option label="本季度计划" value="quarter" />
            <el-option label="半年计划" value="half_year" />
            <el-option label="年度计划" value="year" />
          </el-select>
        </el-form-item>

        <el-form-item label="维保类型">
          <el-select
            v-model="planForm.maintenanceTypes"
            placeholder="请选择类型"
            multiple
          >
            <el-option label="预防性维保" value="preventive" />
            <el-option label="计划性维保" value="planned" />
            <el-option label="大修" value="overhaul" />
            <el-option label="巡检" value="inspection" />
          </el-select>
        </el-form-item>

        <el-form-item label="计划状态">
          <el-select
            v-model="planForm.planStatus"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="待执行" value="pending" />
            <el-option label="执行中" value="executing" />
            <el-option label="已完成" value="completed" />
            <el-option label="已延期" value="delayed" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 计划执行概览 -->
    <div class="plan-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card total">
            <div class="overview-content">
              <div class="overview-icon">📋</div>
              <div class="overview-data">
                <div class="overview-value">{{ planOverview.totalPlans }}</div>
                <div class="overview-label">总计划数</div>
                <div class="overview-detail">
                  涉及{{ planOverview.totalSites }}个站点
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card completed">
            <div class="overview-content">
              <div class="overview-icon">✅</div>
              <div class="overview-data">
                <div class="overview-value">
                  {{ planOverview.completedPlans }}
                </div>
                <div class="overview-label">已完成</div>
                <div class="overview-detail">
                  完成率{{ planOverview.completionRate }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card executing">
            <div class="overview-content">
              <div class="overview-icon">🔄</div>
              <div class="overview-data">
                <div class="overview-value">
                  {{ planOverview.executingPlans }}
                </div>
                <div class="overview-label">执行中</div>
                <div class="overview-detail">
                  预计{{ planOverview.estimatedDays }}天完成
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card delayed">
            <div class="overview-content">
              <div class="overview-icon">⚠️</div>
              <div class="overview-data">
                <div class="overview-value">
                  {{ planOverview.delayedPlans }}
                </div>
                <div class="overview-label">延期计划</div>
                <div class="overview-detail">需要重新安排</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 计划进度图表 -->
    <div class="plan-charts">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>站点计划执行进度</span>
                <el-button-group size="small">
                  <el-button
                    :type="progressView === 'site' ? 'primary' : ''"
                    @click="progressView = 'site'"
                    >按站点</el-button
                  >
                  <el-button
                    :type="progressView === 'type' ? 'primary' : ''"
                    @click="progressView = 'type'"
                    >按类型</el-button
                  >
                  <el-button
                    :type="progressView === 'time' ? 'primary' : ''"
                    @click="progressView = 'time'"
                    >按时间</el-button
                  >
                </el-button-group>
              </div>
            </template>
            <div id="planProgressChart" style="height: 350px"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>维保计划分布</span>
              </div>
            </template>
            <div id="planDistributionChart" style="height: 350px"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="24">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>维保计划时间轴</span>
                <el-select v-model="timelineView" size="small">
                  <el-option label="本月视图" value="month" />
                  <el-option label="本季度视图" value="quarter" />
                  <el-option label="半年视图" value="half_year" />
                </el-select>
              </div>
            </template>
            <div id="planTimelineChart" style="height: 350px"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 计划详细列表 -->
    <el-card shadow="hover" class="table-card">
      <template #header>
        <div class="card-header">
          <span>多站点维保计划详情</span>
          <div class="table-actions">
            <el-button size="small" @click="exportPlanReport"
              >导出计划</el-button
            >
            <el-button size="small" type="warning" @click="batchReschedule"
              >批量调整</el-button
            >
            <el-button size="small" type="primary" @click="createNewPlan"
              >新建计划</el-button
            >
          </div>
        </div>
      </template>

      <el-table :data="planTableData" stripe style="width: 100%" border>
        <el-table-column type="selection" width="55" />
        <el-table-column
          prop="planId"
          label="计划编号"
          width="120"
          fixed="left"
        />
        <el-table-column prop="siteName" label="站点名称" width="120" />
        <el-table-column prop="equipmentName" label="设备名称" width="120" />
        <el-table-column prop="maintenanceType" label="维保类型" width="100" />
        <el-table-column prop="plannedDate" label="计划日期" width="100" />
        <el-table-column
          prop="estimatedDuration"
          label="预计时长(h)"
          width="120"
        />
        <el-table-column
          prop="assignedTechnician"
          label="负责技师"
          width="100"
        />
        <el-table-column prop="priority" label="优先级" width="80">
          <template #default="scope">
            <el-tag :type="getPriorityType(scope.row.priority)" size="small">
              {{ scope.row.priority }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="planStatus" label="计划状态" width="100">
          <template #default="scope">
            <el-tag :type="getPlanStatusType(scope.row.planStatus)">
              {{ scope.row.planStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="progress" label="执行进度" width="120">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.progress"
              :color="getProgressColor(scope.row.progress)"
            />
          </template>
        </el-table-column>
        <el-table-column
          prop="estimatedCost"
          label="预算成本(元)"
          width="120"
        />
        <el-table-column prop="actualStartDate" label="实际开始" width="100" />
        <el-table-column prop="actualEndDate" label="实际结束" width="100" />
        <el-table-column prop="delayReason" label="延期原因" width="150" />
        <el-table-column prop="notes" label="备注" min-width="120" />
        <el-table-column label="操作" fixed="right" width="200">
          <template #default="scope">
            <el-button size="small" @click="viewPlanDetail(scope.row)"
              >详情</el-button
            >
            <el-button size="small" type="warning" @click="editPlan(scope.row)"
              >编辑</el-button
            >
            <el-button
              size="small"
              type="primary"
              @click="executePlan(scope.row)"
              >执行</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 计划执行建议 -->
    <el-card shadow="hover" class="suggestion-card">
      <template #header>
        <div class="card-header">
          <span>计划执行建议</span>
          <span class="update-time">更新时间: {{ lastUpdateTime }}</span>
        </div>
      </template>

      <el-row :gutter="30">
        <el-col :span="8">
          <div class="suggestion-section">
            <h4>🎯 执行优化</h4>
            <ul class="suggestion-list">
              <li
                v-for="(suggestion, index) in executionOptimization"
                :key="index"
              >
                {{ suggestion }}
              </li>
            </ul>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="suggestion-section">
            <h4>⚠️ 风险提醒</h4>
            <ul class="risk-list">
              <li v-for="(risk, index) in riskReminders" :key="index">
                {{ risk }}
              </li>
            </ul>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="suggestion-section">
            <h4>📊 资源配置</h4>
            <ul class="resource-list">
              <li v-for="(resource, index) in resourceAllocation" :key="index">
                {{ resource }}
              </li>
            </ul>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";

// 计划配置表单
const planForm = reactive({
  siteIds: ["pn001", "sb001", "eb001"],
  planPeriod: "month",
  maintenanceTypes: ["preventive", "planned"],
  planStatus: "",
});

// 图表视图配置
const progressView = ref("site");
const timelineView = ref("month");

// 最后更新时间
const lastUpdateTime = ref("2024-01-20 14:30:25");

// 计划概览数据
const planOverview = reactive({
  totalPlans: 156,
  totalSites: 6,
  completedPlans: 98,
  completionRate: 62.8,
  executingPlans: 35,
  estimatedDays: 12,
  delayedPlans: 23,
});

// 计划详细数据
const planTableData = ref([
  {
    planId: "MP2024010001",
    siteName: "浦南一号泵站",
    equipmentName: "1#主水泵",
    maintenanceType: "预防性维保",
    plannedDate: "2024-01-25",
    estimatedDuration: 4.0,
    assignedTechnician: "张师傅",
    priority: "高",
    planStatus: "待执行",
    progress: 0,
    estimatedCost: 3500,
    actualStartDate: "",
    actualEndDate: "",
    delayReason: "",
    notes: "需要更换密封件",
  },
  {
    planId: "MP2024010002",
    siteName: "史北二号泵站",
    equipmentName: "2#变频器",
    maintenanceType: "计划性维保",
    plannedDate: "2024-01-22",
    estimatedDuration: 6.0,
    assignedTechnician: "李师傅",
    priority: "中",
    planStatus: "执行中",
    progress: 65,
    estimatedCost: 5200,
    actualStartDate: "2024-01-22",
    actualEndDate: "",
    delayReason: "",
    notes: "检查控制参数",
  },
  {
    planId: "MP2024010003",
    siteName: "东部一号泵站",
    equipmentName: "3#控制阀",
    maintenanceType: "预防性维保",
    plannedDate: "2024-01-20",
    estimatedDuration: 3.0,
    assignedTechnician: "王师傅",
    priority: "低",
    planStatus: "已延期",
    progress: 30,
    estimatedCost: 2800,
    actualStartDate: "2024-01-20",
    actualEndDate: "",
    delayReason: "备件缺货",
    notes: "等待备件到货",
  },
]);

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 156,
});

// 执行优化建议
const executionOptimization = ref([
  "合理安排维保顺序，优先处理高优先级设备",
  "统筹安排技师资源，避免人员冲突",
  "提前准备维保材料，减少等待时间",
  "建立维保质量检查机制，确保维保效果",
]);

// 风险提醒
const riskReminders = ref([
  "23个计划存在延期风险，需要重新评估",
  "部分关键设备维保窗口期紧张",
  "技师资源在某些时段可能不足",
  "备件库存不足可能影响维保进度",
]);

// 资源配置建议
const resourceAllocation = ref([
  "增加2名临时技师应对维保高峰期",
  "提前采购关键备件，建立安全库存",
  "优化维保工具配置，提高作业效率",
  "加强与外协单位协调，确保资源到位",
]);

// 获取优先级类型
const getPriorityType = (priority) => {
  const typeMap = {
    高: "danger",
    中: "warning",
    低: "info",
  };
  return typeMap[priority] || "info";
};

// 获取计划状态类型
const getPlanStatusType = (status) => {
  const typeMap = {
    待执行: "info",
    执行中: "warning",
    已完成: "success",
    已延期: "danger",
  };
  return typeMap[status] || "info";
};

// 获取进度颜色
const getProgressColor = (progress) => {
  if (progress >= 80) return "#67c23a";
  if (progress >= 50) return "#e6a23c";
  return "#f56c6c";
};

// 查询处理
const handleQuery = () => {
  ElMessage.success("查询成功");
};

// 重置表单
const resetForm = () => {
  planForm.siteIds = ["pn001", "sb001", "eb001"];
  planForm.planPeriod = "month";
  planForm.maintenanceTypes = ["preventive", "planned"];
  planForm.planStatus = "";
  ElMessage.info("已重置查询条件");
};

// 生成计划报表
const generatePlanReport = () => {
  ElMessage.success("计划报表生成中，请稍候...");
};

// 同步计划
const syncPlans = () => {
  ElMessage.success("计划同步成功");
};

// 查看计划详情
const viewPlanDetail = (row) => {
  ElMessage.info(`查看计划 ${row.planId} 的详细信息`);
};

// 编辑计划
const editPlan = (row) => {
  ElMessage.info(`编辑计划 ${row.planId}`);
};

// 执行计划
const executePlan = (row) => {
  ElMessage.success(`开始执行计划 ${row.planId}`);
};

// 导出计划报表
const exportPlanReport = () => {
  ElMessage.success("计划报表导出成功");
};

// 批量调整
const batchReschedule = () => {
  ElMessage.info("批量调整功能开发中");
};

// 新建计划
const createNewPlan = () => {
  ElMessage.info("新建计划功能开发中");
};

// 分页处理
const handleSizeChange = (size) => {
  pagination.pageSize = size;
};

const handleCurrentChange = (page) => {
  pagination.currentPage = page;
};

// 组件挂载
onMounted(() => {
  initCharts();
});

// 初始化图表
const initCharts = () => {
  console.log("初始化多站点维保计划图表");
};
</script>

<style scoped>
.multi-site-plan-report {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.plan-form {
  margin-bottom: 0;
}

.plan-overview {
  margin-bottom: 20px;
}

.overview-card {
  cursor: pointer;
  transition: all 0.3s;
  border-left: 4px solid;
}

.overview-card.total {
  border-left-color: #409eff;
}

.overview-card.completed {
  border-left-color: #67c23a;
}

.overview-card.executing {
  border-left-color: #e6a23c;
}

.overview-card.delayed {
  border-left-color: #f56c6c;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.overview-content {
  display: flex;
  align-items: center;
  padding: 15px 0;
}

.overview-icon {
  font-size: 32px;
  margin-right: 15px;
}

.overview-data {
  flex: 1;
}

.overview-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.overview-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.overview-detail {
  font-size: 12px;
  color: #909399;
}

.plan-charts {
  margin-bottom: 20px;
}

.chart-card {
  min-height: 450px;
}

.table-card {
  margin-bottom: 20px;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.suggestion-card {
  margin-bottom: 20px;
}

.update-time {
  font-size: 14px;
  color: #666;
  background: #f5f5f5;
  padding: 4px 12px;
  border-radius: 4px;
}

.suggestion-section h4 {
  color: #303133;
  margin-bottom: 15px;
  font-size: 16px;
}

.suggestion-list,
.risk-list,
.resource-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.suggestion-list li,
.risk-list li,
.resource-list li {
  margin-bottom: 12px;
  font-size: 14px;
  line-height: 1.5;
  padding-left: 15px;
  position: relative;
}

.suggestion-list li::before {
  content: "🎯";
  position: absolute;
  left: 0;
}

.risk-list li::before {
  content: "⚠️";
  position: absolute;
  left: 0;
}

.resource-list li::before {
  content: "📊";
  position: absolute;
  left: 0;
}
</style>
