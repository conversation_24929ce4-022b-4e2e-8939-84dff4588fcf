import { Injectable } from '@nestjs/common';
import { CreateMasterDto } from './dto/create-master.dto';
import { UpdateMasterDto } from './dto/update-master.dto';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { Master, MasterDocument } from './master.schema';
@Injectable()
export class MasterService {
  constructor(
    @InjectModel('Master') private masterModel: Model<MasterDocument>,
  ) {}
  async create(createMasterDto: CreateMasterDto): Promise<Master> {
    const createdMaster = new this.masterModel(createMasterDto);
    return createdMaster.save();
  }

  findAll(): Promise<Master[]> {
    return this.masterModel.find({}, ['title', '_id']).exec();
  }

  findOne(id: string): Promise<Master> {
    return this.masterModel.findOne({ _id: id }).exec();
  }

  update(id: string, updateMasterDto: UpdateMasterDto) {
    return this.masterModel
      .updateOne({ _id: id }, { $set: updateMasterDto })
      .exec();
  }

  remove(id: string) {
    return this.masterModel.remove({ _id: id }).exec();
  }
}
