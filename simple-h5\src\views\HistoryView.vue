<!-- /src/views/HistoryView.vue -->
<template>
  <div class="container">
    <div class="header">
      <router-link :to="backRoute" class="back-button">&larr; 返回设备状态</router-link>
      <h1>历史数据 - {{ getStationName() }}</h1>
    </div>

    <!-- 搜索筛选面板 -->
    <div class="filter-panel">
      <div class="filter-header">
        <h3>筛选条件 <span v-if="activeFiltersCount > 0" class="filter-count">({{ activeFiltersCount }})</span></h3>
        <button @click="toggleFilterPanel" class="toggle-button">
          {{ showFilters ? '收起' : '展开' }}
        </button>
      </div>

      <div v-show="showFilters" class="filter-content">
        <!-- 时间范围筛选 -->
        <div class="filter-group">
          <label>时间范围:</label>
          <div class="time-range">
            <input type="datetime-local" v-model="filters.startTime" placeholder="开始时间" />
            <span>至</span>
            <input type="datetime-local" v-model="filters.endTime" placeholder="结束时间" />
          </div>
        </div>

        <!-- 设备状态筛选 -->
        <div class="filter-group">
          <label>设备状态:</label>
          <div class="status-filters">
            <div class="status-item">
              <label>浮球1:</label>
              <select v-model="filters.float1">
                <option value="">全部</option>
                <option value="0">未激活</option>
                <option value="1">激活</option>
              </select>
            </div>
            <div class="status-item">
              <label>水泵1:</label>
              <select v-model="filters.waterPump1Status">
                <option value="">全部</option>
                <option value="0">停止</option>
                <option value="1">运行</option>
              </select>
            </div>
            <div class="status-item">
              <label>水泵2:</label>
              <select v-model="filters.waterPump2Status">
                <option value="">全部</option>
                <option value="0">停止</option>
                <option value="1">运行</option>
              </select>
            </div>
            <div class="status-item">
              <label>气泵1:</label>
              <select v-model="filters.airPump1Status">
                <option value="">全部</option>
                <option value="0">停止</option>
                <option value="1">运行</option>
              </select>
            </div>
            <div class="status-item">
              <label>气泵2:</label>
              <select v-model="filters.airPump2Status">
                <option value="">全部</option>
                <option value="0">停止</option>
                <option value="1">运行</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 温湿度范围筛选 -->
        <div class="filter-group">
          <label>温湿度范围:</label>
          <div class="range-filters">
            <div class="range-item">
              <label>温度 (°C):</label>
              <div class="range-inputs">
                <input type="number" v-model.number="filters.tempMin" placeholder="最低" step="0.1" />
                <span>-</span>
                <input type="number" v-model.number="filters.tempMax" placeholder="最高" step="0.1" />
              </div>
            </div>
            <div class="range-item">
              <label>湿度 (%):</label>
              <div class="range-inputs">
                <input type="number" v-model.number="filters.humidityMin" placeholder="最低" step="0.1" />
                <span>-</span>
                <input type="number" v-model.number="filters.humidityMax" placeholder="最高" step="0.1" />
              </div>
            </div>
          </div>
        </div>

        <!-- 电压范围筛选 -->
        <div class="filter-group">
          <label>电压范围 (V):</label>
          <div class="range-filters">
            <div class="range-item">
              <label>A相电压:</label>
              <div class="range-inputs">
                <input type="number" v-model.number="filters.voltageUaMin" placeholder="最低" step="0.1" />
                <span>-</span>
                <input type="number" v-model.number="filters.voltageUaMax" placeholder="最高" step="0.1" />
              </div>
            </div>
            <div class="range-item">
              <label>B相电压:</label>
              <div class="range-inputs">
                <input type="number" v-model.number="filters.voltageUbMin" placeholder="最低" step="0.1" />
                <span>-</span>
                <input type="number" v-model.number="filters.voltageUbMax" placeholder="最高" step="0.1" />
              </div>
            </div>
            <div class="range-item">
              <label>C相电压:</label>
              <div class="range-inputs">
                <input type="number" v-model.number="filters.voltageUcMin" placeholder="最低" step="0.1" />
                <span>-</span>
                <input type="number" v-model.number="filters.voltageUcMax" placeholder="最高" step="0.1" />
              </div>
            </div>
          </div>
        </div>

        <!-- 电流范围筛选 -->
        <div class="filter-group">
          <label>电流范围 (A):</label>
          <div class="range-filters">
            <div class="range-item">
              <label>A相电流:</label>
              <div class="range-inputs">
                <input type="number" v-model.number="filters.currentIaMin" placeholder="最低" step="0.001" />
                <span>-</span>
                <input type="number" v-model.number="filters.currentIaMax" placeholder="最高" step="0.001" />
              </div>
            </div>
            <div class="range-item">
              <label>B相电流:</label>
              <div class="range-inputs">
                <input type="number" v-model.number="filters.currentIbMin" placeholder="最低" step="0.001" />
                <span>-</span>
                <input type="number" v-model.number="filters.currentIbMax" placeholder="最高" step="0.001" />
              </div>
            </div>
            <div class="range-item">
              <label>C相电流:</label>
              <div class="range-inputs">
                <input type="number" v-model.number="filters.currentIcMin" placeholder="最低" step="0.001" />
                <span>-</span>
                <input type="number" v-model.number="filters.currentIcMax" placeholder="最高" step="0.001" />
              </div>
            </div>
          </div>
        </div>

        <!-- 关键字搜索 -->
        <div class="filter-group">
          <label>关键字搜索:</label>
          <input type="text" v-model="filters.searchKeyword" placeholder="在数据中搜索关键字..." class="search-input" />
        </div>

        <!-- 操作按钮 -->
        <div class="filter-actions">
          <button @click="applyFilters" class="btn-primary">应用筛选</button>
          <button @click="resetFilters" class="btn-secondary">重置</button>
        </div>
      </div>
    </div>

    <div v-if="loading" class="loading-state">正在加载数据...</div>
    <div v-else-if="error" class="error-state">{{ error }}</div>

    <div v-else-if="history.length > 0" class="history-list">
      <div v-for="(record, index) in history" :key="record.id || index" class="card">
        <div class="card-header">记录时间: {{ formatTimestamp(record.timestamp as string) }}</div>
        <div class="card-body">
          <!-- 浮球状态（统一显示） -->
          <div class="float-status-item">
            <span>浮球状态:</span>
            <span :class="getFloatClass(record.float_switches?.float1)">
              {{ getFloatText(record.float_switches?.float1) }}
            </span>
          </div>

          <ul class="details-list">
            <!-- 传感器数据（统一格式） -->
            <li><span>温度:</span> <span>{{ record.wenshidu?.temperature?.toFixed(1) ?? '--' }} °C</span></li>
            <li><span>湿度:</span> <span>{{ record.wenshidu?.humidity?.toFixed(1) ?? '--' }} %</span></li>
            <li><span>A相电流:</span> <span>{{ record.diannengbiao?.currents?.Ia?.toFixed(3) ?? '--' }} A</span></li>
            <li><span>B相电流:</span> <span>{{ record.diannengbiao?.currents?.Ib?.toFixed(3) ?? '--' }} A</span></li>
            <li><span>C相电流:</span> <span>{{ record.diannengbiao?.currents?.Ic?.toFixed(3) ?? '--' }} A</span></li>
            <li><span>总有功功率:</span> <span>{{ record.diannengbiao?.active_power?.total?.toFixed(1) ?? '--' }} W</span>
            </li>
            <li><span>用电度数:</span> <span>{{ getEnergyDisplay(record) }} kWh</span></li>

            <!-- 设备状态（统一格式） -->
            <li>
              <span>水泵1:</span>
              <div class="device-status-info">
                <span :class="getStatusClass(record.water_pump1)" class="knob-status">
                  旋钮档位: {{ getKnobStatus(record.water_pump1) }}
                </span>
                <span class="running-status">
                  运行状态: {{ getRunningStatusText(record.DO21_status) }}
                </span>
              </div>
            </li>
            <li>
              <span>水泵2:</span>
              <div class="device-status-info">
                <span :class="getStatusClass(record.water_pump2)" class="knob-status">
                  旋钮档位: {{ getKnobStatus(record.water_pump2) }}
                </span>
                <span class="running-status">
                  运行状态: {{ getRunningStatusText(record.DO22_status) }}
                </span>
              </div>
            </li>
            <li>
              <span>气泵1:</span>
              <div class="device-status-info">
                <span :class="getStatusClass(record.air_pump1)" class="knob-status">
                  旋钮档位: {{ getKnobStatus(record.air_pump1) }}
                </span>
                <span class="running-status">
                  运行状态: {{ getRunningStatusText(record.DO23_status) }}
                </span>
              </div>
            </li>
            <li>
              <span>气泵2:</span>
              <div class="device-status-info">
                <span :class="getStatusClass(record.air_pump2)" class="knob-status">
                  旋钮档位: {{ getKnobStatus(record.air_pump2) }}
                </span>
                <span class="running-status">
                  运行状态: {{ getRunningStatusText(record.DO24_status) }}
                </span>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <div class="pagination">
        <button @click="loadPreviousPage" :disabled="page <= 1">&larr;</button>
        <span v-if="totalPages > 0">第 {{ page }} / {{ totalPages }} 页</span>
        <div class="pagination-jump">
          <input type="number" v-model.number="jumpToPage" min="1" :max="totalPages" @keyup.enter="goToPage" />
          <button @click="goToPage">跳转</button>
        </div>
        <button @click="loadNextPage" :disabled="page >= totalPages">&rarr;</button>
      </div>
    </div>

    <div v-else class="empty-state">
      <p>没有找到任何历史数据。</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';

interface DeviceStatus {
  auto_status: number;
  stop_status: number;
  manual_status: number;
}

interface DeviceData {
  id?: number;
  timestamp?: string; // 从外层记录中添加
  // 使用 Partial<> 使所有属性变为可选，以处理不同记录中字段不一致的情况
  device_info?: { timestamp: string;[key: string]: unknown };
  wenshidu?: { temperature: number; humidity: number };
  diannengbiao?: {
    voltages: { Ua: number; Ub: number; Uc: number };
    currents: { Ia: number; Ib: number; Ic: number };
    active_power: { total: number };
    active_energy?: number;
    reverse_active_energy?: number;
    [key: string]: unknown;
  };
  float_switches?: { float1: number };
  water_pump1?: DeviceStatus;
  water_pump2?: DeviceStatus;
  air_pump1?: DeviceStatus;
  air_pump2?: DeviceStatus;
  // DO状态字段
  DO21_status?: number;
  DO22_status?: number;
  DO23_status?: number;
  DO24_status?: number;
  [key: string]: unknown;
}

const history = ref<DeviceData[]>([]);
const loading = ref(true);
const error = ref<string | null>(null);
const page = ref(1);
const limit = 10; // 每页记录数
const totalPages = ref(0);
const jumpToPage = ref(1);

// 获取路由信息
const route = useRoute();
const currentSn = route.query.sn as string || route.params.stationId as string || '02800125081400008508';

// 返回路由
const backRoute = computed(() => {
  if (route.params.stationId) {
    return {
      name: 'device',
      params: { stationId: route.params.stationId },
      query: route.query
    };
  }
  return '/';
});

// 获取站点名称
function getStationName(): string {
  const stationName = route.query.stationName as string;
  if (stationName) {
    return stationName;
  }

  // 根据SN返回默认名称
  if (currentSn === '02800125081400008508') {
    return '联丰村';
  } else if (currentSn === '02800125071500004977') {
    return '大船港村曹村';
  }

  return `设备 ${currentSn}`;
}

// 筛选相关的响应式变量
const showFilters = ref(false);
const filters = ref({
  startTime: '',
  endTime: '',
  float1: '',
  waterPump1Status: '',
  waterPump2Status: '',
  airPump1Status: '',
  airPump2Status: '',
  searchKeyword: '',
  // 温湿度范围
  tempMin: null as number | null,
  tempMax: null as number | null,
  humidityMin: null as number | null,
  humidityMax: null as number | null,
  // 电压范围
  voltageUaMin: null as number | null,
  voltageUaMax: null as number | null,
  voltageUbMin: null as number | null,
  voltageUbMax: null as number | null,
  voltageUcMin: null as number | null,
  voltageUcMax: null as number | null,
  // 电流范围
  currentIaMin: null as number | null,
  currentIaMax: null as number | null,
  currentIbMin: null as number | null,
  currentIbMax: null as number | null,
  currentIcMin: null as number | null,
  currentIcMax: null as number | null
});

async function fetchHistoryData() {
  loading.value = true;
  error.value = null;

  // 从路由参数获取SN
  const route = useRoute();
  const sn = route.query.sn as string || route.params.stationId as string || '02800125081400008508';
  const baseUrl = 'http://49.235.191.145:8500';

  // 使用新的JSON文件接口
  const apiUrl = `${baseUrl}/device-data/${sn}.json`;

  try {
    const response = await fetch(apiUrl);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const jsonData: DeviceData[] = await response.json();

    if (!jsonData || jsonData.length === 0) {
      history.value = [];
      totalPages.value = 0;
      return;
    }

    // 对数据进行筛选
    let filteredData = jsonData.filter(record => {
      // 时间范围筛选
      if (filters.value.startTime) {
        const recordTime = new Date(record.timestamp || '');
        const startTime = new Date(filters.value.startTime);
        if (recordTime < startTime) return false;
      }
      if (filters.value.endTime) {
        const recordTime = new Date(record.timestamp || '');
        const endTime = new Date(filters.value.endTime);
        if (recordTime > endTime) return false;
      }

      // 浮球状态筛选
      if (filters.value.float1) {
        if (record.float_switches?.float1?.toString() !== filters.value.float1) return false;
      }

      // 设备状态筛选
      if (filters.value.waterPump1Status) {
        if (record.DO21_status?.toString() !== filters.value.waterPump1Status) return false;
      }
      if (filters.value.waterPump2Status) {
        if (record.DO22_status?.toString() !== filters.value.waterPump2Status) return false;
      }
      if (filters.value.airPump1Status) {
        if (record.DO23_status?.toString() !== filters.value.airPump1Status) return false;
      }
      if (filters.value.airPump2Status) {
        if (record.DO24_status?.toString() !== filters.value.airPump2Status) return false;
      }

      // 关键字搜索
      if (filters.value.searchKeyword) {
        const searchText = filters.value.searchKeyword.toLowerCase();
        const recordStr = JSON.stringify(record).toLowerCase();
        if (!recordStr.includes(searchText)) return false;
      }

      // 温度筛选
      if (filters.value.tempMin !== null && record.wenshidu?.temperature !== undefined) {
        if (record.wenshidu.temperature < filters.value.tempMin) return false;
      }
      if (filters.value.tempMax !== null && record.wenshidu?.temperature !== undefined) {
        if (record.wenshidu.temperature > filters.value.tempMax) return false;
      }

      // 湿度筛选
      if (filters.value.humidityMin !== null && record.wenshidu?.humidity !== undefined) {
        if (record.wenshidu.humidity < filters.value.humidityMin) return false;
      }
      if (filters.value.humidityMax !== null && record.wenshidu?.humidity !== undefined) {
        if (record.wenshidu.humidity > filters.value.humidityMax) return false;
      }

      // A相电压筛选
      if (filters.value.voltageUaMin !== null && record.diannengbiao?.voltages?.Ua !== undefined) {
        if (record.diannengbiao.voltages.Ua < filters.value.voltageUaMin) return false;
      }
      if (filters.value.voltageUaMax !== null && record.diannengbiao?.voltages?.Ua !== undefined) {
        if (record.diannengbiao.voltages.Ua > filters.value.voltageUaMax) return false;
      }

      // B相电压筛选
      if (filters.value.voltageUbMin !== null && record.diannengbiao?.voltages?.Ub !== undefined) {
        if (record.diannengbiao.voltages.Ub < filters.value.voltageUbMin) return false;
      }
      if (filters.value.voltageUbMax !== null && record.diannengbiao?.voltages?.Ub !== undefined) {
        if (record.diannengbiao.voltages.Ub > filters.value.voltageUbMax) return false;
      }

      // C相电压筛选
      if (filters.value.voltageUcMin !== null && record.diannengbiao?.voltages?.Uc !== undefined) {
        if (record.diannengbiao.voltages.Uc < filters.value.voltageUcMin) return false;
      }
      if (filters.value.voltageUcMax !== null && record.diannengbiao?.voltages?.Uc !== undefined) {
        if (record.diannengbiao.voltages.Uc > filters.value.voltageUcMax) return false;
      }

      // A相电流筛选
      if (filters.value.currentIaMin !== null && record.diannengbiao?.currents?.Ia !== undefined) {
        if (record.diannengbiao.currents.Ia < filters.value.currentIaMin) return false;
      }
      if (filters.value.currentIaMax !== null && record.diannengbiao?.currents?.Ia !== undefined) {
        if (record.diannengbiao.currents.Ia > filters.value.currentIaMax) return false;
      }

      // B相电流筛选
      if (filters.value.currentIbMin !== null && record.diannengbiao?.currents?.Ib !== undefined) {
        if (record.diannengbiao.currents.Ib < filters.value.currentIbMin) return false;
      }
      if (filters.value.currentIbMax !== null && record.diannengbiao?.currents?.Ib !== undefined) {
        if (record.diannengbiao.currents.Ib > filters.value.currentIbMax) return false;
      }

      // C相电流筛选
      if (filters.value.currentIcMin !== null && record.diannengbiao?.currents?.Ic !== undefined) {
        if (record.diannengbiao.currents.Ic < filters.value.currentIcMin) return false;
      }
      if (filters.value.currentIcMax !== null && record.diannengbiao?.currents?.Ic !== undefined) {
        if (record.diannengbiao.currents.Ic > filters.value.currentIcMax) return false;
      }

      return true;
    });

    // 按时间倒序排列
    filteredData.sort((a, b) => {
      const timeA = new Date(a.timestamp || '').getTime();
      const timeB = new Date(b.timestamp || '').getTime();
      return timeB - timeA;
    });

    // 客户端分页
    const totalRecords = filteredData.length;
    totalPages.value = Math.ceil(totalRecords / limit);

    const startIndex = (page.value - 1) * limit;
    const endIndex = startIndex + limit;
    history.value = filteredData.slice(startIndex, endIndex);

  } catch (e) {
    error.value = `获取历史数据失败: ${e instanceof Error ? e.message : String(e)}`;
    console.error(e);
    history.value = [];
    totalPages.value = 0;
  } finally {
    loading.value = false;
    jumpToPage.value = page.value;
  }
}

function loadNextPage() {
  if (page.value < totalPages.value) {
    page.value++;
    fetchHistoryData();
  }
}

function loadPreviousPage() {
  if (page.value > 1) {
    page.value--;
    fetchHistoryData();
  }
}

function goToPage() {
  const pageNum = Math.floor(jumpToPage.value);
  if (pageNum >= 1 && pageNum <= totalPages.value && pageNum !== page.value) {
    page.value = pageNum;
    fetchHistoryData();
  } else {
    jumpToPage.value = page.value;
  }
}

// 筛选相关函数
function toggleFilterPanel() {
  showFilters.value = !showFilters.value;
}

function applyFilters() {
  page.value = 1; // 重置到第一页
  fetchHistoryData();
}

function resetFilters() {
  filters.value = {
    startTime: '',
    endTime: '',
    float1: '',
    waterPump1Status: '',
    waterPump2Status: '',
    airPump1Status: '',
    airPump2Status: '',
    searchKeyword: '',
    // 温湿度范围
    tempMin: null,
    tempMax: null,
    humidityMin: null,
    humidityMax: null,
    // 电压范围
    voltageUaMin: null,
    voltageUaMax: null,
    voltageUbMin: null,
    voltageUbMax: null,
    voltageUcMin: null,
    voltageUcMax: null,
    // 电流范围
    currentIaMin: null,
    currentIaMax: null,
    currentIbMin: null,
    currentIbMax: null,
    currentIcMin: null,
    currentIcMax: null
  };
  page.value = 1; // 重置到第一页
  fetchHistoryData();
}

// 计算当前激活的筛选条件数量
const activeFiltersCount = computed(() => {
  let count = 0;
  if (filters.value.startTime) count++;
  if (filters.value.endTime) count++;
  if (filters.value.float1) count++;
  if (filters.value.waterPump1Status) count++;
  if (filters.value.waterPump2Status) count++;
  if (filters.value.airPump1Status) count++;
  if (filters.value.airPump2Status) count++;
  if (filters.value.searchKeyword) count++;
  // 温湿度范围
  if (filters.value.tempMin !== null) count++;
  if (filters.value.tempMax !== null) count++;
  if (filters.value.humidityMin !== null) count++;
  if (filters.value.humidityMax !== null) count++;
  // 电压范围
  if (filters.value.voltageUaMin !== null) count++;
  if (filters.value.voltageUaMax !== null) count++;
  if (filters.value.voltageUbMin !== null) count++;
  if (filters.value.voltageUbMax !== null) count++;
  if (filters.value.voltageUcMin !== null) count++;
  if (filters.value.voltageUcMax !== null) count++;
  // 电流范围
  if (filters.value.currentIaMin !== null) count++;
  if (filters.value.currentIaMax !== null) count++;
  if (filters.value.currentIbMin !== null) count++;
  if (filters.value.currentIbMax !== null) count++;
  if (filters.value.currentIcMin !== null) count++;
  if (filters.value.currentIcMax !== null) count++;
  return count;
});

function formatTimestamp(ts: string) {
  if (!ts) return 'N/A';
  // 强制将时间字符串解析为UTC时间，然后再转换为中国时区 (UTC+8)
  return new Date(ts + 'Z').toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
}

/**
 * 获取旋钮开关的文本状态
 */
function getKnobStatus(deviceStatus?: DeviceStatus): '自动' | '停止' | '手动' | '--' {
  if (!deviceStatus) return '--'
  if (deviceStatus.auto_status === 1) return '自动'
  if (deviceStatus.stop_status === 1) return '停止'
  if (deviceStatus.manual_status === 1) return '手动'
  return '--'
}

/**
 * 获取旋钮开关的CSS类
 */
function getStatusClass(deviceStatus?: DeviceStatus): string {
  const status = getKnobStatus(deviceStatus);
  switch (status) {
    case '自动': return 'status-auto';
    case '停止': return 'status-stop';
    case '手动': return 'status-manual';
    default: return '';
  }
}

/**
 * 获取设备运行状态文本
 */
function getRunningStatusText(doStatus?: number): '运行中' | '已停止' | '--' {
  if (doStatus === undefined || doStatus === null) return '--';
  return doStatus === 1 ? '运行中' : '已停止';
}

/**
 * 获取浮球状态文本
 */
function getFloatText(floatStatus?: number): '激活' | '未激活' {
  return floatStatus === 1 ? '激活' : '未激活';
}

/**
 * 获取浮球状态CSS类
 */
function getFloatClass(floatStatus?: number): string {
  return floatStatus === 1 ? 'status-active' : '';
}

/**
 * 获取用电度数显示
 */
function getEnergyDisplay(record: DeviceData): string {
  return record.diannengbiao?.active_energy?.toFixed(2) ?? '--';
}

onMounted(fetchHistoryData);
</script>

<style scoped>
:root {
  --primary-color: #007bff;
  --bg-color: #f0f2f5;
  --card-bg-color: #ffffff;
  --card-header-bg-color: #f8f9fa;
  --text-color: #333;
  --text-light-color: #666;
}

.container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.back-button {
  padding: 8px 16px;
  background-color: var(--primary-color);
  color: #0056b3;
  text-decoration: none;
  border-radius: 6px;
  margin-right: 20px;
  transition: background-color 0.3s;
}

h1 {
  color: var(--text-color);
}

/* 筛选面板样式 */
.filter-panel {
  background-color: var(--card-bg-color);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  overflow: hidden;
}

.filter-header {
  background-color: var(--card-header-bg-color);
  padding: 12px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e9ecef;
}

.filter-header h3 {
  margin: 0;
  color: var(--text-color);
  font-size: 16px;
}

.filter-count {
  color: var(--primary-color);
  font-weight: normal;
  font-size: 14px;
}

.toggle-button {
  background: var(--primary-color);
  color: #0056b3;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.filter-content {
  padding: 20px;
}

.filter-group {
  margin-bottom: 20px;
}

.filter-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: var(--text-color);
}

.time-range {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.time-range input {
  flex: 1;
  min-width: 200px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.time-range span {
  color: var(--text-light-color);
  font-weight: normal;
}

.status-filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.status-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.status-item label {
  font-size: 14px;
  margin-bottom: 0;
}

.status-item select {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.range-filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.range-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.range-item label {
  font-size: 14px;
  margin-bottom: 0;
  color: var(--text-color);
}

.range-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.range-inputs input {
  flex: 1;
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  min-width: 80px;
}

.range-inputs span {
  color: var(--text-light-color);
  font-weight: normal;
  font-size: 14px;
}

.filter-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.btn-secondary:hover {
  background: #545b62;
}

.loading-state,
.error-state,
.empty-state {
  text-align: center;
  padding: 40px;
  font-size: 18px;
  color: var(--text-light-color);
}

.error-state {
  color: #dc3545;
}

.card {
  background-color: var(--card-bg-color);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  overflow: hidden;
}

.card-header {
  background-color: var(--card-header-bg-color);
  padding: 12px 20px;
  font-weight: bold;
  border-bottom: 1px solid #e9ecef;
}

.card-body {
  padding: 20px;
}

.details-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 10px 20px;
}

.details-list li {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.details-list span:first-child {
  color: var(--text-light-color);
}

.details-list span:last-child {
  font-weight: 500;
}

.float-status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  /* 与details-list的gap保持一致 */
}

.float-status-item span:first-child {
  color: var(--text-light-color);
}

.float-status-item span:last-child {
  font-weight: 500;
}

/* 状态高亮样式 */
.status-auto {
  color: #007bff;
}

.status-manual {
  color: #28a745;
}

.status-stop {
  color: #6c757d;
}

.status-active {
  color: #ffc107;
  font-weight: bold;
}

/* 设备状态信息样式 */
.device-status-info {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
}

.knob-status {
  font-size: 13px;
  font-weight: 500;
}

.running-status {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-color);
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  gap: 15px;
}

.pagination-jump {
  display: flex;
  align-items: center;
}

.pagination-jump input {
  width: 60px;
  padding: 8px;
  text-align: center;
  border: 1px solid #ced4da;
  border-radius: 6px;
  margin-right: 8px;
}

.pagination button {
  padding: 8px 12px;
  border: 1px solid var(--primary-color);
  background-color: #fff;
  color: var(--primary-color);
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
}

.pagination>button {
  font-size: 1.2rem;
  font-weight: bold;
}

.pagination button:hover:not(:disabled) {
  background-color: var(--primary-color);
  color: #fff;
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
