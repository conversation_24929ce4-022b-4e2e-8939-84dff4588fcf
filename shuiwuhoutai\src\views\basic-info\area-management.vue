<template>
  <div class="area-management-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="header-actions">
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              新增区域
            </el-button>
            <el-button @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form">
        <el-form :model="searchForm" inline>
          <el-form-item label="区域名称">
            <el-input
              v-model="searchForm.areaName"
              placeholder="请输入区域名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="区域类型">
            <el-select
              v-model="searchForm.areaType"
              placeholder="请选择区域类型"
              clearable
              style="width: 150px"
            >
              <el-option label="行政区域" value="administrative" />
              <el-option label="功能区域" value="functional" />
              <el-option label="地理区域" value="geographical" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 120px"
            >
              <el-option label="启用" value="1" />
              <el-option label="禁用" value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 区域展示 -->
      <div class="area-display">
        <div class="tree-container">
          <h4>区域层级结构</h4>
          <el-tree
            ref="treeRef"
            :data="treeData"
            :props="treeProps"
            default-expand-all
            highlight-current
            node-key="id"
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
          >
            <template #default="{ data }">
              <div class="custom-tree-node">
                <div class="node-label">
                  <el-icon><MapLocation /></el-icon>
                  <span>{{ data.label }}</span>
                  <el-tag
                    :type="getTypeTagType(data.type)"
                    size="small"
                    style="margin-left: 8px"
                  >
                    {{ getTypeLabel(data.type) }}
                  </el-tag>
                </div>
                <div class="node-actions">
                  <el-button
                    size="small"
                    type="primary"
                    text
                    @click.stop="handleEdit(data)"
                  >
                    <el-icon><Edit /></el-icon>
                  </el-button>
                  <el-button
                    size="small"
                    type="success"
                    text
                    @click.stop="handleAddChild(data)"
                  >
                    <el-icon><Plus /></el-icon>
                  </el-button>
                  <el-button
                    size="small"
                    type="danger"
                    text
                    @click.stop="handleDelete(data)"
                    v-if="!data.children || data.children.length === 0"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 区域详细信息 -->
      <div class="area-details" v-if="selectedArea">
        <el-divider>区域详细信息</el-divider>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="区域名称">{{
            selectedArea.label
          }}</el-descriptions-item>
          <el-descriptions-item label="区域编码">{{
            selectedArea.code || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="区域类型">{{
            getTypeLabel(selectedArea.type)
          }}</el-descriptions-item>
          <el-descriptions-item label="面积"
            >{{ selectedArea.area || "-" }} 平方公里</el-descriptions-item
          >
          <el-descriptions-item label="人口"
            >{{ selectedArea.population || "-" }} 人</el-descriptions-item
          >
          <el-descriptions-item label="状态">
            <el-tag :type="selectedArea.status === '1' ? 'success' : 'danger'">
              {{ selectedArea.status === "1" ? "启用" : "禁用" }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="负责人">{{
            selectedArea.manager || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{
            selectedArea.phone || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{
            selectedArea.createTime || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="描述" :span="3">
            {{ selectedArea.description || "-" }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="上级区域" prop="parentName">
              <el-input v-model="formData.parentName" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="区域名称" prop="areaName">
              <el-input
                v-model="formData.areaName"
                placeholder="请输入区域名称"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="区域编码" prop="areaCode">
              <el-input
                v-model="formData.areaCode"
                placeholder="请输入区域编码"
                maxlength="20"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="区域类型" prop="areaType">
              <el-select
                v-model="formData.areaType"
                placeholder="请选择区域类型"
                style="width: 100%"
              >
                <el-option label="行政区域" value="administrative" />
                <el-option label="功能区域" value="functional" />
                <el-option label="地理区域" value="geographical" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="面积(平方公里)" prop="area">
              <el-input-number
                v-model="formData.area"
                :min="0"
                :precision="2"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="人口(人)" prop="population">
              <el-input-number
                v-model="formData.population"
                :min="0"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="负责人" prop="manager">
              <el-input
                v-model="formData.manager"
                placeholder="请输入负责人姓名"
                maxlength="20"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input
                v-model="formData.phone"
                placeholder="请输入联系电话"
                maxlength="20"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="排序" prop="sort">
              <el-input-number
                v-model="formData.sort"
                :min="0"
                :max="999"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="formData.status">
                <el-radio label="1">启用</el-radio>
                <el-radio label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入区域描述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleSubmit"
            :loading="submitLoading"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Plus,
  Refresh,
  Search,
  Edit,
  Delete,
  MapLocation,
} from "@element-plus/icons-vue";

// 搜索表单
const searchForm = reactive({
  areaName: "",
  areaType: "",
  status: "",
});

// 选中的区域
const selectedArea = ref<any>(null);

// 树形数据
const treeRef = ref();
const treeData = ref([
  {
    id: "1",
    label: "吴江区",
    type: "administrative",
    status: "1",
    parentId: "0",
    code: "WJ001",
    area: 1176.68,
    population: 1300000,
    manager: "张三",
    phone: "0512-63498000",
    createTime: "2023-01-01",
    description: "吴江区行政区域",
    children: [
      {
        id: "2",
        label: "松陵镇",
        type: "administrative",
        status: "1",
        parentId: "1",
        code: "SLZ001",
        area: 98.5,
        population: 280000,
        manager: "李四",
        phone: "0512-63421234",
        createTime: "2023-01-02",
        description: "松陵镇行政区域",
        children: [
          {
            id: "3",
            label: "东风村",
            type: "administrative",
            status: "1",
            parentId: "2",
            code: "DFC001",
            area: 12.3,
            population: 5600,
            manager: "王五",
            phone: "0512-63431111",
            createTime: "2023-01-03",
            description: "东风村行政区域",
            children: [],
          },
          {
            id: "4",
            label: "建设村",
            type: "administrative",
            status: "1",
            parentId: "2",
            code: "JSC001",
            area: 15.2,
            population: 6800,
            manager: "赵六",
            phone: "0512-63432222",
            createTime: "2023-01-04",
            description: "建设村行政区域",
            children: [],
          },
        ],
      },
      {
        id: "5",
        label: "盛泽镇",
        type: "administrative",
        status: "1",
        parentId: "1",
        code: "SZZ001",
        area: 135.8,
        population: 320000,
        manager: "孙七",
        phone: "0512-63751234",
        createTime: "2023-01-05",
        description: "盛泽镇行政区域",
        children: [
          {
            id: "6",
            label: "胜利村",
            type: "administrative",
            status: "1",
            parentId: "5",
            code: "SLC001",
            area: 18.5,
            population: 8200,
            manager: "周八",
            phone: "0512-63753333",
            createTime: "2023-01-06",
            description: "胜利村行政区域",
            children: [],
          },
        ],
      },
      {
        id: "7",
        label: "黎里镇",
        type: "administrative",
        status: "1",
        parentId: "1",
        code: "LLZ001",
        area: 84.2,
        population: 180000,
        manager: "吴九",
        phone: "0512-63821234",
        createTime: "2023-01-07",
        description: "黎里镇行政区域",
        children: [],
      },
      {
        id: "8",
        label: "开发区",
        type: "functional",
        status: "1",
        parentId: "1",
        code: "KFQ001",
        area: 125.6,
        population: 95000,
        manager: "郑十",
        phone: "0512-63881234",
        createTime: "2023-01-08",
        description: "吴江开发区功能区域",
        children: [],
      },
    ],
  },
]);

const treeProps = {
  children: "children",
  label: "label",
};

// 对话框
const dialogVisible = ref(false);
const dialogTitle = ref("");
const submitLoading = ref(false);
const formRef = ref();

const formData = reactive({
  id: "",
  parentId: "",
  parentName: "",
  areaName: "",
  areaCode: "",
  areaType: "",
  area: 0,
  population: 0,
  manager: "",
  phone: "",
  sort: 0,
  status: "1",
  description: "",
});

const formRules = {
  areaName: [
    { required: true, message: "请输入区域名称", trigger: "blur" },
    { min: 2, max: 50, message: "长度在 2 到 50 个字符", trigger: "blur" },
  ],
  areaCode: [
    { required: true, message: "请输入区域编码", trigger: "blur" },
    {
      pattern: /^[A-Za-z0-9]+$/,
      message: "编码只能包含字母和数字",
      trigger: "blur",
    },
  ],
  areaType: [{ required: true, message: "请选择区域类型", trigger: "change" }],
  manager: [{ required: true, message: "请输入负责人姓名", trigger: "blur" }],
};

// 方法
const getTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    administrative: "行政区域",
    functional: "功能区域",
    geographical: "地理区域",
  };
  return typeMap[type] || "未知";
};

const getTypeTagType = (
  type: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const typeTagMap: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    administrative: "primary",
    functional: "success",
    geographical: "warning",
  };
  return typeTagMap[type] || "info";
};

const handleSearch = () => {
  // TODO: 实现搜索逻辑
  console.log("搜索条件:", searchForm);
  ElMessage.success("搜索功能待实现");
};

const handleReset = () => {
  searchForm.areaName = "";
  searchForm.areaType = "";
  searchForm.status = "";
  ElMessage.success("搜索条件已重置");
};

const refreshData = () => {
  // TODO: 实现数据刷新
  ElMessage.success("数据已刷新");
};

const handleNodeClick = (data: any) => {
  selectedArea.value = data;
  console.log("选中区域:", data);
};

const handleAdd = () => {
  dialogTitle.value = "新增区域";
  resetFormData();
  formData.parentName = "根区域";
  formData.parentId = "0";
  dialogVisible.value = true;
};

const handleAddChild = (data: any) => {
  dialogTitle.value = "新增子区域";
  resetFormData();
  formData.parentName = data.label;
  formData.parentId = data.id;
  dialogVisible.value = true;
};

const handleEdit = (data: any) => {
  dialogTitle.value = "编辑区域";
  formData.id = data.id;
  formData.parentId = data.parentId;
  formData.areaName = data.label;
  formData.areaCode = data.code || "";
  formData.areaType = data.type;
  formData.area = data.area || 0;
  formData.population = data.population || 0;
  formData.manager = data.manager || "";
  formData.phone = data.phone || "";
  formData.sort = data.sort || 0;
  formData.status = data.status;
  formData.description = data.description || "";

  // 查找父级名称
  const findParentName = (nodes: any[], parentId: string): string => {
    for (const node of nodes) {
      if (node.id === parentId) {
        return node.label;
      }
      if (node.children?.length > 0) {
        const result = findParentName(node.children, parentId);
        if (result) return result;
      }
    }
    return parentId === "0" ? "根区域" : "未知";
  };

  formData.parentName = findParentName(treeData.value, data.parentId);
  dialogVisible.value = true;
};

const handleDelete = (data: any) => {
  ElMessageBox.confirm(`确认删除区域 "${data.label}" 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // TODO: 实现删除逻辑
      ElMessage.success("删除成功");
    })
    .catch(() => {
      ElMessage.info("已取消删除");
    });
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitLoading.value = true;

    // TODO: 实现提交逻辑
    await new Promise((resolve) => setTimeout(resolve, 1000));

    ElMessage.success(
      dialogTitle.value.includes("新增") ? "新增成功" : "修改成功"
    );
    dialogVisible.value = false;
    refreshData();
  } catch (error) {
    console.error("表单验证失败:", error);
  } finally {
    submitLoading.value = false;
  }
};

const handleDialogClose = () => {
  resetFormData();
  formRef.value?.clearValidate();
};

const resetFormData = () => {
  formData.id = "";
  formData.parentId = "";
  formData.parentName = "";
  formData.areaName = "";
  formData.areaCode = "";
  formData.areaType = "";
  formData.area = 0;
  formData.population = 0;
  formData.manager = "";
  formData.phone = "";
  formData.sort = 0;
  formData.status = "1";
  formData.description = "";
};

onMounted(() => {
  // 初始化数据
  refreshData();
});
</script>

<style scoped>
.area-management-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.area-display {
  margin-top: 20px;
}

.tree-container {
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  width: 100%;
}

.tree-container h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.node-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.node-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.area-details {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-tree-node__content) {
  height: 40px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}
</style>
