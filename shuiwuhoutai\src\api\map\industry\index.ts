import request from "@/utils/request";
import { UserQuery } from "./model";

class IndustryAPI {
  // 获取企业分页列表
  static getCompany(queryParams: UserQuery) {
    return request<any, any>({
      url: "/companydetail",
      method: "get",
      params: queryParams,
    });
  }

  // 获取企业表单详情
  static getCompanyFormData(userId: number) {
    return request<any, any>({
      url: "/companydetail/" + userId,
      method: "get",
    });
  }

  // 添加企业
  static addCompany(data: any) {
    return request({
      url: "/companydetail",
      method: "post",
      data: data,
    });
  }

  // 修改企业
  static updateCompany(id: number, data: any) {
    return request({
      url: "/companydetail/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除企业
  static deleteCompanyByIds(ids: string) {
    return request({
      url: "/companydetail/" + ids,
      method: "delete",
    });
  }

  // 导入企业信息
  static importCompany(file: FormData) {
    return request({
      url: "/companydetail/import",
      method: "post",
      data: file,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  // 获取经营性收入分页列表
  static getIncome(queryParams: UserQuery) {
    return request<any, any>({
      url: "/income",
      method: "get",
      params: queryParams,
    });
  }

  // 获取经营性收入表单详情
  static getIncomeFormData(userId: number) {
    return request<any, any>({
      url: "/income/" + userId,
      method: "get",
    });
  }

  // 添加经营性收入
  static addIncome(data: any) {
    return request({
      url: "/income",
      method: "post",
      data: data,
    });
  }

  // 修改经营性收入
  static updateIncome(id: number, data: any) {
    return request({
      url: "/income/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除经营性收入
  static deleteIncomeByIds(ids: string) {
    return request({
      url: "/income/" + ids,
      method: "delete",
    });
  }

  // 获取汇总数据分页列表
  static getSummary(queryParams: UserQuery) {
    return request<any, any>({
      url: "/pooled",
      method: "get",
      params: queryParams,
    });
  }

  // 获取汇总数据表单详情
  static getSummaryFormData(userId: number) {
    return request<any, any>({
      url: "/pooled/" + userId,
      method: "get",
    });
  }

  // 添加汇总数据
  static addSummary(data: any) {
    return request({
      url: "/pooled",
      method: "post",
      data: data,
    });
  }

  // 修改汇总数据
  static updateSummary(id: number, data: any) {
    return request({
      url: "/pooled/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除汇总数据
  static deleteSummaryByIds(ids: string) {
    return request({
      url: "/pooled/" + ids,
      method: "delete",
    });
  }
}

export default IndustryAPI;
