<template>
  <div class="pipeline-inspection-strategy">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <el-button type="primary" @click="handleCreateStrategy"
            >制定策略</el-button
          >
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="策略名称">
          <el-input
            v-model="searchForm.strategyName"
            placeholder="请输入策略名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="适用范围">
          <el-select
            v-model="searchForm.scope"
            placeholder="请选择范围"
            clearable
          >
            <el-option label="全市范围" value="city" />
            <el-option label="中心城区" value="center" />
            <el-option label="工业区" value="industrial" />
          </el-select>
        </el-form-item>
        <el-form-item label="策略状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="草稿" value="draft" />
            <el-option label="已发布" value="published" />
            <el-option label="已停用" value="disabled" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 策略列表 -->
      <el-table :data="strategyData" stripe>
        <el-table-column prop="strategyNo" label="策略编号" width="120" />
        <el-table-column prop="strategyName" label="策略名称" />
        <el-table-column prop="scope" label="适用范围" width="120">
          <template #default="{ row }">
            {{ getScopeText(row.scope) }}
          </template>
        </el-table-column>
        <el-table-column prop="inspectionCycle" label="巡检周期" width="120">
          <template #default="{ row }"> {{ row.inspectionCycle }}天 </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityType(row.priority)">
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="150" />
        <el-table-column prop="creator" label="创建人" width="100" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)"
              >查看</el-button
            >
            <el-button type="success" size="small" @click="handleEdit(row)"
              >编辑</el-button
            >
            <el-button type="danger" size="small" @click="handleDelete(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";

// 搜索表单
const searchForm = reactive({
  strategyName: "",
  scope: "",
  status: "",
});

// 策略数据
const strategyData = ref([
  {
    id: 1,
    strategyNo: "PS202401001",
    strategyName: "供水管网日常巡检策略",
    scope: "city",
    inspectionCycle: 7,
    priority: "high",
    status: "published",
    createTime: "2024-01-01",
    creator: "管理员",
  },
]);

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0,
});

// 获取范围文本
const getScopeText = (scope: string) => {
  const texts: Record<string, string> = {
    city: "全市范围",
    center: "中心城区",
    industrial: "工业区",
  };
  return texts[scope] || "其他";
};

// 获取优先级类型和文本
const getPriorityType = (
  priority: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const types: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = { low: "info", medium: "warning", high: "danger" };
  return types[priority] || "info";
};

const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = { low: "低", medium: "中", high: "高" };
  return texts[priority] || "未知";
};

// 获取状态类型和文本
const getStatusType = (
  status: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const types: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = { draft: "warning", published: "success", disabled: "danger" };
  return types[status] || "info";
};

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    draft: "草稿",
    published: "已发布",
    disabled: "已停用",
  };
  return texts[status] || "未知";
};

// 事件处理函数
const handleCreateStrategy = () => console.log("制定策略");
const handleSearch = () => console.log("搜索策略", searchForm);
const handleReset = () =>
  Object.assign(searchForm, { strategyName: "", scope: "", status: "" });
const handleView = (row: any) => console.log("查看策略", row);
const handleEdit = (row: any) => console.log("编辑策略", row);
const handleDelete = (row: any) => console.log("删除策略", row);
const handleSizeChange = (size: number) => {
  pagination.size = size;
};
const handleCurrentChange = (page: number) => {
  pagination.page = page;
};

onMounted(() => {
  pagination.total = strategyData.value.length;
});
</script>

<style scoped>
.pipeline-inspection-strategy {
  padding: 20px;
}

.page-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
