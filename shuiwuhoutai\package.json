{"name": "污水智慧运维系统", "version": "2.11.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit & vite build", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint:eslint": "eslint  --fix --ext .ts,.js,.vue ./src ", "lint:prettier": "prettier --write \"**/*.{js,cjs,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint  \"**/*.{css,scss,vue}\" --fix", "lint:lint-staged": "lint-staged", "preinstall": "npx only-allow pnpm", "prepare": "husky", "commit": "git-cz"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{cjs,json}": ["prettier --write"], "*.{vue,html}": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,css}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@element-plus/icons-vue": "^2.3.1", "@types/date-fns": "^2.6.0", "@types/moment-timezone": "^0.5.30", "@vuemap/vue-amap": "^2.1.2", "@vuemap/vue-amap-extra": "^2.1.4", "@vuemap/vue-amap-loca": "^2.1.2", "@vueuse/core": "^10.9.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "5.1.10", "animate.css": "^4.1.1", "axios": "^1.7.2", "beautify-qrcode": "^1.0.3", "color": "^4.2.3", "date-fns": "^3.6.0", "echarts": "^5.5.0", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.7.3", "html2pdf.js": "^0.10.2", "lodash-es": "^4.17.21", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "net": "^1.0.2", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^6.2.2", "pinia": "^2.1.7", "qrcode.vue": "^3.4.1", "sockjs-client": "1.6.1", "sortablejs": "^1.15.2", "stompjs": "^2.3.3", "vue": "^3.4.27", "vue-i18n": "9.9.1", "vue-router": "^4.3.2", "xlsx": "^0.14.1"}, "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@iconify-json/ep": "^1.1.15", "@types/color": "^3.0.6", "@types/lodash": "^4.17.4", "@types/node": "^20.12.12", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.2", "@types/sockjs-client": "^1.5.4", "@types/sortablejs": "^1.15.8", "@types/stompjs": "^2.3.9", "@typescript-eslint/eslint-plugin": "^7.10.0", "@typescript-eslint/parser": "^7.10.0", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vuemap/unplugin-resolver": "^2.0.0", "autoprefixer": "^10.4.19", "commitizen": "^4.3.0", "cz-git": "^1.9.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.26.0", "fast-glob": "^3.3.2", "husky": "^9.0.11", "lint-staged": "^15.2.5", "postcss": "^8.4.38", "postcss-html": "^1.7.0", "postcss-scss": "^4.0.9", "prettier": "^3.2.5", "sass": "^1.77.2", "stylelint": "^16.6.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended-scss": "^14.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.0", "terser": "^5.31.0", "typescript": "^5.4.5", "unocss": "^0.58.9", "unplugin-auto-import": "^0.17.6", "unplugin-icons": "^0.18.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.2.11", "vite-plugin-mock-dev-server": "^1.5.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.2.1", "vue-tsc": "^2.0.19"}, "repository": "https://gitee.com/youlaiorg/vue3-element-admin.git", "author": "有来开源组织", "license": "MIT", "engines": {"node": ">=18.0.0"}}