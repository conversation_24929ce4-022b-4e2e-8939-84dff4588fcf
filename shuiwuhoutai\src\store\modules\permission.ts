import { RouteRecordRaw } from "vue-router";
import { constantRoutes } from "@/router";
import { store } from "@/store";
import MenuAPI from "@/api/common/menu";
import { RouteVO } from "@/api/common/menu/model";
import { defineStore } from "pinia";

const modules = import.meta.glob("../../views/**/**.vue");
const Layout = () => import("@/layout/index.vue");

/**
 * 判断用户是否有权限
 *
 * @param roles 用户角色
 * @param perms 用户权限集合
 * @param route 路由
 * @param hasChildPermission 是否有子路由权限
 * @returns
 */
const hasPermission = (
  roles: string | string[],
  perms: string[],
  route: RouteRecordRaw,
  hasChildPermission: boolean = false
) => {
  // 将roles统一转换为数组
  const roleArray = Array.isArray(roles) ? roles : [roles];

  // superadmin和admin用户拥有所有权限
  if (roleArray.includes("superadmin") || roleArray.includes("admin")) {
    return true;
  }

  // 修改密码页面特殊处理，所有用户都可以访问
  if (route.path === "edit" && route.meta?.title === "修改密码") {
    return true;
  }

  // 如果路由没有设置权限要求，且有子路由权限，则允许访问
  if (!route.meta?.permissions) {
    return hasChildPermission;
  }

  // 获取路由所需的权限
  const requiredPermissions = route.meta.permissions as string[];

  // 过滤掉空字符串，并规范化权限
  const normalizedPerms = perms.filter((p) => p && p.trim() !== "");

  // 检查用户是否拥有路由所需的权限
  if (normalizedPerms.length > 0) {
    // 修改权限检查逻辑,要求完全匹配权限字符串
    return requiredPermissions.some((permission) =>
      normalizedPerms.includes(permission)
    );
  }

  // 如果用户没有任何权限，但有子路由权限，则允许访问
  return hasChildPermission;
};

/**
 * 递归过滤有权限的路由
 *
 * @param routes 路由列表
 * @param roles 用户角色
 * @param perms 用户权限集合
 * @param parentPath 父路径
 * @returns 返回用户有权限的路由
 */
const filterAsyncRoutes = (
  routes: RouteVO[],
  roles: string | string[],
  perms: string[],
  parentPath: string = ""
) => {
  const asyncRoutes: RouteRecordRaw[] = [];
  routes.forEach((route) => {
    const tmpRoute = { ...route } as RouteRecordRaw;
    // 添加父路径信息到meta中
    if (!tmpRoute.meta) {
      tmpRoute.meta = {};
    }
    tmpRoute.meta.parentPath = parentPath;

    // 先处理子路由
    let accessibleChildren: RouteRecordRaw[] = [];
    if (tmpRoute.children) {
      // 传递当前路由的完整路径给子路由
      const currentPath = parentPath
        ? `${parentPath}/${tmpRoute.path}`
        : tmpRoute.path;
      accessibleChildren = filterAsyncRoutes(
        route.children,
        roles,
        perms,
        currentPath
      );
    }

    // 检查是否有权限访问当前路由或其子路由
    const hasRoutePermission = hasPermission(
      roles,
      perms,
      tmpRoute,
      accessibleChildren.length > 0
    );
    const hasChildrenPermission = accessibleChildren.length > 0;

    // 如果当前路由有权限访问，或者有可访问的子路由，则保留该路由
    if (hasRoutePermission || hasChildrenPermission) {
      if (tmpRoute.component?.toString() == "Layout") {
        tmpRoute.component = Layout;
      } else {
        const component = modules[`../../views/${tmpRoute.component}.vue`];
        if (component) {
          tmpRoute.component = component;
        } else {
          tmpRoute.component = modules[`../../views/error-page/404.vue`];
        }
      }

      // 如果有可访问的子路由，添加到路由配置中
      if (accessibleChildren.length > 0) {
        tmpRoute.children = accessibleChildren;
        // 如果是父路由且没有权限访问，但有可访问的子路由
        if (!hasRoutePermission) {
          // 移除父路由的component，使其变成一个纯粹的路由组
          if (tmpRoute.component === Layout) {
            tmpRoute.component = Layout;
          }
          // 添加重定向到第一个可访问的子路由
          tmpRoute.redirect = `${tmpRoute.path}/${accessibleChildren[0].path}`;
          // 移除父路由的path属性，防止其变成可点击的菜单项
          delete tmpRoute.component;
        }
      }

      asyncRoutes.push(tmpRoute);
    }
  });
  return asyncRoutes;
};

// setup
export const usePermissionStore = defineStore("permission", () => {
  // state
  const routes = ref<RouteRecordRaw[]>([]);
  const dynamicRoutes = ref<RouteRecordRaw[]>([]);

  // actions
  function setRoutes(roles: string | string[], perms: string[] = []) {
    // 如果是superadmin或admin，直接使用所有路由
    const roleArray = Array.isArray(roles) ? roles : [roles];
    if (roleArray.includes("superadmin") || roleArray.includes("admin")) {
      routes.value = constantRoutes;
      dynamicRoutes.value = constantRoutes;
      return;
    }
    // 否则过滤路由
    const accessedRoutes = filterAsyncRoutes(
      constantRoutes as unknown as RouteVO[],
      roles,
      perms
    );
    routes.value = accessedRoutes;
    dynamicRoutes.value = accessedRoutes;
  }

  /**
   * 生成动态路由
   *
   * @param roles 用户角色集合
   * @param perms 用户权限集合
   * @returns
   */
  function generateRoutes(roles: string[], perms: string[] = []) {
    return new Promise<RouteRecordRaw[]>((resolve, reject) => {
      // 接口获取所有路由
      MenuAPI.getRoutes()
        .then((data) => {
          // 过滤有权限的动态路由
          const accessedRoutes = filterAsyncRoutes(data, roles, perms);
          setRoutes(roles, perms);
          resolve(accessedRoutes);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  /**
   * 获取与激活的顶部菜单项相关的混合模式左侧菜单集合
   */
  const mixLeftMenus = ref<RouteRecordRaw[]>([]);
  function setMixLeftMenus(topMenuPath: string) {
    const matchedItem = routes.value.find((item) => item.path === topMenuPath);
    if (matchedItem && matchedItem.children) {
      mixLeftMenus.value = matchedItem.children;
    }
  }

  return {
    routes,
    dynamicRoutes,
    setRoutes,
    generateRoutes,
    mixLeftMenus,
    setMixLeftMenus,
  };
});

// 非setup
export function usePermissionStoreHook() {
  return usePermissionStore(store);
}
