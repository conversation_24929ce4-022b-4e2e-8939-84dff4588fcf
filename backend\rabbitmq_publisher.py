# RabbitMQ消息队列集成模块
import pika
import json
import logging
import threading
import time
from typing import Optional, Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class RabbitMQPublisher:
    """RabbitMQ消息发布者 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(RabbitMQPublisher, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self.connection = None
        self.channel = None
        self.exchange_name = 'water_station_data'
        self.routing_key = 'device_data'
        self._connect_lock = threading.Lock()
        
        # 初始化连接
        self._connect()
    
    def _connect(self):
        """连接到RabbitMQ服务器"""
        try:
            # RabbitMQ连接参数
            connection_params = pika.ConnectionParameters(
                host='localhost',
                port=5672,
                virtual_host='/',
                credentials=pika.PlainCredentials('water_user', 'water123'),
                heartbeat=600,
                blocked_connection_timeout=300,
            )
            
            self.connection = pika.BlockingConnection(connection_params)
            self.channel = self.connection.channel()
            
            # 声明交换机（使用direct类型）
            self.channel.exchange_declare(
                exchange=self.exchange_name,
                exchange_type='direct',
                durable=True
            )
            
            # 声明队列
            self.channel.queue_declare(
                queue='device_data_queue',
                durable=True
            )
            
            # 绑定队列到交换机
            self.channel.queue_bind(
                exchange=self.exchange_name,
                queue='device_data_queue',
                routing_key=self.routing_key
            )
            
            logger.info("RabbitMQ连接成功建立")
            
        except Exception as e:
            logger.error(f"RabbitMQ连接失败: {e}")
            self.connection = None
            self.channel = None
    
    def _ensure_connection(self):
        """确保RabbitMQ连接有效，增加重试机制"""
        with self._connect_lock:
            max_retries = 3
            retry_count = 0
            
            while retry_count < max_retries:
                try:
                    # 检查连接状态
                    if self.connection is None or self.connection.is_closed:
                        logger.warning(f"RabbitMQ连接已断开，尝试重新连接... (尝试 {retry_count + 1}/{max_retries})")
                        self._connect()
                        
                    # 检查通道状态
                    if self.connection and not self.connection.is_closed:
                        if self.channel is None or self.channel.is_closed:
                            logger.warning("RabbitMQ通道已关闭，尝试重新创建...")
                            self.channel = self.connection.channel()
                            self.channel.exchange_declare(
                                exchange=self.exchange_name,
                                exchange_type='topic',
                                durable=True
                            )
                            self.channel.queue_declare(
                                queue='device_data_queue',
                                durable=True
                            )
                            self.channel.queue_bind(
                                exchange=self.exchange_name,
                                queue='device_data_queue',
                                routing_key=self.routing_key
                            )
                        
                        # 连接成功，退出重试循环
                        if self.channel and not self.channel.is_closed:
                            return
                    
                except Exception as e:
                    logger.error(f"重新连接RabbitMQ失败 (尝试 {retry_count + 1}): {e}")
                    retry_count += 1
                    if retry_count < max_retries:
                        time.sleep(1)  # 等待1秒后重试
                    
            # 重试失败
            logger.error("RabbitMQ连接重试失败，已达到最大重试次数")
            self.connection = None
            self.channel = None
    
    def publish_message(self, message_data: Dict[str, Any], message_type: str = 'device_data') -> bool:
        """
        发布消息到RabbitMQ
        
        Args:
            message_data: 要发送的消息数据
            message_type: 消息类型 ('device_data', 'status_change')
            
        Returns:
            bool: 发送是否成功
        """
        try:
            self._ensure_connection()
            
            if self.channel is None:
                logger.error("RabbitMQ通道不可用，消息发送失败")
                return False
            
            # 准备消息
            message = {
                'message_type': message_type,
                'timestamp': datetime.now().isoformat(),
                'data': message_data
            }
            
            # 发布消息
            self.channel.basic_publish(
                exchange=self.exchange_name,
                routing_key=self.routing_key,
                body=json.dumps(message, ensure_ascii=False),
                properties=pika.BasicProperties(
                    delivery_mode=2,  # 持久化消息
                    content_type='application/json'
                )
            )
            
            logger.debug(f"消息已发送到RabbitMQ: {message_type}")
            return True
            
        except Exception as e:
            logger.error(f"RabbitMQ消息发送失败: {e}")
            return False
    
    def publish_device_data(self, device_sn: str, raw_data: Dict[str, Any], 
                           client_address: str) -> bool:
        """
        发布设备数据消息
        
        Args:
            device_sn: 设备序列号
            raw_data: 原始设备数据
            client_address: 客户端地址
            
        Returns:
            bool: 发送是否成功
        """
        message_data = {
            'device_sn': device_sn,
            'raw_data': raw_data,
            'client_address': client_address,
            'receive_timestamp': datetime.now().isoformat()
        }
        
        return self.publish_message(message_data, 'device_data')
    
    def publish_status_change(self, device_sn: str, changes: list, 
                             client_address: str, is_status_only: bool = False) -> bool:
        """
        发布设备状态变化消息
        
        Args:
            device_sn: 设备序列号
            changes: 状态变化列表
            client_address: 客户端地址
            is_status_only: 是否为单独的状态变化消息
            
        Returns:
            bool: 发送是否成功
        """
        message_data = {
            'device_sn': device_sn,
            'changes': changes,
            'client_address': client_address,
            'is_status_only': is_status_only,
            'change_timestamp': datetime.now().isoformat()
        }
        
        return self.publish_message(message_data, 'status_change')
    
    def close(self):
        """关闭RabbitMQ连接"""
        try:
            if self.channel and not self.channel.is_closed:
                self.channel.close()
            if self.connection and not self.connection.is_closed:
                self.connection.close()
            logger.info("RabbitMQ连接已关闭")
        except Exception as e:
            logger.error(f"关闭RabbitMQ连接时出错: {e}")

# 全局RabbitMQ发布者实例
_rabbitmq_publisher = None
_publisher_lock = threading.Lock()

def get_rabbitmq_publisher() -> RabbitMQPublisher:
    """获取RabbitMQ发布者实例（单例）"""
    global _rabbitmq_publisher
    if _rabbitmq_publisher is None:
        with _publisher_lock:
            if _rabbitmq_publisher is None:
                _rabbitmq_publisher = RabbitMQPublisher()
    return _rabbitmq_publisher

def close_rabbitmq_publisher():
    """关闭RabbitMQ发布者"""
    global _rabbitmq_publisher
    if _rabbitmq_publisher is not None:
        _rabbitmq_publisher.close()
        _rabbitmq_publisher = None