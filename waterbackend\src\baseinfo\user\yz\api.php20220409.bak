<?php
 error_reporting(1);
// ini_set("display_errors","Off");
		error_reporting(E_ALL^E_NOTICE);
// 		error_reporting(E_ALL);


header ('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
class api{
	public $db;
	function __construct(){
		$this->db=new SQLite3("sql.php");
		$this->db->exec('pragma synchronous = off;');
	}
	function run($str){
		eval('$this->'.$str.'();');
	}
	
	//添加卡
	function addcard(){
	    if(!$_POST['sn'])exit;
	    
	    $list=explode("\r\n",$_POST['sn']);
	   // print_r($list);exit;
	    for($i=0;$i<count($list);$i++){
	        if(strlen($list[$i])<10){continue;
	        echo  $list[$i];
	        }
	         $sql="insert into  card (sn,cardtype,addtime,customerid) values('".$list[$i].   "','".$_POST['cardtype']."',".time().",$_POST[customerid])";
	   
	    $this->db->exec($sql);
	    }
	    
	   
	    	echo json_encode(array (status =>"success",msg =>"添加成功",data =>array ()));
	}
	
	//删除卡
	function  removecard(){
	    $this->db->exec("delete  from  card where id=$_POST[id]");
	    echo json_encode(array (status =>"success",msg =>"OK",data =>null));
	}
	
	//搜索卡
	function  showcard(){
	      $sql="select * from  card where 1=1 and  customerid=$_POST[customerid]";
	  
	    if(isset($_POST['status']) && $_POST['status']!==''){
	        $sql.=" and status=$_POST[status]";
	    }
	     if(isset($_POST['keyword']) && $_POST['keyword']!==''){
	        $sql.=" and( sn like '%".$_POST['keyword']."%' or   memo  like '%".$_POST['keyword']."%' or cardtype like '%".$_POST['keyword']."%' or machineid='".$_POST['keyword']."')  ";
	    }
	    
	    
	    
	    
	    $totalsql=str_replace("*", "count(id)" ,$sql);
	   
	   // echo $totalsql;exit;
	    $totalresult=$this->db->query($totalsql);
	    $total=$totalresult->fetchArray();
	    
	    
	    if($_POST['page']){
	       $page=$_POST['page']-1;
	    }else {
	        $page=0;
	    }
	    $sql.=" order by  id  desc limit ".($page*10).",10";
	    
	    $result=$this->db->query($sql);
	    $list=[];
	    while($tmp=$result->fetchArray()){
	        $list[]=$tmp;
	    }
	    	echo json_encode(array (status =>"success",msg =>"操作成功",data =>$list,total=>$total[0]));
	}
	
	//保存卡备注
	function savecardmemo(){
	    $this->db->exec("update card set memo='".$_POST['memo']."' where id=$_POST[id]");

        	echo json_encode(array (status =>"success",msg =>"操作成功",data =>null));
	}
	
	
	//保存设备备注
	function  savemachinememo(){
	    $this->db->exec("update machine set  memo='".$_POST['memo']."' where  id=$_POST[id]");
	    	echo json_encode(array (status =>"success",msg =>"操作成功",data =>null));
	}
	
	
	
	//激活卡
	function  activecard(){
	    //20190129先查询是否已使用过得卡
	   $result=$this->db->query("select  * from   card  where   status=1  and  sn='".$_POST['sn']."'");
	   $res=$result->fetchArray();
	   if($res['id']){
	       echo json_encode(array (status =>"failed",msg =>"卡已激活,激活日期".date("Y-m-d H:i:s",$res['usedtime']/1000),data =>null));
	        exit;
	   }
	    
	    
	    
	    
	    //错误卡号或已激活退出
	    $result=$this->db->query("select * from  card where  status=0 and  sn='".$_POST['sn']."'");
	    $res=$result->fetchArray();
	    if(!$res['id']){
	        echo json_encode(array (status =>"failed",msg =>"操作失败",data =>$list));
	        exit;
	    }
	    $cardtype=$res['cardtype'];
	    $customerid=$res['customerid'];
	    switch ($cardtype) {
	         case '小时卡':
	            $time= 1000*3600;
	            break;
	              case '天卡':
	            $time= 1000*3600*24;
	            break;
	             case '天周卡':
	            $time= 1000*3600*24*7;
	            break;
	        case '年卡':
	            $time= 1000*3600*24*366;
	            break;
	        case '季卡':
	            $time= 1000*3600*24*92;
	            break;
	       case '月卡':
	            $time= 1000*3600*24*31;
	            break;
	             case '永久卡':
	            $time= 1000*3600*24*31*366*100;
	            break;
	        
	    }
	    //20190130把到期时间同时保存进卡里面
	    
	   
	    echo json_encode(array (status =>"success",msg =>"OK",data =>$list));
	    
	    $result=$this->db->query("select * from machine where machineid='".$_POST['machineid']."'");
	    $res=$result->fetchArray();
	    $now=time()*1000;
	    $exptime= $now > $res['exptime']? ($now+$time):($res['exptime']+$time);
	     $sql="update  card set  machineid='".$_POST['machineid']."' , status=1 ,usedtime= ".(time()*1000).",exptime=$exptime where  sn='".$_POST['sn']."'  and  status=0";
	    $this->db->exec($sql);
	    
	    if($res['id']){
	        $sql="update  machine  set  exptime=$exptime";
	        if($_POST['ctype'])$sql.=",ctype= '".$_POST['ctype']."'";
	        if($_POST['scriptname'])$sql.=",scriptname= '".$_POST['scriptname']."'";
	        if($_POST['mobilename'])$sql.=",mobilename= '".$_POST['mobilename']."'";
	        if($_POST['systeminfo'])$sql.=",systeminfo= '".$_POST['systeminfo']."'";
	        
	        
	        $this->db->exec($sql." where id=$res[id]");
	        
	    }else {
	        $this->db->exec("insert into  machine(ctype,machineid,scriptname,mobilename,systeminfo,runinfo,exptime,status,customerid) values('".$_POST['ctype']."','".$_POST['machineid']."','".$_POST['scriptname']."','".$_POST['mobilename']."','".$_POST['systeminfo']."','".$_POST['runinfo']."',$exptime ,'1',$customerid ) ");
	    }
	    
	    
	    
	}
	
	
	
	function  setmachinestatus(){
	    $this->db->exec("update  machine set  status='".$_POST['status']."' where  id=$_POST[id]");
	     echo  json_encode(array (status =>"success",msg =>"OK",data =>null));
	}
	
	
	//登录
	function  dologin(){
	    $result=$this->db->query("select id,exptime,username  from customer  where username='".$_POST['username']."' and  password ='".md5($_POST['password'])."'  and  status =1 ");
	    $res=$result->fetchArray();
	    if($res['id']){
	        echo  json_encode(array (status =>"success",msg =>"OK",data =>$res));
	    }
	    else {
	        echo  json_encode(array (status =>"failed",msg =>"用户名或密码错误",data =>null));
	    }
	    
	}
	
	//用户列表
	function  getuserlist(){
	    $result=$this->db->query("select * from  customer where username!='admin'");
	    $list=[];
	    while($tmp=$result->fetchArray()){
	        $list[]=$tmp;
	    }
	    echo  json_encode(array (status =>"success",msg =>"OK",data =>$list));
	}
	
	//添加用户
	function adduser(){
	    $this->db->exec("insert into  customer(username,password,status) values('".$_POST['username']."','".md5($_POST['password'])."',1)");
	     echo  json_encode(array (status =>"success",msg =>"OK",data =>null));
	}
	
	//删除用户
	function  deleteuser(){
	    $this->db->exec("delete from customer where  id=$_POST[id] and  username !='admin'");
	     echo  json_encode(array (status =>"success",msg =>"OK",data =>null));
	}
	
	//设备列表
	function  machinelist(){
	    $sql="select * from  machine where  customerid=$_POST[customerid]";
	    if($_POST['keyword']){
	        $sql.=" and    ( ctype like  '%".$_POST['keyword']."%' or   scriptname like  '%".$_POST['keyword']."%' or   machineid like '".$_POST['keyword']."'     ) ";
	    }
	 
	  
	     $totalsql=str_replace("*", "count(id)" ,$sql);
	   
	    $totalresult=$this->db->query($totalsql);
	    $total=$totalresult->fetchArray();
	    
	    
	    if($_POST['page']){
	       $page=$_POST['page']-1;
	    }else {
	        $page=0;
	    }
	    $sql.=" order by id  desc limit ".($page*10).",10";
	    
	    
	    $result=$this->db->query($sql);
	    $list=[];
	      while($tmp=$result->fetchArray()){
	        $list[]=$tmp;
	    }
	    
	    for($i=0;$i<count($list)  ;$i++){
	        $file=getcwd()."/keeplive/".$list[$i]['machineid'];
	        if(file_exists($file )){
	            $arr=file ($file);
	            $lasttime=$arr[1];
	            if(time()-$lasttime  > 300   ){
	                $list[$i]['runinfo']="下线";
	            }else {
	                $list[$i]['runinfo']='在线';
	            }
	        }else {
	            $list[$i]['runinfo']="下线";
	        }
	    }
	    
	    
	    
	    echo  json_encode(array (status =>"success",msg =>"OK",data =>$list,total=>$total[0]));
	}
	
	//设备心跳
	function heartbeat(){
	    $dir=getcwd()."/keeplive/";
	    if(!$_POST['machineid']|| !$_POST['id']   ){
	        print_r($_POST);
	        exit;
	    }
	    $file=$dir.$_POST['machineid'];
	    $arr=file($file);
	    $str=trim($arr[0]);
	    if(!$str){
	        echo $str;
	        exit; 
	    };
	    if($str==$_POST['id']){
	        echo  json_encode(array (status =>"success",msg =>"OK",data =>null));
	        $fp=fopen($file,"w");
	        fwrite($fp,$str."\n".time());
	        fclose($fp);
	    }else {
	        echo  json_encode(array (status =>"failed",msg =>"Error",data =>null));
	    }
	}
	
	//设备心跳初始化
	function  heartbeatinit(){
	    $dir=getcwd()."/keeplive/";
	   
	    if(!$_POST['machineid'] ||  !$_POST['username'] )exit;
	    
	    $result=$this->db->query("select id from  customer where username like  '".$_POST['username']."'");
	    $res=$result->fetchArray();
	    $customerid=$res['id'];
	   
	    
	    $result=$this->db->query("select  exptime  from  machine where machineid='".$_POST['machineid']."'  and   customerid=$customerid and  status='1'");
	  //  $result=$this->db->query("select  *  from  machine where 1=1 ");
	    $res=$result->fetchArray();
	  //  print_r($res);exit;
	    if(!$res['exptime'])exit;
	    if($res['exptime']*1  <  (time()*1000)  ){
	        echo  json_encode(array (status =>"failed",msg =>"账号过期",data =>null));
	        exit;
	    }
	    
	    $file=$dir.$_POST['machineid'];
	    $fp=fopen($file,"w");
	    $id=rand(1,100000);
	    fwrite($fp,$id."\n".time());
	    fclose($fp);
	     echo  json_encode(array (status =>"success",msg =>"OK",data =>$id));
	}
	
	//查询设备到期时间
	function getexptime(){
	     if(!$_POST['machineid']  )exit;
	    $result=$this->db->query("select  exptime  from  machine where machineid='".$_POST['machineid']."'");
	    $res=$result->fetchArray();
	    if(!$res['exptime'])exit;
	        echo  json_encode(array (status =>"success",msg =>"OK",data =>date("Y-m-d H:i:s", $res['exptime']/1000  )));
	    
	}
	
}
$app=new api();
if($_GET['action']){
    $active=$_GET['action'];
}else {
  $active= $_GET['active'];  
}

$app->run($active);


