# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@angular-devkit/core@11.2.4":
  version "11.2.4"
  resolved "https://registry.nlark.com/@angular-devkit/core/download/@angular-devkit/core-11.2.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40angular-devkit%2Fcore%2Fdownload%2F%40angular-devkit%2Fcore-11.2.4.tgz"
  integrity sha1-RAS4bY29tBoOP5TLCP+GBODEmi4=
  dependencies:
    ajv "6.12.6"
    fast-json-stable-stringify "2.1.0"
    magic-string "0.25.7"
    rxjs "6.6.3"
    source-map "0.7.3"

"@angular-devkit/core@11.2.6":
  version "11.2.6"
  resolved "https://registry.nlark.com/@angular-devkit/core/download/@angular-devkit/core-11.2.6.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40angular-devkit%2Fcore%2Fdownload%2F%40angular-devkit%2Fcore-11.2.6.tgz"
  integrity sha1-+Q38xyBM3Fjfy5kBziZcXJwKXfo=
  dependencies:
    ajv "6.12.6"
    fast-json-stable-stringify "2.1.0"
    magic-string "0.25.7"
    rxjs "6.6.3"
    source-map "0.7.3"

"@angular-devkit/schematics-cli@0.1102.6":
  version "0.1102.6"
  resolved "https://registry.nlark.com/@angular-devkit/schematics-cli/download/@angular-devkit/schematics-cli-0.1102.6.tgz"
  integrity sha1-UbkBKRO+lLboBjoviDn35LZSBXs=
  dependencies:
    "@angular-devkit/core" "11.2.6"
    "@angular-devkit/schematics" "11.2.6"
    "@schematics/schematics" "0.1102.6"
    ansi-colors "4.1.1"
    inquirer "7.3.3"
    minimist "1.2.5"
    symbol-observable "3.0.0"

"@angular-devkit/schematics@11.2.4":
  version "11.2.4"
  resolved "https://registry.nlark.com/@angular-devkit/schematics/download/@angular-devkit/schematics-11.2.4.tgz?cache=0&sync_timestamp=1628119009962&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40angular-devkit%2Fschematics%2Fdownload%2F%40angular-devkit%2Fschematics-11.2.4.tgz"
  integrity sha1-umfug1zrIQd38f7s6GGV8owbLpY=
  dependencies:
    "@angular-devkit/core" "11.2.4"
    ora "5.3.0"
    rxjs "6.6.3"

"@angular-devkit/schematics@11.2.6":
  version "11.2.6"
  resolved "https://registry.nlark.com/@angular-devkit/schematics/download/@angular-devkit/schematics-11.2.6.tgz?cache=0&sync_timestamp=1628119009962&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40angular-devkit%2Fschematics%2Fdownload%2F%40angular-devkit%2Fschematics-11.2.6.tgz"
  integrity sha1-WQja72CvLl2Y/XWsP+d8AqsUT6M=
  dependencies:
    "@angular-devkit/core" "11.2.6"
    ora "5.3.0"
    rxjs "6.6.3"

"@babel/code-frame@7.12.11":
  version "7.12.11"
  resolved "https://registry.nlark.com/@babel/code-frame/download/@babel/code-frame-7.12.11.tgz"
  integrity sha1-9K1DWqJj25NbjxDyxVLSP7cWpj8=
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.14.5", "@babel/code-frame@^7.8.3":
  version "7.14.5"
  resolved "https://registry.nlark.com/@babel/code-frame/download/@babel/code-frame-7.14.5.tgz"
  integrity sha1-I7CNdA6D9JxeWZRfvxtD6Au/Tts=
  dependencies:
    "@babel/highlight" "^7.14.5"

"@babel/compat-data@^7.15.0":
  version "7.15.0"
  resolved "https://registry.nlark.com/@babel/compat-data/download/@babel/compat-data-7.15.0.tgz"
  integrity sha1-Lbr4uFM0eWyvuw9Xk6kKL8AQsXY=

"@babel/core@^7.1.0", "@babel/core@^7.7.5":
  version "7.15.0"
  resolved "https://registry.nlark.com/@babel/core/download/@babel/core-7.15.0.tgz?cache=0&sync_timestamp=1628111663424&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fcore%2Fdownload%2F%40babel%2Fcore-7.15.0.tgz"
  integrity sha1-dJ5Xxod4tzrYCCd1Vh9n9Rlqr6g=
  dependencies:
    "@babel/code-frame" "^7.14.5"
    "@babel/generator" "^7.15.0"
    "@babel/helper-compilation-targets" "^7.15.0"
    "@babel/helper-module-transforms" "^7.15.0"
    "@babel/helpers" "^7.14.8"
    "@babel/parser" "^7.15.0"
    "@babel/template" "^7.14.5"
    "@babel/traverse" "^7.15.0"
    "@babel/types" "^7.15.0"
    convert-source-map "^1.7.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.1.2"
    semver "^6.3.0"
    source-map "^0.5.0"

"@babel/generator@^7.15.0":
  version "7.15.0"
  resolved "https://registry.nlark.com/@babel/generator/download/@babel/generator-7.15.0.tgz"
  integrity sha1-p9DBcuDYFJdLrVqnes5UO5eRfxU=
  dependencies:
    "@babel/types" "^7.15.0"
    jsesc "^2.5.1"
    source-map "^0.5.0"

"@babel/helper-compilation-targets@^7.15.0":
  version "7.15.0"
  resolved "https://registry.nlark.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.15.0.tgz"
  integrity sha1-lz34y9AlUV8/8l2wwF78cE+nmBg=
  dependencies:
    "@babel/compat-data" "^7.15.0"
    "@babel/helper-validator-option" "^7.14.5"
    browserslist "^4.16.6"
    semver "^6.3.0"

"@babel/helper-function-name@^7.14.5":
  version "7.14.5"
  resolved "https://registry.nlark.com/@babel/helper-function-name/download/@babel/helper-function-name-7.14.5.tgz"
  integrity sha1-ieLEdJcvFdjiM7Uu6MSA4s/NUMQ=
  dependencies:
    "@babel/helper-get-function-arity" "^7.14.5"
    "@babel/template" "^7.14.5"
    "@babel/types" "^7.14.5"

"@babel/helper-get-function-arity@^7.14.5":
  version "7.14.5"
  resolved "https://registry.nlark.com/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.14.5.tgz"
  integrity sha1-Jfv6V5sJN+7h87gF7OTOOYxDGBU=
  dependencies:
    "@babel/types" "^7.14.5"

"@babel/helper-hoist-variables@^7.14.5":
  version "7.14.5"
  resolved "https://registry.nlark.com/@babel/helper-hoist-variables/download/@babel/helper-hoist-variables-7.14.5.tgz"
  integrity sha1-4N0nwzp45XfXyIhJFqPn7x98f40=
  dependencies:
    "@babel/types" "^7.14.5"

"@babel/helper-member-expression-to-functions@^7.15.0":
  version "7.15.0"
  resolved "https://registry.nlark.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.15.0.tgz"
  integrity sha1-Ddr1KZyBefJ/NzJ5NlU+m7pgmQs=
  dependencies:
    "@babel/types" "^7.15.0"

"@babel/helper-module-imports@^7.14.5":
  version "7.14.5"
  resolved "https://registry.nlark.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.14.5.tgz"
  integrity sha1-bRpE32o4yVeqfDEtoHZCnxG0IvM=
  dependencies:
    "@babel/types" "^7.14.5"

"@babel/helper-module-transforms@^7.15.0":
  version "7.15.0"
  resolved "https://registry.nlark.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.15.0.tgz"
  integrity sha1-Z5J1WB6gVjc+3b42DhQZ7yN4Owg=
  dependencies:
    "@babel/helper-module-imports" "^7.14.5"
    "@babel/helper-replace-supers" "^7.15.0"
    "@babel/helper-simple-access" "^7.14.8"
    "@babel/helper-split-export-declaration" "^7.14.5"
    "@babel/helper-validator-identifier" "^7.14.9"
    "@babel/template" "^7.14.5"
    "@babel/traverse" "^7.15.0"
    "@babel/types" "^7.15.0"

"@babel/helper-optimise-call-expression@^7.14.5":
  version "7.14.5"
  resolved "https://registry.nlark.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.14.5.tgz"
  integrity sha1-8nOVqGGeBmWz8DZM3bQcJdcbSZw=
  dependencies:
    "@babel/types" "^7.14.5"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.8.0":
  version "7.14.5"
  resolved "https://registry.nlark.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.14.5.tgz?cache=0&sync_timestamp=1623280305577&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelper-plugin-utils%2Fdownload%2F%40babel%2Fhelper-plugin-utils-7.14.5.tgz"
  integrity sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=

"@babel/helper-replace-supers@^7.15.0":
  version "7.15.0"
  resolved "https://registry.nlark.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.15.0.tgz"
  integrity sha1-rOB3CPW/dGvy5rqZVyzOebXU5/Q=
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.15.0"
    "@babel/helper-optimise-call-expression" "^7.14.5"
    "@babel/traverse" "^7.15.0"
    "@babel/types" "^7.15.0"

"@babel/helper-simple-access@^7.14.8":
  version "7.14.8"
  resolved "https://registry.nlark.com/@babel/helper-simple-access/download/@babel/helper-simple-access-7.14.8.tgz"
  integrity sha1-guH+wGRKfndcdNMF8hLDn4/nOSQ=
  dependencies:
    "@babel/types" "^7.14.8"

"@babel/helper-split-export-declaration@^7.14.5":
  version "7.14.5"
  resolved "https://registry.nlark.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.14.5.tgz"
  integrity sha1-IrI6VO9RwrdgXYUZMMGXbdC8aTo=
  dependencies:
    "@babel/types" "^7.14.5"

"@babel/helper-validator-identifier@^7.14.5", "@babel/helper-validator-identifier@^7.14.9":
  version "7.14.9"
  resolved "https://registry.nlark.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.14.9.tgz"
  integrity sha1-ZlTRcbICT22O4VG/JQlpmRkTHUg=

"@babel/helper-validator-option@^7.14.5":
  version "7.14.5"
  resolved "https://registry.nlark.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.14.5.tgz?cache=0&sync_timestamp=1623280323607&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelper-validator-option%2Fdownload%2F%40babel%2Fhelper-validator-option-7.14.5.tgz"
  integrity sha1-bnKh//GNXfy4eOHmLxoCHEty1aM=

"@babel/helpers@^7.14.8":
  version "7.14.8"
  resolved "https://registry.nlark.com/@babel/helpers/download/@babel/helpers-7.14.8.tgz"
  integrity sha1-g5+I9GMCWIbP9/haNSlwB+LaG3c=
  dependencies:
    "@babel/template" "^7.14.5"
    "@babel/traverse" "^7.14.8"
    "@babel/types" "^7.14.8"

"@babel/highlight@^7.10.4", "@babel/highlight@^7.14.5":
  version "7.14.5"
  resolved "https://registry.nlark.com/@babel/highlight/download/@babel/highlight-7.14.5.tgz?cache=0&sync_timestamp=1623280306084&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhighlight%2Fdownload%2F%40babel%2Fhighlight-7.14.5.tgz"
  integrity sha1-aGGlLwOWZAUAH2qlNKAaJNmejNk=
  dependencies:
    "@babel/helper-validator-identifier" "^7.14.5"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/parser@^7.1.0", "@babel/parser@^7.14.5", "@babel/parser@^7.15.0":
  version "7.15.0"
  resolved "https://registry.nlark.com/@babel/parser/download/@babel/parser-7.15.0.tgz"
  integrity sha1-ttbikFjKNpEnsO7KKhxLV5Txtrk=

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "https://registry.npm.taobao.org/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz"
  integrity sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npm.taobao.org/@babel/plugin-syntax-bigint/download/@babel/plugin-syntax-bigint-7.8.3.tgz"
  integrity sha1-TJpvZp9dDN8bkKFnHpoUa+UwDOo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.8.3":
  version "7.12.13"
  resolved "https://registry.nlark.com/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.12.13.tgz"
  integrity sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-import-meta@^7.8.3":
  version "7.10.4"
  resolved "https://registry.npm.taobao.org/@babel/plugin-syntax-import-meta/download/@babel/plugin-syntax-import-meta-7.10.4.tgz"
  integrity sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npm.taobao.org/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz"
  integrity sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-logical-assignment-operators@^7.8.3":
  version "7.10.4"
  resolved "https://registry.nlark.com/@babel/plugin-syntax-logical-assignment-operators/download/@babel/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  integrity sha1-ypHvRjA1MESLkGZSusLp/plB9pk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npm.taobao.org/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  integrity sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.8.3":
  version "7.10.4"
  resolved "https://registry.nlark.com/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.10.4.tgz"
  integrity sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npm.taobao.org/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz"
  integrity sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npm.taobao.org/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  integrity sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npm.taobao.org/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz"
  integrity sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-top-level-await@^7.8.3":
  version "7.14.5"
  resolved "https://registry.nlark.com/@babel/plugin-syntax-top-level-await/download/@babel/plugin-syntax-top-level-await-7.14.5.tgz?cache=0&sync_timestamp=1623280492352&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fplugin-syntax-top-level-await%2Fdownload%2F%40babel%2Fplugin-syntax-top-level-await-7.14.5.tgz"
  integrity sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/runtime@^7.10.5":
  version "7.17.9"
  resolved "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.17.9.tgz"
  integrity sha512-lSiBBvodq29uShpWGNbgFdKYNiFDo5/HIYsaCEY9ff4sb10x9jizo2+pRrSyF4jKZCXqgzuqBOQKbUm90gQwJg==
  dependencies:
    regenerator-runtime "^0.13.4"

"@babel/template@^7.14.5", "@babel/template@^7.3.3":
  version "7.14.5"
  resolved "https://registry.nlark.com/@babel/template/download/@babel/template-7.14.5.tgz"
  integrity sha1-qbydizM1T/blWpxg0RCSAKaJdPQ=
  dependencies:
    "@babel/code-frame" "^7.14.5"
    "@babel/parser" "^7.14.5"
    "@babel/types" "^7.14.5"

"@babel/traverse@^7.1.0", "@babel/traverse@^7.14.8", "@babel/traverse@^7.15.0":
  version "7.15.0"
  resolved "https://registry.nlark.com/@babel/traverse/download/@babel/traverse-7.15.0.tgz"
  integrity sha1-TMqDj9GyoDKDwfOOFB9jnWCz/Jg=
  dependencies:
    "@babel/code-frame" "^7.14.5"
    "@babel/generator" "^7.15.0"
    "@babel/helper-function-name" "^7.14.5"
    "@babel/helper-hoist-variables" "^7.14.5"
    "@babel/helper-split-export-declaration" "^7.14.5"
    "@babel/parser" "^7.15.0"
    "@babel/types" "^7.15.0"
    debug "^4.1.0"
    globals "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.14.5", "@babel/types@^7.14.8", "@babel/types@^7.15.0", "@babel/types@^7.3.0", "@babel/types@^7.3.3":
  version "7.15.0"
  resolved "https://registry.nlark.com/@babel/types/download/@babel/types-7.15.0.tgz?cache=0&sync_timestamp=1628111608723&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Ftypes%2Fdownload%2F%40babel%2Ftypes-7.15.0.tgz"
  integrity sha1-Ya8R8ihsTpxpyo3rX0N1pzxy3L0=
  dependencies:
    "@babel/helper-validator-identifier" "^7.14.9"
    to-fast-properties "^2.0.0"

"@bcoe/v8-coverage@^0.2.3":
  version "0.2.3"
  resolved "https://registry.npm.taobao.org/@bcoe/v8-coverage/download/@bcoe/v8-coverage-0.2.3.tgz"
  integrity sha1-daLotRy3WKdVPWgEpZMteqznXDk=

"@cnakazawa/watch@^1.0.3":
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/@cnakazawa/watch/download/@cnakazawa/watch-1.0.4.tgz"
  integrity sha1-+GSuhQBND8q29QvpFBxNo2jRZWo=
  dependencies:
    exec-sh "^0.3.2"
    minimist "^1.2.0"

"@eslint/eslintrc@^0.4.3":
  version "0.4.3"
  resolved "https://registry.nlark.com/@eslint/eslintrc/download/@eslint/eslintrc-0.4.3.tgz?cache=0&sync_timestamp=1628296487039&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40eslint%2Feslintrc%2Fdownload%2F%40eslint%2Feslintrc-0.4.3.tgz"
  integrity sha1-nkKYHvA1vrPdSa3ResuW6P9vOUw=
  dependencies:
    ajv "^6.12.4"
    debug "^4.1.1"
    espree "^7.3.0"
    globals "^13.9.0"
    ignore "^4.0.6"
    import-fresh "^3.2.1"
    js-yaml "^3.13.1"
    minimatch "^3.0.4"
    strip-json-comments "^3.1.1"

"@humanwhocodes/config-array@^0.5.0":
  version "0.5.0"
  resolved "https://registry.nlark.com/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.5.0.tgz?cache=0&sync_timestamp=1625264021699&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40humanwhocodes%2Fconfig-array%2Fdownload%2F%40humanwhocodes%2Fconfig-array-0.5.0.tgz"
  integrity sha1-FAeWfUxu7Nc4j4Os8er00Mbljvk=
  dependencies:
    "@humanwhocodes/object-schema" "^1.2.0"
    debug "^4.1.1"
    minimatch "^3.0.4"

"@humanwhocodes/object-schema@^1.2.0":
  version "1.2.0"
  resolved "https://registry.nlark.com/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-1.2.0.tgz?cache=0&sync_timestamp=1625264051240&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40humanwhocodes%2Fobject-schema%2Fdownload%2F%40humanwhocodes%2Fobject-schema-1.2.0.tgz"
  integrity sha1-h956+cIxgm/daKxyWPd8Qp4OX88=

"@istanbuljs/load-nyc-config@^1.0.0":
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/@istanbuljs/load-nyc-config/download/@istanbuljs/load-nyc-config-1.1.0.tgz"
  integrity sha1-/T2x1Z7PfPEh6AZQu4ZxL5tV7O0=
  dependencies:
    camelcase "^5.3.1"
    find-up "^4.1.0"
    get-package-type "^0.1.0"
    js-yaml "^3.13.1"
    resolve-from "^5.0.0"

"@istanbuljs/schema@^0.1.2":
  version "0.1.3"
  resolved "https://registry.nlark.com/@istanbuljs/schema/download/@istanbuljs/schema-0.1.3.tgz"
  integrity sha1-5F44TkuOwWvOL9kDr3hFD2v37Jg=

"@jest/console@^26.6.2":
  version "26.6.2"
  resolved "https://registry.nlark.com/@jest/console/download/@jest/console-26.6.2.tgz"
  integrity sha1-TgS8RkAUNYsDq0k3gF7jagrrmPI=
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    chalk "^4.0.0"
    jest-message-util "^26.6.2"
    jest-util "^26.6.2"
    slash "^3.0.0"

"@jest/core@^26.6.3":
  version "26.6.3"
  resolved "https://registry.nlark.com/@jest/core/download/@jest/core-26.6.3.tgz?cache=0&sync_timestamp=1624900576827&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40jest%2Fcore%2Fdownload%2F%40jest%2Fcore-26.6.3.tgz"
  integrity sha1-djn8s4M9dIpGVq2lS94ZMFHkX60=
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/reporters" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    exit "^0.1.2"
    graceful-fs "^4.2.4"
    jest-changed-files "^26.6.2"
    jest-config "^26.6.3"
    jest-haste-map "^26.6.2"
    jest-message-util "^26.6.2"
    jest-regex-util "^26.0.0"
    jest-resolve "^26.6.2"
    jest-resolve-dependencies "^26.6.3"
    jest-runner "^26.6.3"
    jest-runtime "^26.6.3"
    jest-snapshot "^26.6.2"
    jest-util "^26.6.2"
    jest-validate "^26.6.2"
    jest-watcher "^26.6.2"
    micromatch "^4.0.2"
    p-each-series "^2.1.0"
    rimraf "^3.0.0"
    slash "^3.0.0"
    strip-ansi "^6.0.0"

"@jest/environment@^26.6.2":
  version "26.6.2"
  resolved "https://registry.nlark.com/@jest/environment/download/@jest/environment-26.6.2.tgz?cache=0&sync_timestamp=1624900572451&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40jest%2Fenvironment%2Fdownload%2F%40jest%2Fenvironment-26.6.2.tgz"
  integrity sha1-ujZMxy4iHnnMjwqZVVv111d8+Sw=
  dependencies:
    "@jest/fake-timers" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    jest-mock "^26.6.2"

"@jest/fake-timers@^26.6.2":
  version "26.6.2"
  resolved "https://registry.nlark.com/@jest/fake-timers/download/@jest/fake-timers-26.6.2.tgz?cache=0&sync_timestamp=1624900568532&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40jest%2Ffake-timers%2Fdownload%2F%40jest%2Ffake-timers-26.6.2.tgz"
  integrity sha1-RZwym89wzuSvTX4/PmeEgSNTWq0=
  dependencies:
    "@jest/types" "^26.6.2"
    "@sinonjs/fake-timers" "^6.0.1"
    "@types/node" "*"
    jest-message-util "^26.6.2"
    jest-mock "^26.6.2"
    jest-util "^26.6.2"

"@jest/globals@^26.6.2":
  version "26.6.2"
  resolved "https://registry.nlark.com/@jest/globals/download/@jest/globals-26.6.2.tgz"
  integrity sha1-W2E7eKGqJlWukI66Y4zJaiDfcgo=
  dependencies:
    "@jest/environment" "^26.6.2"
    "@jest/types" "^26.6.2"
    expect "^26.6.2"

"@jest/reporters@^26.6.2":
  version "26.6.2"
  resolved "https://registry.nlark.com/@jest/reporters/download/@jest/reporters-26.6.2.tgz?cache=0&sync_timestamp=1624900577476&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40jest%2Freporters%2Fdownload%2F%40jest%2Freporters-26.6.2.tgz"
  integrity sha1-H1GLmWN6Xxgwe9Ps+SdfaIKmZ/Y=
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    chalk "^4.0.0"
    collect-v8-coverage "^1.0.0"
    exit "^0.1.2"
    glob "^7.1.2"
    graceful-fs "^4.2.4"
    istanbul-lib-coverage "^3.0.0"
    istanbul-lib-instrument "^4.0.3"
    istanbul-lib-report "^3.0.0"
    istanbul-lib-source-maps "^4.0.0"
    istanbul-reports "^3.0.2"
    jest-haste-map "^26.6.2"
    jest-resolve "^26.6.2"
    jest-util "^26.6.2"
    jest-worker "^26.6.2"
    slash "^3.0.0"
    source-map "^0.6.0"
    string-length "^4.0.1"
    terminal-link "^2.0.0"
    v8-to-istanbul "^7.0.0"
  optionalDependencies:
    node-notifier "^8.0.0"

"@jest/source-map@^26.6.2":
  version "26.6.2"
  resolved "https://registry.nlark.com/@jest/source-map/download/@jest/source-map-26.6.2.tgz?cache=0&sync_timestamp=1624900561091&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40jest%2Fsource-map%2Fdownload%2F%40jest%2Fsource-map-26.6.2.tgz"
  integrity sha1-Ka9eHi4yTK/MyTbyGDCfVKtp1TU=
  dependencies:
    callsites "^3.0.0"
    graceful-fs "^4.2.4"
    source-map "^0.6.0"

"@jest/test-result@^26.6.2":
  version "26.6.2"
  resolved "https://registry.nlark.com/@jest/test-result/download/@jest/test-result-26.6.2.tgz?cache=0&sync_timestamp=1624900572400&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40jest%2Ftest-result%2Fdownload%2F%40jest%2Ftest-result-26.6.2.tgz"
  integrity sha1-VdpYti3xNFdsyVR276X3lJ4/Xxg=
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/istanbul-lib-coverage" "^2.0.0"
    collect-v8-coverage "^1.0.0"

"@jest/test-sequencer@^26.6.3":
  version "26.6.3"
  resolved "https://registry.nlark.com/@jest/test-sequencer/download/@jest/test-sequencer-26.6.3.tgz"
  integrity sha1-mOikUQCGOIbQdCBej/3Fp+tYKxc=
  dependencies:
    "@jest/test-result" "^26.6.2"
    graceful-fs "^4.2.4"
    jest-haste-map "^26.6.2"
    jest-runner "^26.6.3"
    jest-runtime "^26.6.3"

"@jest/transform@^26.6.2":
  version "26.6.2"
  resolved "https://registry.nlark.com/@jest/transform/download/@jest/transform-26.6.2.tgz?cache=0&sync_timestamp=1624900568914&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40jest%2Ftransform%2Fdownload%2F%40jest%2Ftransform-26.6.2.tgz"
  integrity sha1-WsV8X6GtF7Kq6D5z5FgTiU3PLks=
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/types" "^26.6.2"
    babel-plugin-istanbul "^6.0.0"
    chalk "^4.0.0"
    convert-source-map "^1.4.0"
    fast-json-stable-stringify "^2.0.0"
    graceful-fs "^4.2.4"
    jest-haste-map "^26.6.2"
    jest-regex-util "^26.0.0"
    jest-util "^26.6.2"
    micromatch "^4.0.2"
    pirates "^4.0.1"
    slash "^3.0.0"
    source-map "^0.6.1"
    write-file-atomic "^3.0.0"

"@jest/types@^26.6.2":
  version "26.6.2"
  resolved "https://registry.nlark.com/@jest/types/download/@jest/types-26.6.2.tgz"
  integrity sha1-vvWlMgMOHYii9abZM/hOlyJu1I4=
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^15.0.0"
    chalk "^4.0.0"

"@nestjs/cli@^7.6.0":
  version "7.6.0"
  resolved "https://registry.nlark.com/@nestjs/cli/download/@nestjs/cli-7.6.0.tgz?cache=0&sync_timestamp=1627893636784&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40nestjs%2Fcli%2Fdownload%2F%40nestjs%2Fcli-7.6.0.tgz"
  integrity sha1-Zh+ZtXgoT5EkMHqAd/AEoJGyXnc=
  dependencies:
    "@angular-devkit/core" "11.2.6"
    "@angular-devkit/schematics" "11.2.6"
    "@angular-devkit/schematics-cli" "0.1102.6"
    "@nestjs/schematics" "^7.3.0"
    chalk "3.0.0"
    chokidar "3.5.1"
    cli-table3 "0.5.1"
    commander "4.1.1"
    fork-ts-checker-webpack-plugin "6.2.0"
    inquirer "7.3.3"
    node-emoji "1.10.0"
    ora "5.4.0"
    os-name "4.0.0"
    rimraf "3.0.2"
    shelljs "0.8.4"
    tree-kill "1.2.2"
    tsconfig-paths "3.9.0"
    tsconfig-paths-webpack-plugin "3.5.1"
    typescript "4.2.3"
    webpack "5.28.0"
    webpack-node-externals "2.5.2"

"@nestjs/common@^7.6.18":
  version "7.6.18"
  resolved "https://registry.npmmirror.com/@nestjs/common/-/common-7.6.18.tgz"
  integrity sha512-BUJQHNhWzwWOkS4Ryndzd4HTeRObcAWV2Fh+ermyo3q3xYQQzNoEWclJVL/wZec8AONELwIJ+PSpWI53VP0leg==
  dependencies:
    axios "0.21.1"
    iterare "1.2.1"
    tslib "2.2.0"
    uuid "8.3.2"

"@nestjs/core@^7.6.15":
  version "7.6.18"
  resolved "https://registry.nlark.com/@nestjs/core/download/@nestjs/core-7.6.18.tgz?cache=0&sync_timestamp=1628150806782&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40nestjs%2Fcore%2Fdownload%2F%40nestjs%2Fcore-7.6.18.tgz"
  integrity sha1-NkSPCuf30I8DLh5+U7SkyCroRNc=
  dependencies:
    "@nuxtjs/opencollective" "0.3.2"
    fast-safe-stringify "2.0.7"
    iterare "1.2.1"
    object-hash "2.1.1"
    path-to-regexp "3.2.0"
    tslib "2.2.0"
    uuid "8.3.2"

"@nestjs/jwt@^7.2.0":
  version "7.2.0"
  resolved "https://registry.npmmirror.com/@nestjs/jwt/-/jwt-7.2.0.tgz"
  integrity sha512-uOTqYmWNpu+oS/MrdYjrWXtKGV4HkCYmAEVEFPP/KfiP/7K6fNy+boLllE6cnqESAXh9u0CLa1noAAavs+LHEQ==
  dependencies:
    "@types/jsonwebtoken" "8.5.0"
    jsonwebtoken "8.5.1"

"@nestjs/mapped-types@0.4.1":
  version "0.4.1"
  resolved "https://registry.nlark.com/@nestjs/mapped-types/download/@nestjs/mapped-types-0.4.1.tgz?cache=0&sync_timestamp=1625660910977&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40nestjs%2Fmapped-types%2Fdownload%2F%40nestjs%2Fmapped-types-0.4.1.tgz"
  integrity sha1-5/4Djwvdp7j4WPp5yoUWuPkGmxo=

"@nestjs/mongoose@^7.2.4":
  version "7.2.4"
  resolved "https://registry.nlark.com/@nestjs/mongoose/download/@nestjs/mongoose-7.2.4.tgz"
  integrity sha1-YKUFMqJEJ5cDNENo107NgCrtpKQ=

"@nestjs/passport@^7.1.6":
  version "7.1.6"
  resolved "https://registry.npmmirror.com/@nestjs/passport/-/passport-7.1.6.tgz"
  integrity sha512-iHD/4D01CsKKWg7kUzo4yGNS4m90z1an0eUz+6nBlE5VeEOuCzhGFoBKAt0OKLJLOTEsJKuj4C2b5QfjqXVTyQ==

"@nestjs/platform-express@^7.6.15":
  version "7.6.18"
  resolved "https://registry.nlark.com/@nestjs/platform-express/download/@nestjs/platform-express-7.6.18.tgz"
  integrity sha1-zfRC39hZSPx7Z7vEAH3d74PN1Lk=
  dependencies:
    body-parser "1.19.0"
    cors "2.8.5"
    express "4.17.1"
    multer "1.4.2"
    tslib "2.2.0"

"@nestjs/platform-socket.io@^7.6.18":
  version "7.6.18"
  resolved "https://registry.npmmirror.com/@nestjs/platform-socket.io/-/platform-socket.io-7.6.18.tgz"
  integrity sha512-0zNALNCt3OO+pWE0t4Cd71FIUyKlsTQvaOE0V2Kkn1aDMFj90xli1Dvc/Zd2iYGuwP4QFKjWX+BxBXq6qbHgow==
  dependencies:
    socket.io "2.4.1"
    tslib "2.2.0"

"@nestjs/schematics@^7.3.0":
  version "7.3.1"
  resolved "https://registry.nlark.com/@nestjs/schematics/download/@nestjs/schematics-7.3.1.tgz"
  integrity sha1-aLVZ0uaoqez2yYT4fqp9TjepEL4=
  dependencies:
    "@angular-devkit/core" "11.2.4"
    "@angular-devkit/schematics" "11.2.4"
    fs-extra "9.1.0"
    jsonc-parser "3.0.0"
    pluralize "8.0.0"

"@nestjs/serve-static@^2.2.2":
  version "2.2.2"
  resolved "https://registry.nlark.com/@nestjs/serve-static/download/@nestjs/serve-static-2.2.2.tgz"
  integrity sha1-jp3C/GwELdrFEzuVfWvCXZ+PoiU=
  dependencies:
    path-to-regexp "0.1.7"

"@nestjs/swagger@^4.8.0":
  version "4.8.2"
  resolved "https://registry.nlark.com/@nestjs/swagger/download/@nestjs/swagger-4.8.2.tgz"
  integrity sha1-Cgs8obJRRueXynet3Z+pf4JAbBw=
  dependencies:
    "@nestjs/mapped-types" "0.4.1"
    lodash "4.17.21"
    path-to-regexp "3.2.0"

"@nestjs/testing@^7.6.15":
  version "7.6.18"
  resolved "https://registry.nlark.com/@nestjs/testing/download/@nestjs/testing-7.6.18.tgz"
  integrity sha1-tME3tbbC+xjFFgLTOgg82XxkgoM=
  dependencies:
    optional "0.1.4"
    tslib "2.2.0"

"@nestjs/typeorm@^7.1.5":
  version "7.1.5"
  resolved "https://registry.nlark.com/@nestjs/typeorm/download/@nestjs/typeorm-7.1.5.tgz"
  integrity sha1-UOO/hf+M941H2N0ZIQxfArSI9eM=
  dependencies:
    uuid "8.3.1"

"@nestjs/websockets@^7.6.18":
  version "7.6.18"
  resolved "https://registry.npmmirror.com/@nestjs/websockets/-/websockets-7.6.18.tgz"
  integrity sha512-QBp+zVAGMksNKa5HnfpZfXzO8FbDO+vGcGbxuXLPqEeH2/HsS1O09uPvi4uG02WtGWIJ+DewWdq/odsukl4lDg==
  dependencies:
    iterare "1.2.1"
    tslib "2.2.0"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://registry.nlark.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "https://registry.nlark.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40nodelib%2Ffs.stat%2Fdownload%2F%40nodelib%2Ffs.stat-2.0.5.tgz"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "https://registry.nlark.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz?cache=0&sync_timestamp=1625769815389&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40nodelib%2Ffs.walk%2Fdownload%2F%40nodelib%2Ffs.walk-1.2.8.tgz"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@nuxtjs/opencollective@0.3.2":
  version "0.3.2"
  resolved "https://registry.npm.taobao.org/@nuxtjs/opencollective/download/@nuxtjs/opencollective-0.3.2.tgz"
  integrity sha1-YgzhBE96x3GF6CXhk2EVuzjiaBw=
  dependencies:
    chalk "^4.1.0"
    consola "^2.15.0"
    node-fetch "^2.6.1"

"@schematics/schematics@0.1102.6":
  version "0.1102.6"
  resolved "https://registry.nlark.com/@schematics/schematics/download/@schematics/schematics-0.1102.6.tgz"
  integrity sha1-LOAvfBFVhHFijq/rNPqqf1q0siw=
  dependencies:
    "@angular-devkit/core" "11.2.6"
    "@angular-devkit/schematics" "11.2.6"

"@sinonjs/commons@^1.7.0":
  version "1.8.3"
  resolved "https://registry.npm.taobao.org/@sinonjs/commons/download/@sinonjs/commons-1.8.3.tgz?cache=0&sync_timestamp=1617868496285&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40sinonjs%2Fcommons%2Fdownload%2F%40sinonjs%2Fcommons-1.8.3.tgz"
  integrity sha1-OALd0hpQqUm2ch3dcto25n5/Gy0=
  dependencies:
    type-detect "4.0.8"

"@sinonjs/fake-timers@^6.0.1":
  version "6.0.1"
  resolved "https://registry.nlark.com/@sinonjs/fake-timers/download/@sinonjs/fake-timers-6.0.1.tgz?cache=0&sync_timestamp=1622212451029&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40sinonjs%2Ffake-timers%2Fdownload%2F%40sinonjs%2Ffake-timers-6.0.1.tgz"
  integrity sha1-KTZ0/MsyYqx4LHqt/eyoaxDHXEA=
  dependencies:
    "@sinonjs/commons" "^1.7.0"

"@sqltools/formatter@^1.2.2":
  version "1.2.3"
  resolved "https://registry.npm.taobao.org/@sqltools/formatter/download/@sqltools/formatter-1.2.3.tgz"
  integrity sha1-EYVyZhCsw3MX3asRw8f5BmlmvSA=

"@tootallnate/once@1":
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/@tootallnate/once/download/@tootallnate/once-1.1.2.tgz"
  integrity sha1-zLkURTYBeaBOf+av94wA/8Hur4I=

"@typegoose/typegoose@^7.6.1":
  version "7.6.3"
  resolved "https://registry.nlark.com/@typegoose/typegoose/download/@typegoose/typegoose-7.6.3.tgz"
  integrity sha1-HbtUy1gBKUYuNGD1+Usvkp+DyJ8=
  dependencies:
    lodash "^4.17.20"
    loglevel "^1.7.0"
    reflect-metadata "^0.1.13"
    semver "^7.3.2"
    tslib "^2.0.1"

"@types/babel__core@^7.0.0", "@types/babel__core@^7.1.7":
  version "7.1.15"
  resolved "https://registry.nlark.com/@types/babel__core/download/@types/babel__core-7.1.15.tgz"
  integrity sha1-LM+xrVWgLIP44K0yfLwzL1XrECQ=
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.6.3"
  resolved "https://registry.nlark.com/@types/babel__generator/download/@types/babel__generator-7.6.3.tgz"
  integrity sha1-9Fa0ss55E392iqEw0kI9LwzPq6U=
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.4.1"
  resolved "https://registry.nlark.com/@types/babel__template/download/@types/babel__template-7.4.1.tgz"
  integrity sha1-PRpI/Z1sDt/Vby/1eNrtSPNsiWk=
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.4", "@types/babel__traverse@^7.0.6":
  version "7.14.2"
  resolved "https://registry.nlark.com/@types/babel__traverse/download/@types/babel__traverse-7.14.2.tgz?cache=0&sync_timestamp=1625754682738&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fbabel__traverse%2Fdownload%2F%40types%2Fbabel__traverse-7.14.2.tgz"
  integrity sha1-/81HC7s/i/MEgWePtVAieMqDOkM=
  dependencies:
    "@babel/types" "^7.3.0"

"@types/bcrypt@^5.0.0":
  version "5.0.0"
  resolved "https://registry.npmmirror.com/@types/bcrypt/-/bcrypt-5.0.0.tgz"
  integrity sha512-agtcFKaruL8TmcvqbndlqHPSJgsolhf/qPWchFlgnW1gECTN/nKbFcoFnvKAQRFfKbh+BO6A3SWdJu9t+xF3Lw==
  dependencies:
    "@types/node" "*"

"@types/body-parser@*":
  version "1.19.1"
  resolved "https://registry.nlark.com/@types/body-parser/download/@types/body-parser-1.19.1.tgz?cache=0&sync_timestamp=1625595663636&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fbody-parser%2Fdownload%2F%40types%2Fbody-parser-1.19.1.tgz"
  integrity sha1-DAF0xCp9AXuBgwPUtdlpywt1kpw=
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/bson@*":
  version "4.0.5"
  resolved "https://registry.nlark.com/@types/bson/download/@types/bson-4.0.5.tgz?cache=0&sync_timestamp=1627566133448&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fbson%2Fdownload%2F%40types%2Fbson-4.0.5.tgz"
  integrity sha1-ng4dGm+IZkg/loaKmzO8gEkmsfw=
  dependencies:
    "@types/node" "*"

"@types/component-emitter@^1.2.10":
  version "1.2.10"
  resolved "https://registry.nlark.com/@types/component-emitter/download/@types/component-emitter-1.2.10.tgz?cache=0&sync_timestamp=1621240806137&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fcomponent-emitter%2Fdownload%2F%40types%2Fcomponent-emitter-1.2.10.tgz"
  integrity sha1-71sVibnxZURkLkc9tepWORB+8+o=

"@types/connect@*":
  version "3.4.35"
  resolved "https://registry.nlark.com/@types/connect/download/@types/connect-3.4.35.tgz"
  integrity sha1-X89q5EXkAh0fwiGaSHPMc6O7KtE=
  dependencies:
    "@types/node" "*"

"@types/cookie@^0.4.0":
  version "0.4.1"
  resolved "https://registry.nlark.com/@types/cookie/download/@types/cookie-0.4.1.tgz?cache=0&sync_timestamp=1625603754133&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fcookie%2Fdownload%2F%40types%2Fcookie-0.4.1.tgz"
  integrity sha1-v9AsHyIkVnZ2wVRRmfh8OoYdh40=

"@types/cookiejar@*":
  version "2.1.2"
  resolved "https://registry.nlark.com/@types/cookiejar/download/@types/cookiejar-2.1.2.tgz"
  integrity sha1-Zq2TMfY/6KPT2djG45Bt0Q9kRug=

"@types/cors@^2.8.10":
  version "2.8.12"
  resolved "https://registry.nlark.com/@types/cors/download/@types/cors-2.8.12.tgz"
  integrity sha1-ayxRCnrXA56Y57jT1lmPQ1nlwIA=

"@types/eslint-scope@^3.7.0":
  version "3.7.1"
  resolved "https://registry.nlark.com/@types/eslint-scope/download/@types/eslint-scope-3.7.1.tgz?cache=0&sync_timestamp=1625598848492&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Feslint-scope%2Fdownload%2F%40types%2Feslint-scope-3.7.1.tgz"
  integrity sha1-jcOQp7T53Z8ShGKe/OmC5BYSEW4=
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*":
  version "7.28.0"
  resolved "https://registry.nlark.com/@types/eslint/download/@types/eslint-7.28.0.tgz"
  integrity sha1-fkHySB0wHGjhT0g/4QsBd1POjVo=
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*":
  version "0.0.50"
  resolved "https://registry.nlark.com/@types/estree/download/@types/estree-0.0.50.tgz"
  integrity sha1-Hgyqk2TT/M0pMcPtlv2+ql1MyoM=

"@types/estree@^0.0.46":
  version "0.0.46"
  resolved "https://registry.nlark.com/@types/estree/download/@types/estree-0.0.46.tgz"
  integrity sha1-D7a/u+q9ejCIBQSZM2nEvx3qsf4=

"@types/express-serve-static-core@^4.17.18":
  version "4.17.24"
  resolved "https://registry.nlark.com/@types/express-serve-static-core/download/@types/express-serve-static-core-4.17.24.tgz"
  integrity sha1-6kH5O/fg1ZzVp2ZlBo7WqraBXAc=
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"

"@types/express@*", "@types/express@^4.17.11":
  version "4.17.13"
  resolved "https://registry.nlark.com/@types/express/download/@types/express-4.17.13.tgz?cache=0&sync_timestamp=1625604604626&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fexpress%2Fdownload%2F%40types%2Fexpress-4.17.13.tgz"
  integrity sha1-p24plXKJmbq1GjP6vOHXBaNwkDQ=
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.18"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/graceful-fs@^4.1.2":
  version "4.1.5"
  resolved "https://registry.nlark.com/@types/graceful-fs/download/@types/graceful-fs-4.1.5.tgz?cache=0&sync_timestamp=1621241272949&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fgraceful-fs%2Fdownload%2F%40types%2Fgraceful-fs-4.1.5.tgz"
  integrity sha1-If+6DZjaQ1DbZIkfkqnl2zzbThU=
  dependencies:
    "@types/node" "*"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0", "@types/istanbul-lib-coverage@^2.0.1":
  version "2.0.3"
  resolved "https://registry.nlark.com/@types/istanbul-lib-coverage/download/@types/istanbul-lib-coverage-2.0.3.tgz"
  integrity sha1-S6jdtyAiH0MuRDvV+RF/0iz9R2I=

"@types/istanbul-lib-report@*":
  version "3.0.0"
  resolved "https://registry.nlark.com/@types/istanbul-lib-report/download/@types/istanbul-lib-report-3.0.0.tgz"
  integrity sha1-wUwk8Y6oGQwRjudWK3/5mjZVJoY=
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.0":
  version "3.0.1"
  resolved "https://registry.nlark.com/@types/istanbul-reports/download/@types/istanbul-reports-3.0.1.tgz"
  integrity sha1-kVP+mLuivVZaY63ZQ21vDX+EaP8=
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/jest@^26.0.22":
  version "26.0.24"
  resolved "https://registry.nlark.com/@types/jest/download/@types/jest-26.0.24.tgz?cache=0&sync_timestamp=1625610505834&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fjest%2Fdownload%2F%40types%2Fjest-26.0.24.tgz"
  integrity sha1-lD0Rl2sWc5GFkToZNuDeDEp9WVo=
  dependencies:
    jest-diff "^26.0.0"
    pretty-format "^26.0.0"

"@types/json-schema@*", "@types/json-schema@^7.0.4", "@types/json-schema@^7.0.5", "@types/json-schema@^7.0.7", "@types/json-schema@^7.0.8":
  version "7.0.9"
  resolved "https://registry.nlark.com/@types/json-schema/download/@types/json-schema-7.0.9.tgz?cache=0&sync_timestamp=1627999424913&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fjson-schema%2Fdownload%2F%40types%2Fjson-schema-7.0.9.tgz"
  integrity sha1-l+3JA36gw4WFMgsolk3eOznkZg0=

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "https://registry.npm.taobao.org/@types/json5/download/@types/json5-0.0.29.tgz"
  integrity sha1-7ihweulOEdK4J7y+UnC86n8+ce4=

"@types/jsonwebtoken@*":
  version "8.5.4"
  resolved "https://registry.nlark.com/@types/jsonwebtoken/download/@types/jsonwebtoken-8.5.4.tgz"
  integrity sha1-UMyvCqb117mVbnD+Mjt25YKZGRM=
  dependencies:
    "@types/node" "*"

"@types/jsonwebtoken@8.5.0":
  version "8.5.0"
  resolved "https://registry.nlark.com/@types/jsonwebtoken/download/@types/jsonwebtoken-8.5.0.tgz"
  integrity sha1-JTHV4wCAOqYyebIywBSs94DJgcU=
  dependencies:
    "@types/node" "*"

"@types/mime@^1":
  version "1.3.2"
  resolved "https://registry.nlark.com/@types/mime/download/@types/mime-1.3.2.tgz"
  integrity sha1-k+Jb+e51/g/YC1lLxP6w6GIRG1o=

"@types/mockjs@^1.0.3":
  version "1.0.4"
  resolved "https://registry.nlark.com/@types/mockjs/download/@types/mockjs-1.0.4.tgz?cache=0&sync_timestamp=1625771765667&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fmockjs%2Fdownload%2F%40types%2Fmockjs-1.0.4.tgz"
  integrity sha1-5waVHV4ztPCku3Ox+LEk4m8IHeA=

"@types/mongodb@^3.5.27":
  version "3.6.20"
  resolved "https://registry.nlark.com/@types/mongodb/download/@types/mongodb-3.6.20.tgz"
  integrity sha1-t8XFgGRPY2QAK2Sa8cBsPARU4dI=
  dependencies:
    "@types/bson" "*"
    "@types/node" "*"

"@types/mongoose@^5.11.67":
  version "5.11.97"
  resolved "https://registry.nlark.com/@types/mongoose/download/@types/mongoose-5.11.97.tgz"
  integrity sha1-gLA1fz3mgH61lyYvUuScPhPuFNg=
  dependencies:
    mongoose "*"

"@types/multer@^1.4.7":
  version "1.4.7"
  resolved "https://registry.npmmirror.com/@types/multer/-/multer-1.4.7.tgz"
  integrity sha512-/SNsDidUFCvqqcWDwxv2feww/yqhNeTRL5CVoL3jU4Goc4kKEL10T7Eye65ZqPNi4HRx8sAEX59pV1aEH7drNA==
  dependencies:
    "@types/express" "*"

"@types/node@*", "@types/node@^14.14.36":
  version "14.17.9"
  resolved "https://registry.nlark.com/@types/node/download/@types/node-14.17.9.tgz?cache=0&sync_timestamp=1628211786489&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-14.17.9.tgz"
  integrity sha1-uXwFfmE4rbe3IN8r0CZLA8n1BP0=

"@types/node@>=10.0.0":
  version "16.4.13"
  resolved "https://registry.nlark.com/@types/node/download/@types/node-16.4.13.tgz?cache=0&sync_timestamp=1628211786489&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.4.13.tgz"
  integrity sha1-ff2cFGYe3GXMzUOinrRUF0ZCNw0=

"@types/normalize-package-data@^2.4.0":
  version "2.4.1"
  resolved "https://registry.nlark.com/@types/normalize-package-data/download/@types/normalize-package-data-2.4.1.tgz?cache=0&sync_timestamp=1625675733240&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fnormalize-package-data%2Fdownload%2F%40types%2Fnormalize-package-data-2.4.1.tgz"
  integrity sha1-0zV0eaD9/dWQf+Z+F+CoXJBuEwE=

"@types/parse-json@^4.0.0":
  version "4.0.0"
  resolved "https://registry.nlark.com/@types/parse-json/download/@types/parse-json-4.0.0.tgz?cache=0&sync_timestamp=1621242198435&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fparse-json%2Fdownload%2F%40types%2Fparse-json-4.0.0.tgz"
  integrity sha1-L4u0QUNNFjs1+4/9zNcTiSf/uMA=

"@types/passport-jwt@^3.0.6":
  version "3.0.6"
  resolved "https://registry.npmmirror.com/@types/passport-jwt/-/passport-jwt-3.0.6.tgz"
  integrity sha512-cmAAMIRTaEwpqxlrZyiEY9kdibk94gP5KTF8AT1Ra4rWNZYHNMreqhKUEeC5WJtuN5SJZjPQmV+XO2P5PlnvNQ==
  dependencies:
    "@types/express" "*"
    "@types/jsonwebtoken" "*"
    "@types/passport-strategy" "*"

"@types/passport-local@^1.0.34":
  version "1.0.34"
  resolved "https://registry.npmmirror.com/@types/passport-local/-/passport-local-1.0.34.tgz"
  integrity sha512-PSc07UdYx+jhadySxxIYWuv6sAnY5e+gesn/5lkPKfBeGuIYn9OPR+AAEDq73VRUh6NBTpvE/iPE62rzZUslog==
  dependencies:
    "@types/express" "*"
    "@types/passport" "*"
    "@types/passport-strategy" "*"

"@types/passport-strategy@*":
  version "0.2.35"
  resolved "https://registry.nlark.com/@types/passport-strategy/download/@types/passport-strategy-0.2.35.tgz"
  integrity sha1-5S9SEieepz8C2bBq9n7+nO/OLQw=
  dependencies:
    "@types/express" "*"
    "@types/passport" "*"

"@types/passport@*":
  version "1.0.7"
  resolved "https://registry.nlark.com/@types/passport/download/@types/passport-1.0.7.tgz"
  integrity sha1-hYkvFJMhaBWMhq7K/QaxL1Q5Rno=
  dependencies:
    "@types/express" "*"

"@types/prettier@^2.0.0":
  version "2.3.2"
  resolved "https://registry.nlark.com/@types/prettier/download/@types/prettier-2.3.2.tgz?cache=0&sync_timestamp=1625678505137&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fprettier%2Fdownload%2F%40types%2Fprettier-2.3.2.tgz"
  integrity sha1-/IwoJeTtIUJHO0qBBk5uCBRj0bM=

"@types/qs@*":
  version "6.9.7"
  resolved "https://registry.nlark.com/@types/qs/download/@types/qs-6.9.7.tgz"
  integrity sha1-Y7t9Bn2xB8weRXwwO8JdUR/r9ss=

"@types/range-parser@*":
  version "1.2.4"
  resolved "https://registry.nlark.com/@types/range-parser/download/@types/range-parser-1.2.4.tgz"
  integrity sha1-zWZ7z90CUhOq+3ylkVqTJZCs3Nw=

"@types/request-ip@^0.0.37":
  version "0.0.37"
  resolved "https://registry.npmmirror.com/@types/request-ip/-/request-ip-0.0.37.tgz"
  integrity sha512-uw6/i3rQnpznxD7LtLaeuZytLhKZK6bRoTS6XVJlwxIOoOpEBU7bgKoVXDNtOg4Xl6riUKHa9bjMVrL6ESqYlQ==
  dependencies:
    "@types/node" "*"

"@types/serve-static@*":
  version "1.13.10"
  resolved "https://registry.nlark.com/@types/serve-static/download/@types/serve-static-1.13.10.tgz?cache=0&sync_timestamp=1625590985769&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fserve-static%2Fdownload%2F%40types%2Fserve-static-1.13.10.tgz"
  integrity sha1-9eDOh5fS18xevtpIpSyWxPpHqNk=
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/socket.io@^3.0.2":
  version "3.0.2"
  resolved "https://registry.nlark.com/@types/socket.io/download/@types/socket.io-3.0.2.tgz"
  integrity sha1-YGyWOeP5O7hFTLqPXwooPUeRd1k=
  dependencies:
    socket.io "*"

"@types/stack-utils@^2.0.0":
  version "2.0.1"
  resolved "https://registry.nlark.com/@types/stack-utils/download/@types/stack-utils-2.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fstack-utils%2Fdownload%2F%40types%2Fstack-utils-2.0.1.tgz"
  integrity sha1-IPGClPeX8iCbX2XI47XI6CYdEnw=

"@types/superagent@*":
  version "4.1.12"
  resolved "https://registry.nlark.com/@types/superagent/download/@types/superagent-4.1.12.tgz"
  integrity sha1-+taMZxKTaJKtJM+U8vegfMdJ/Q8=
  dependencies:
    "@types/cookiejar" "*"
    "@types/node" "*"

"@types/supertest@^2.0.10":
  version "2.0.11"
  resolved "https://registry.nlark.com/@types/supertest/download/@types/supertest-2.0.11.tgz"
  integrity sha1-LnD2nyILx3tPZg1ywuGkIx9Ep30=
  dependencies:
    "@types/superagent" "*"

"@types/yargs-parser@*":
  version "20.2.1"
  resolved "https://registry.nlark.com/@types/yargs-parser/download/@types/yargs-parser-20.2.1.tgz"
  integrity sha1-O5ziSJkZ2eT+pDm3aRarw0st8Sk=

"@types/yargs@^15.0.0":
  version "15.0.14"
  resolved "https://registry.nlark.com/@types/yargs/download/@types/yargs-15.0.14.tgz?cache=0&sync_timestamp=1625519310139&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40types%2Fyargs%2Fdownload%2F%40types%2Fyargs-15.0.14.tgz"
  integrity sha1-Jtgh3biecEkhYLZtEKDrbfj2+wY=
  dependencies:
    "@types/yargs-parser" "*"

"@types/zen-observable@0.8.3":
  version "0.8.3"
  resolved "https://registry.nlark.com/@types/zen-observable/download/@types/zen-observable-0.8.3.tgz"
  integrity sha1-eB02DCgkNklLMv59n3+OZLMRiqM=

"@typescript-eslint/eslint-plugin@^4.19.0":
  version "4.29.0"
  resolved "https://registry.nlark.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-4.29.0.tgz?cache=0&sync_timestamp=1627925125510&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40typescript-eslint%2Feslint-plugin%2Fdownload%2F%40typescript-eslint%2Feslint-plugin-4.29.0.tgz"
  integrity sha1-uGbJzRk7+rpeibreABVinr6yeZY=
  dependencies:
    "@typescript-eslint/experimental-utils" "4.29.0"
    "@typescript-eslint/scope-manager" "4.29.0"
    debug "^4.3.1"
    functional-red-black-tree "^1.0.1"
    regexpp "^3.1.0"
    semver "^7.3.5"
    tsutils "^3.21.0"

"@typescript-eslint/experimental-utils@4.29.0":
  version "4.29.0"
  resolved "https://registry.nlark.com/@typescript-eslint/experimental-utils/download/@typescript-eslint/experimental-utils-4.29.0.tgz?cache=0&sync_timestamp=1627924854694&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40typescript-eslint%2Fexperimental-utils%2Fdownload%2F%40typescript-eslint%2Fexperimental-utils-4.29.0.tgz"
  integrity sha1-GbFBdgLQ4e8yWzMS7pX2EiBULfU=
  dependencies:
    "@types/json-schema" "^7.0.7"
    "@typescript-eslint/scope-manager" "4.29.0"
    "@typescript-eslint/types" "4.29.0"
    "@typescript-eslint/typescript-estree" "4.29.0"
    eslint-scope "^5.1.1"
    eslint-utils "^3.0.0"

"@typescript-eslint/parser@^4.19.0":
  version "4.29.0"
  resolved "https://registry.nlark.com/@typescript-eslint/parser/download/@typescript-eslint/parser-4.29.0.tgz?cache=0&sync_timestamp=1627924723468&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40typescript-eslint%2Fparser%2Fdownload%2F%40typescript-eslint%2Fparser-4.29.0.tgz"
  integrity sha1-5TZ8o8Y2NrtdjgdI/Lq3pPSgQok=
  dependencies:
    "@typescript-eslint/scope-manager" "4.29.0"
    "@typescript-eslint/types" "4.29.0"
    "@typescript-eslint/typescript-estree" "4.29.0"
    debug "^4.3.1"

"@typescript-eslint/scope-manager@4.29.0":
  version "4.29.0"
  resolved "https://registry.nlark.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-4.29.0.tgz?cache=0&sync_timestamp=1627925116667&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40typescript-eslint%2Fscope-manager%2Fdownload%2F%40typescript-eslint%2Fscope-manager-4.29.0.tgz"
  integrity sha1-z1R0+HMhvt9BbvZYObaTvd2DhZk=
  dependencies:
    "@typescript-eslint/types" "4.29.0"
    "@typescript-eslint/visitor-keys" "4.29.0"

"@typescript-eslint/types@4.29.0":
  version "4.29.0"
  resolved "https://registry.nlark.com/@typescript-eslint/types/download/@typescript-eslint/types-4.29.0.tgz?cache=0&sync_timestamp=1627924716320&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40typescript-eslint%2Ftypes%2Fdownload%2F%40typescript-eslint%2Ftypes-4.29.0.tgz"
  integrity sha1-yPGh5EQepKypsxCSQa28FF9/ik4=

"@typescript-eslint/typescript-estree@4.29.0":
  version "4.29.0"
  resolved "https://registry.nlark.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-4.29.0.tgz?cache=0&sync_timestamp=1627924854480&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40typescript-eslint%2Ftypescript-estree%2Fdownload%2F%40typescript-eslint%2Ftypescript-estree-4.29.0.tgz"
  integrity sha1-r3q1R3V7hskb/bxU/4aEVBCFYlY=
  dependencies:
    "@typescript-eslint/types" "4.29.0"
    "@typescript-eslint/visitor-keys" "4.29.0"
    debug "^4.3.1"
    globby "^11.0.3"
    is-glob "^4.0.1"
    semver "^7.3.5"
    tsutils "^3.21.0"

"@typescript-eslint/visitor-keys@4.29.0":
  version "4.29.0"
  resolved "https://registry.nlark.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-4.29.0.tgz?cache=0&sync_timestamp=1627924717405&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40typescript-eslint%2Fvisitor-keys%2Fdownload%2F%40typescript-eslint%2Fvisitor-keys-4.29.0.tgz"
  integrity sha1-H/YPJA3vTYXqaNT9Lk6XWbeFDAQ=
  dependencies:
    "@typescript-eslint/types" "4.29.0"
    eslint-visitor-keys "^2.0.0"

"@webassemblyjs/ast@1.11.0":
  version "1.11.0"
  resolved "https://registry.nlark.com/@webassemblyjs/ast/download/@webassemblyjs/ast-1.11.0.tgz?cache=0&sync_timestamp=1625473459015&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fast%2Fdownload%2F%40webassemblyjs%2Fast-1.11.0.tgz"
  integrity sha1-papnnv3J5RcHpCBxOdpXkgVVlh8=
  dependencies:
    "@webassemblyjs/helper-numbers" "1.11.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.0"

"@webassemblyjs/floating-point-hex-parser@1.11.0":
  version "1.11.0"
  resolved "https://registry.nlark.com/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.11.0.tgz"
  integrity sha1-NNYgUvRTzUMQHXLqtJZqAiWHlHw=

"@webassemblyjs/helper-api-error@1.11.0":
  version "1.11.0"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.11.0.tgz?cache=0&sync_timestamp=1625473346773&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fhelper-api-error%2Fdownload%2F%40webassemblyjs%2Fhelper-api-error-1.11.0.tgz"
  integrity sha1-quqPs7kj9KqptRL/VBsBP/to0tQ=

"@webassemblyjs/helper-buffer@1.11.0":
  version "1.11.0"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.11.0.tgz?cache=0&sync_timestamp=1625473344792&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fhelper-buffer%2Fdownload%2F%40webassemblyjs%2Fhelper-buffer-1.11.0.tgz"
  integrity sha1-0CbCXRdeOIp9valpTpHnQ8vptkI=

"@webassemblyjs/helper-numbers@1.11.0":
  version "1.11.0"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-numbers/download/@webassemblyjs/helper-numbers-1.11.0.tgz?cache=0&sync_timestamp=1625473485159&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fhelper-numbers%2Fdownload%2F%40webassemblyjs%2Fhelper-numbers-1.11.0.tgz"
  integrity sha1-erBBctVOMSzG6kKG19n6J8iM1Pk=
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.11.0"
    "@webassemblyjs/helper-api-error" "1.11.0"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.11.0":
  version "1.11.0"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.11.0.tgz"
  integrity sha1-hf3NpBKZAv6G+Bq/fnI2lT7FpOE=

"@webassemblyjs/helper-wasm-section@1.11.0":
  version "1.11.0"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.11.0.tgz?cache=0&sync_timestamp=1625473466570&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fhelper-wasm-section%2Fdownload%2F%40webassemblyjs%2Fhelper-wasm-section-1.11.0.tgz"
  integrity sha1-nOLMiTACYlCcgBtK8RPRyiXBp1s=
  dependencies:
    "@webassemblyjs/ast" "1.11.0"
    "@webassemblyjs/helper-buffer" "1.11.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.0"
    "@webassemblyjs/wasm-gen" "1.11.0"

"@webassemblyjs/ieee754@1.11.0":
  version "1.11.0"
  resolved "https://registry.nlark.com/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.11.0.tgz"
  integrity sha1-RpddWD+YKPXQlKwhDiGUQcTm9c8=
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.11.0":
  version "1.11.0"
  resolved "https://registry.nlark.com/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.11.0.tgz?cache=0&sync_timestamp=1625473342433&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fleb128%2Fdownload%2F%40webassemblyjs%2Fleb128-1.11.0.tgz"
  integrity sha1-9zU94d84qiAcup+4i0P0H3X/QDs=
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.11.0":
  version "1.11.0"
  resolved "https://registry.nlark.com/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.11.0.tgz"
  integrity sha1-huSPlZz0ng5QkfBppwm4YvWiyt8=

"@webassemblyjs/wasm-edit@1.11.0":
  version "1.11.0"
  resolved "https://registry.nlark.com/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.11.0.tgz?cache=0&sync_timestamp=1625473463093&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fwasm-edit%2Fdownload%2F%40webassemblyjs%2Fwasm-edit-1.11.0.tgz"
  integrity sha1-7kpcn2dwRqIQVCrmOJcJTCAny3g=
  dependencies:
    "@webassemblyjs/ast" "1.11.0"
    "@webassemblyjs/helper-buffer" "1.11.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.0"
    "@webassemblyjs/helper-wasm-section" "1.11.0"
    "@webassemblyjs/wasm-gen" "1.11.0"
    "@webassemblyjs/wasm-opt" "1.11.0"
    "@webassemblyjs/wasm-parser" "1.11.0"
    "@webassemblyjs/wast-printer" "1.11.0"

"@webassemblyjs/wasm-gen@1.11.0":
  version "1.11.0"
  resolved "https://registry.nlark.com/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.11.0.tgz?cache=0&sync_timestamp=1625473361759&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fwasm-gen%2Fdownload%2F%40webassemblyjs%2Fwasm-gen-1.11.0.tgz"
  integrity sha1-PNs15wCC1Co1FmmI3aZPJM65er4=
  dependencies:
    "@webassemblyjs/ast" "1.11.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.0"
    "@webassemblyjs/ieee754" "1.11.0"
    "@webassemblyjs/leb128" "1.11.0"
    "@webassemblyjs/utf8" "1.11.0"

"@webassemblyjs/wasm-opt@1.11.0":
  version "1.11.0"
  resolved "https://registry.nlark.com/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.11.0.tgz?cache=0&sync_timestamp=1625473467198&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fwasm-opt%2Fdownload%2F%40webassemblyjs%2Fwasm-opt-1.11.0.tgz"
  integrity sha1-FjiuGIE39LsDH1aKQTzSTTL5KXg=
  dependencies:
    "@webassemblyjs/ast" "1.11.0"
    "@webassemblyjs/helper-buffer" "1.11.0"
    "@webassemblyjs/wasm-gen" "1.11.0"
    "@webassemblyjs/wasm-parser" "1.11.0"

"@webassemblyjs/wasm-parser@1.11.0":
  version "1.11.0"
  resolved "https://registry.nlark.com/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.11.0.tgz?cache=0&sync_timestamp=1625473464593&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fwasm-parser%2Fdownload%2F%40webassemblyjs%2Fwasm-parser-1.11.0.tgz"
  integrity sha1-PmgLiDDVsT0eyGzELzjz1KdwB1Q=
  dependencies:
    "@webassemblyjs/ast" "1.11.0"
    "@webassemblyjs/helper-api-error" "1.11.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.0"
    "@webassemblyjs/ieee754" "1.11.0"
    "@webassemblyjs/leb128" "1.11.0"
    "@webassemblyjs/utf8" "1.11.0"

"@webassemblyjs/wast-printer@1.11.0":
  version "1.11.0"
  resolved "https://registry.nlark.com/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.11.0.tgz"
  integrity sha1-aA0falNl1tQBl0qOlJ4FR04fq34=
  dependencies:
    "@webassemblyjs/ast" "1.11.0"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "https://registry.nlark.com/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz"
  integrity sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "https://registry.nlark.com/@xtuc/long/download/@xtuc/long-4.2.2.tgz"
  integrity sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=

abab@^2.0.3, abab@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npm.taobao.org/abab/download/abab-2.0.5.tgz?cache=0&sync_timestamp=1599850271460&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fabab%2Fdownload%2Fabab-2.0.5.tgz"
  integrity sha1-wLZ4+zLWD8EhnHhNaoJv44Wut5o=

abbrev@1:
  version "1.1.1"
  resolved "https://registry.nlark.com/abbrev/download/abbrev-1.1.1.tgz"
  integrity sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg=

accepts@~1.3.4, accepts@~1.3.7:
  version "1.3.7"
  resolved "https://registry.npm.taobao.org/accepts/download/accepts-1.3.7.tgz"
  integrity sha1-UxvHJlF6OytB+FACHGzBXqq1B80=
  dependencies:
    mime-types "~2.1.24"
    negotiator "0.6.2"

acorn-globals@^6.0.0:
  version "6.0.0"
  resolved "https://registry.nlark.com/acorn-globals/download/acorn-globals-6.0.0.tgz"
  integrity sha1-Rs3Tnw+P8IqHZhm1X1rIptx3C0U=
  dependencies:
    acorn "^7.1.1"
    acorn-walk "^7.1.1"

acorn-jsx@^5.3.1:
  version "5.3.2"
  resolved "https://registry.nlark.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn-walk@^7.1.1:
  version "7.2.0"
  resolved "https://registry.nlark.com/acorn-walk/download/acorn-walk-7.2.0.tgz"
  integrity sha1-DeiJpgEgOQmw++B7iTjcIdLpZ7w=

acorn@^7.1.1, acorn@^7.4.0:
  version "7.4.1"
  resolved "https://registry.nlark.com/acorn/download/acorn-7.4.1.tgz"
  integrity sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=

acorn@^8.0.4, acorn@^8.2.4:
  version "8.4.1"
  resolved "https://registry.nlark.com/acorn/download/acorn-8.4.1.tgz"
  integrity sha1-VsNiUfx8q8cJatwY8Fr+gUMhoow=

adler-32@~1.3.0:
  version "1.3.1"
  resolved "https://registry.npmmirror.com/adler-32/-/adler-32-1.3.1.tgz"
  integrity sha512-ynZ4w/nUUv5rrsR8UUGoe1VC9hZj6V5hU9Qw1HlMDJGEJw5S7TfTErWTjMys6M7vr0YWcPqs3qAr4ss0nDfP+A==

after@0.8.2:
  version "0.8.2"
  resolved "https://registry.npm.taobao.org/after/download/after-0.8.2.tgz"
  integrity sha1-/ts5T58OAqqXaOcCvaI7UF+ufh8=

agent-base@6:
  version "6.0.2"
  resolved "https://registry.nlark.com/agent-base/download/agent-base-6.0.2.tgz"
  integrity sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c=
  dependencies:
    debug "4"

ajv-keywords@^3.4.1, ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "https://registry.npm.taobao.org/ajv-keywords/download/ajv-keywords-3.5.2.tgz"
  integrity sha1-MfKdpatuANHC0yms97WSlhTVAU0=

ajv@6.12.6, ajv@^6.10.0, ajv@^6.12.2, ajv@^6.12.3, ajv@^6.12.4, ajv@^6.12.5:
  version "6.12.6"
  resolved "https://registry.nlark.com/ajv/download/ajv-6.12.6.tgz?cache=0&sync_timestamp=1626380134544&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fajv%2Fdownload%2Fajv-6.12.6.tgz"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.0.1:
  version "8.6.2"
  resolved "https://registry.nlark.com/ajv/download/ajv-8.6.2.tgz?cache=0&sync_timestamp=1626380134544&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fajv%2Fdownload%2Fajv-8.6.2.tgz"
  integrity sha1-L7ReDl/LwIEzJsHD2lNdGIG7BXE=
  dependencies:
    fast-deep-equal "^3.1.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.2.2"

ansi-colors@4.1.1, ansi-colors@^4.1.1:
  version "4.1.1"
  resolved "https://registry.nlark.com/ansi-colors/download/ansi-colors-4.1.1.tgz"
  integrity sha1-y7muJWv3UK8eqzRPIpqif+lLo0g=

ansi-escapes@^4.2.1:
  version "4.3.2"
  resolved "https://registry.nlark.com/ansi-escapes/download/ansi-escapes-4.3.2.tgz"
  integrity sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=
  dependencies:
    type-fest "^0.21.3"

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "https://registry.nlark.com/ansi-regex/download/ansi-regex-2.1.1.tgz"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/ansi-regex/download/ansi-regex-3.0.0.tgz"
  integrity sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=

ansi-regex@^5.0.0:
  version "5.0.0"
  resolved "https://registry.nlark.com/ansi-regex/download/ansi-regex-5.0.0.tgz"
  integrity sha1-OIU59VF5vzkznIGvMKZU1p+Hy3U=

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "https://registry.nlark.com/ansi-styles/download/ansi-styles-2.2.1.tgz"
  integrity sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://registry.nlark.com/ansi-styles/download/ansi-styles-3.2.1.tgz"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://registry.nlark.com/ansi-styles/download/ansi-styles-4.3.0.tgz"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

any-promise@^1.0.0:
  version "1.3.0"
  resolved "https://registry.nlark.com/any-promise/download/any-promise-1.3.0.tgz"
  integrity sha1-q8av7tzqUugJzcA3au0845Y10X8=

anymatch@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/anymatch/download/anymatch-2.0.0.tgz"
  integrity sha1-vLJLTzeTTZqnrBe0ra+J58du8us=
  dependencies:
    micromatch "^3.1.4"
    normalize-path "^2.1.1"

anymatch@^3.0.3, anymatch@~3.1.1, anymatch@~3.1.2:
  version "3.1.2"
  resolved "https://registry.nlark.com/anymatch/download/anymatch-3.1.2.tgz"
  integrity sha1-wFV8CWrzLxBhmPT04qODU343hxY=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

app-root-path@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/app-root-path/download/app-root-path-3.0.0.tgz"
  integrity sha1-IQtvQ4cyJ+GKS4EKAyKDMRVV1a0=

append-field@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/append-field/download/append-field-1.0.0.tgz?cache=0&sync_timestamp=1627133758857&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fappend-field%2Fdownload%2Fappend-field-1.0.0.tgz"
  integrity sha1-HjRA6RXwsSA9I3SOeO3XubW0PlY=

aproba@^1.0.3:
  version "1.2.0"
  resolved "https://registry.nlark.com/aproba/download/aproba-1.2.0.tgz"
  integrity sha1-aALmJk79GMeQobDVF/DyYnvyyUo=

are-we-there-yet@~1.1.2:
  version "1.1.5"
  resolved "https://registry.npm.taobao.org/are-we-there-yet/download/are-we-there-yet-1.1.5.tgz"
  integrity sha1-SzXClE8GKov82mZBB2A1D+nd/CE=
  dependencies:
    delegates "^1.0.0"
    readable-stream "^2.0.6"

arg@^4.1.0:
  version "4.1.3"
  resolved "https://registry.npm.taobao.org/arg/download/arg-4.1.3.tgz?cache=0&sync_timestamp=1605575054403&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Farg%2Fdownload%2Farg-4.1.3.tgz"
  integrity sha1-Jp/HrVuOQstjyJbVZmAXJhwUQIk=

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.npm.taobao.org/argparse/download/argparse-1.0.10.tgz?cache=0&sync_timestamp=1598649397806&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fargparse%2Fdownload%2Fargparse-1.0.10.tgz"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/argparse/download/argparse-2.0.1.tgz?cache=0&sync_timestamp=1598649397806&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fargparse%2Fdownload%2Fargparse-2.0.1.tgz"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/arr-diff/download/arr-diff-4.0.0.tgz"
  integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=

arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/arr-flatten/download/arr-flatten-1.1.0.tgz"
  integrity sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=

arr-union@^3.1.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/arr-union/download/arr-union-3.1.0.tgz"
  integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/array-flatten/download/array-flatten-1.1.1.tgz"
  integrity sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/array-union/download/array-union-2.1.0.tgz"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

array-unique@^0.3.2:
  version "0.3.2"
  resolved "https://registry.npm.taobao.org/array-unique/download/array-unique-0.3.2.tgz"
  integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=

arraybuffer.slice@~0.0.7:
  version "0.0.7"
  resolved "https://registry.npm.taobao.org/arraybuffer.slice/download/arraybuffer.slice-0.0.7.tgz"
  integrity sha1-O7xCdd1YTMGxCAm4nU6LY6aednU=

asn1@^0.2.4, asn1@~0.2.3:
  version "0.2.4"
  resolved "https://registry.nlark.com/asn1/download/asn1-0.2.4.tgz"
  integrity sha1-jSR136tVO7M+d7VOWeiAu4ziMTY=
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/assert-plus/download/assert-plus-1.0.0.tgz"
  integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/assign-symbols/download/assign-symbols-1.0.0.tgz"
  integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=

astral-regex@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/astral-regex/download/astral-regex-2.0.0.tgz"
  integrity sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=

async@3.2.0:
  version "3.2.0"
  resolved "https://registry.nlark.com/async/download/async-3.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fasync%2Fdownload%2Fasync-3.2.0.tgz"
  integrity sha1-s6JoXF67ZB094C0WEALGD8n4VyA=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.nlark.com/asynckit/download/asynckit-0.4.0.tgz"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

at-least-node@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/at-least-node/download/at-least-node-1.0.0.tgz"
  integrity sha1-YCzUtG6EStTv/JKoARo8RuAjjcI=

atob@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npm.taobao.org/atob/download/atob-2.1.2.tgz"
  integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "https://registry.npm.taobao.org/aws-sign2/download/aws-sign2-0.7.0.tgz"
  integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=

aws4@^1.8.0:
  version "1.11.0"
  resolved "https://registry.npm.taobao.org/aws4/download/aws4-1.11.0.tgz?cache=0&sync_timestamp=1604101230105&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Faws4%2Fdownload%2Faws4-1.11.0.tgz"
  integrity sha1-1h9G2DslGSUOJ4Ta9bCUeai0HFk=

axios@0.21.1:
  version "0.21.1"
  resolved "https://registry.npm.taobao.org/axios/download/axios-0.21.1.tgz?cache=0&sync_timestamp=1608609188013&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Faxios%2Fdownload%2Faxios-0.21.1.tgz"
  integrity sha1-IlY0gZYvTWvemnbVFu8OXTwJsrg=
  dependencies:
    follow-redirects "^1.10.0"

babel-jest@^26.6.3:
  version "26.6.3"
  resolved "https://registry.nlark.com/babel-jest/download/babel-jest-26.6.3.tgz?cache=0&sync_timestamp=1624900211926&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbabel-jest%2Fdownload%2Fbabel-jest-26.6.3.tgz"
  integrity sha1-2H0lywA3V3oMifguV1XF0pPAEFY=
  dependencies:
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/babel__core" "^7.1.7"
    babel-plugin-istanbul "^6.0.0"
    babel-preset-jest "^26.6.2"
    chalk "^4.0.0"
    graceful-fs "^4.2.4"
    slash "^3.0.0"

babel-plugin-istanbul@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npm.taobao.org/babel-plugin-istanbul/download/babel-plugin-istanbul-6.0.0.tgz"
  integrity sha1-4VnM3Jr5XgtXDHW0Vzt8NNZx12U=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-instrument "^4.0.0"
    test-exclude "^6.0.0"

babel-plugin-jest-hoist@^26.6.2:
  version "26.6.2"
  resolved "https://registry.nlark.com/babel-plugin-jest-hoist/download/babel-plugin-jest-hoist-26.6.2.tgz?cache=0&sync_timestamp=1624900204559&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbabel-plugin-jest-hoist%2Fdownload%2Fbabel-plugin-jest-hoist-26.6.2.tgz"
  integrity sha1-gYW9AwNI0lTG192XQ1Xmoosh5i0=
  dependencies:
    "@babel/template" "^7.3.3"
    "@babel/types" "^7.3.3"
    "@types/babel__core" "^7.0.0"
    "@types/babel__traverse" "^7.0.6"

babel-preset-current-node-syntax@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/babel-preset-current-node-syntax/download/babel-preset-current-node-syntax-1.0.1.tgz?cache=0&sync_timestamp=1608036018794&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbabel-preset-current-node-syntax%2Fdownload%2Fbabel-preset-current-node-syntax-1.0.1.tgz"
  integrity sha1-tDmSObibKgEfndvj5PQB/EDP9zs=
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.8.3"
    "@babel/plugin-syntax-import-meta" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.8.3"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.8.3"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-top-level-await" "^7.8.3"

babel-preset-jest@^26.6.2:
  version "26.6.2"
  resolved "https://registry.nlark.com/babel-preset-jest/download/babel-preset-jest-26.6.2.tgz?cache=0&sync_timestamp=1624900204237&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbabel-preset-jest%2Fdownload%2Fbabel-preset-jest-26.6.2.tgz"
  integrity sha1-dHhysRcd8DIlJCZYaIHWLTF5j+4=
  dependencies:
    babel-plugin-jest-hoist "^26.6.2"
    babel-preset-current-node-syntax "^1.0.0"

backo2@1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/backo2/download/backo2-1.0.2.tgz"
  integrity sha1-MasayLEpNjRj41s+u2n038+6eUc=

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.nlark.com/balanced-match/download/balanced-match-1.0.2.tgz"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

base64-arraybuffer@0.1.4:
  version "0.1.4"
  resolved "https://registry.npm.taobao.org/base64-arraybuffer/download/base64-arraybuffer-0.1.4.tgz"
  integrity sha1-mBjHngWbE1X5fgQooBfIOOkLqBI=

base64-js@^1.3.1:
  version "1.5.1"
  resolved "https://registry.npm.taobao.org/base64-js/download/base64-js-1.5.1.tgz?cache=0&sync_timestamp=1605123700994&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbase64-js%2Fdownload%2Fbase64-js-1.5.1.tgz"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

base64id@2.0.0, base64id@~2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/base64id/download/base64id-2.0.0.tgz"
  integrity sha1-J3Csa8R9MSr5eov5pjQ0LgzSXLY=

base@^0.11.1:
  version "0.11.2"
  resolved "https://registry.nlark.com/base/download/base-0.11.2.tgz"
  integrity sha1-e95c7RRbbVUakNuH+DxVi060io8=
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

bcrypt-pbkdf@^1.0.0, bcrypt-pbkdf@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz"
  integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
  dependencies:
    tweetnacl "^0.14.3"

bcryptjs@^2.4.3:
  version "2.4.3"
  resolved "https://registry.npmmirror.com/bcryptjs/-/bcryptjs-2.4.3.tgz"
  integrity sha512-V/Hy/X9Vt7f3BbPJEi8BdVFMByHi+jNXrYkW3huaybV/kQ0KJg0Y6PkEMbn+zeT+i+SiKZ/HMqJGIIt4LZDqNQ==

beautify-qrcode@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/beautify-qrcode/-/beautify-qrcode-1.0.3.tgz"
  integrity sha512-DzQKFHkTXrNuD4dMq1leQPFmR/A3tBZs5rdVF7j4DCfwrZOslAHmXEYtrLTelpQDMJjYzqjGvPZXCMdjJEBC1g==
  dependencies:
    yup "^0.29.1"

big.js@^5.2.2:
  version "5.2.2"
  resolved "https://registry.nlark.com/big.js/download/big.js-5.2.2.tgz"
  integrity sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=

binary-extensions@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/binary-extensions/download/binary-extensions-2.2.0.tgz?cache=0&sync_timestamp=1610299268308&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbinary-extensions%2Fdownload%2Fbinary-extensions-2.2.0.tgz"
  integrity sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0=

bl@^2.2.1:
  version "2.2.1"
  resolved "https://registry.nlark.com/bl/download/bl-2.2.1.tgz"
  integrity sha1-jBGntzBlXF1WiYzchxIk9A/ZAdU=
  dependencies:
    readable-stream "^2.3.5"
    safe-buffer "^5.1.1"

bl@^4.0.3, bl@^4.1.0:
  version "4.1.0"
  resolved "https://registry.nlark.com/bl/download/bl-4.1.0.tgz"
  integrity sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo=
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

blob@0.0.5:
  version "0.0.5"
  resolved "https://registry.npm.taobao.org/blob/download/blob-0.0.5.tgz"
  integrity sha1-1oDu7yX4zZGtUz9bAe7UjmTK9oM=

block-stream@*:
  version "0.0.9"
  resolved "https://registry.npm.taobao.org/block-stream/download/block-stream-0.0.9.tgz"
  integrity sha1-E+v+d4oDIFz+A3UUgeu0szAMEmo=
  dependencies:
    inherits "~2.0.0"

bluebird@3.5.1:
  version "3.5.1"
  resolved "https://registry.nlark.com/bluebird/download/bluebird-3.5.1.tgz?cache=0&sync_timestamp=1618847007562&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbluebird%2Fdownload%2Fbluebird-3.5.1.tgz"
  integrity sha1-2VUfnemPH82h5oPRfukaBgLuLrk=

body-parser@1.19.0:
  version "1.19.0"
  resolved "https://registry.nlark.com/body-parser/download/body-parser-1.19.0.tgz"
  integrity sha1-lrJwnlfJxOCab9Zqj9l5hE9p8Io=
  dependencies:
    bytes "3.1.0"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "~1.1.2"
    http-errors "1.7.2"
    iconv-lite "0.4.24"
    on-finished "~2.3.0"
    qs "6.7.0"
    raw-body "2.4.0"
    type-is "~1.6.17"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.nlark.com/brace-expansion/download/brace-expansion-1.1.11.tgz"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^2.3.1:
  version "2.3.2"
  resolved "https://registry.npm.taobao.org/braces/download/braces-2.3.2.tgz"
  integrity sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@^3.0.1, braces@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npm.taobao.org/braces/download/braces-3.0.2.tgz"
  integrity sha1-NFThpGLujVmeI23zNs2epPiv4Qc=
  dependencies:
    fill-range "^7.0.1"

browser-process-hrtime@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/browser-process-hrtime/download/browser-process-hrtime-1.0.0.tgz"
  integrity sha1-PJtLfXgsgSHlbxAQbYTA0P/JRiY=

browserslist@^4.14.5, browserslist@^4.16.6:
  version "4.16.7"
  resolved "https://registry.nlark.com/browserslist/download/browserslist-4.16.7.tgz?cache=0&sync_timestamp=1627982452691&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbrowserslist%2Fdownload%2Fbrowserslist-4.16.7.tgz"
  integrity sha1-EIsNHvM8SvG1h8VPOQ5wQReOQzU=
  dependencies:
    caniuse-lite "^1.0.30001248"
    colorette "^1.2.2"
    electron-to-chromium "^1.3.793"
    escalade "^3.1.1"
    node-releases "^1.1.73"

bs-logger@0.x:
  version "0.2.6"
  resolved "https://registry.npm.taobao.org/bs-logger/download/bs-logger-0.2.6.tgz"
  integrity sha1-6302UwenLPl0zGzadraDVK0za9g=
  dependencies:
    fast-json-stable-stringify "2.x"

bser@2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/bser/download/bser-2.1.1.tgz"
  integrity sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=
  dependencies:
    node-int64 "^0.4.0"

bson@^1.1.4:
  version "1.1.6"
  resolved "https://registry.nlark.com/bson/download/bson-1.1.6.tgz?cache=0&sync_timestamp=1625593618256&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbson%2Fdownload%2Fbson-1.1.6.tgz"
  integrity sha1-+4Gb6aYM1nfghTruTKcSp4XWYYo=

buffer-equal-constant-time@1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/buffer-equal-constant-time/download/buffer-equal-constant-time-1.0.1.tgz"
  integrity sha1-+OcRMvf/5uAaXJaXpMbz5I1cyBk=

buffer-from@1.x, buffer-from@^1.0.0:
  version "1.1.2"
  resolved "https://registry.nlark.com/buffer-from/download/buffer-from-1.1.2.tgz"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

buffer@^5.5.0:
  version "5.7.1"
  resolved "https://registry.npm.taobao.org/buffer/download/buffer-5.7.1.tgz?cache=0&sync_timestamp=1606098073225&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbuffer%2Fdownload%2Fbuffer-5.7.1.tgz"
  integrity sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

buffer@^6.0.3:
  version "6.0.3"
  resolved "https://registry.npm.taobao.org/buffer/download/buffer-6.0.3.tgz?cache=0&sync_timestamp=1606098073225&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fbuffer%2Fdownload%2Fbuffer-6.0.3.tgz"
  integrity sha1-Ks5XhFnMj74qcKqo9S7mO2p0xsY=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.2.1"

busboy@^0.2.11:
  version "0.2.14"
  resolved "https://registry.npm.taobao.org/busboy/download/busboy-0.2.14.tgz"
  integrity sha1-bCpiLvz0fFe7vh4qnDetNseSVFM=
  dependencies:
    dicer "0.2.5"
    readable-stream "1.1.x"

bytes@3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/bytes/download/bytes-3.1.0.tgz"
  integrity sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY=

cache-base@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/cache-base/download/cache-base-1.0.1.tgz"
  integrity sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

cache-manager@^3.4.4:
  version "3.4.4"
  resolved "https://registry.nlark.com/cache-manager/download/cache-manager-3.4.4.tgz"
  integrity sha1-xpgUdj0/MDE5WuDTqakpapFgIiY=
  dependencies:
    async "3.2.0"
    lodash "^4.17.21"
    lru-cache "6.0.0"

call-bind@^1.0.0:
  version "1.0.2"
  resolved "https://registry.nlark.com/call-bind/download/call-bind-1.0.2.tgz"
  integrity sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/callsites/download/callsites-3.1.0.tgz"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camelcase@^5.0.0, camelcase@^5.3.1:
  version "5.3.1"
  resolved "https://registry.nlark.com/camelcase/download/camelcase-5.3.1.tgz"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

camelcase@^6.0.0:
  version "6.2.0"
  resolved "https://registry.nlark.com/camelcase/download/camelcase-6.2.0.tgz"
  integrity sha1-kkr4gcnVJaydh/QNlk5c6pgqGAk=

caniuse-lite@^1.0.30001248:
  version "1.0.30001249"
  resolved "https://registry.nlark.com/caniuse-lite/download/caniuse-lite-1.0.30001249.tgz?cache=0&sync_timestamp=1628131934955&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcaniuse-lite%2Fdownload%2Fcaniuse-lite-1.0.30001249.tgz"
  integrity sha1-kKMwBX+P91v+l6lNBH1eFPq7Lug=

capture-exit@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/capture-exit/download/capture-exit-2.0.0.tgz"
  integrity sha1-+5U7+uvreB9iiYI52rtCbQilCaQ=
  dependencies:
    rsvp "^4.8.4"

caseless@~0.12.0:
  version "0.12.0"
  resolved "https://registry.nlark.com/caseless/download/caseless-0.12.0.tgz"
  integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=

cfb@~1.2.1:
  version "1.2.2"
  resolved "https://registry.npmmirror.com/cfb/-/cfb-1.2.2.tgz"
  integrity sha512-KfdUZsSOw19/ObEWasvBP/Ac4reZvAGauZhs6S/gqNhXhI7cKwvlH7ulj+dOEYnca4bm4SGo8C1bTAQvnTjgQA==
  dependencies:
    adler-32 "~1.3.0"
    crc-32 "~1.2.0"

chalk@3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/chalk/download/chalk-3.0.0.tgz"
  integrity sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^1.1.1:
  version "1.1.3"
  resolved "https://registry.nlark.com/chalk/download/chalk-1.1.3.tgz"
  integrity sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^2.0.0:
  version "2.4.2"
  resolved "https://registry.nlark.com/chalk/download/chalk-2.4.2.tgz"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0, chalk@^4.1.0:
  version "4.1.2"
  resolved "https://registry.nlark.com/chalk/download/chalk-4.1.2.tgz"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

char-regex@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/char-regex/download/char-regex-1.0.2.tgz?cache=0&sync_timestamp=1622809071355&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fchar-regex%2Fdownload%2Fchar-regex-1.0.2.tgz"
  integrity sha1-10Q1giYhf5ge1Y9Hmx1rzClUXc8=

chardet@^0.7.0:
  version "0.7.0"
  resolved "https://registry.npm.taobao.org/chardet/download/chardet-0.7.0.tgz?cache=0&sync_timestamp=1601032467034&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fchardet%2Fdownload%2Fchardet-0.7.0.tgz"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

charenc@0.0.2:
  version "0.0.2"
  resolved "https://registry.npmmirror.com/charenc/-/charenc-0.0.2.tgz"
  integrity sha512-yrLQ/yVUFXkzg7EDQsPieE/53+0RlaWTs+wBrvW36cyilJ2SaDWfl4Yj7MtLTXleV9uEKefbAGUPv2/iWSooRA==

chokidar@3.5.1:
  version "3.5.1"
  resolved "https://registry.nlark.com/chokidar/download/chokidar-3.5.1.tgz"
  integrity sha1-7pznu+vSt59J8wR5nVRo4x4U5oo=
  dependencies:
    anymatch "~3.1.1"
    braces "~3.0.2"
    glob-parent "~5.1.0"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.5.0"
  optionalDependencies:
    fsevents "~2.3.1"

chokidar@^3.4.2:
  version "3.5.2"
  resolved "https://registry.nlark.com/chokidar/download/chokidar-3.5.2.tgz"
  integrity sha1-26OXb8rbAW9m/TZQIdkWANAcHnU=
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chownr@^1.1.1:
  version "1.1.4"
  resolved "https://registry.nlark.com/chownr/download/chownr-1.1.4.tgz"
  integrity sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs=

chrome-trace-event@^1.0.2:
  version "1.0.3"
  resolved "https://registry.nlark.com/chrome-trace-event/download/chrome-trace-event-1.0.3.tgz"
  integrity sha1-EBXs7UdB4V0GZkqVfbv1DQQeJqw=

ci-info@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/ci-info/download/ci-info-2.0.0.tgz?cache=0&sync_timestamp=1622039942508&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fci-info%2Fdownload%2Fci-info-2.0.0.tgz"
  integrity sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y=

cjs-module-lexer@^0.6.0:
  version "0.6.0"
  resolved "https://registry.nlark.com/cjs-module-lexer/download/cjs-module-lexer-0.6.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcjs-module-lexer%2Fdownload%2Fcjs-module-lexer-0.6.0.tgz"
  integrity sha1-QYb8yg6uF1lwruhwuf4tbPjVZV8=

class-utils@^0.3.5:
  version "0.3.6"
  resolved "https://registry.npm.taobao.org/class-utils/download/class-utils-0.3.6.tgz"
  integrity sha1-+TNprouafOAv1B+q0MqDAzGQxGM=
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/cli-cursor/download/cli-cursor-3.1.0.tgz"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-highlight@^2.1.11:
  version "2.1.11"
  resolved "https://registry.nlark.com/cli-highlight/download/cli-highlight-2.1.11.tgz"
  integrity sha1-SXNvpFLwqvT65YDjCssmgo0twb8=
  dependencies:
    chalk "^4.0.0"
    highlight.js "^10.7.1"
    mz "^2.4.0"
    parse5 "^5.1.1"
    parse5-htmlparser2-tree-adapter "^6.0.0"
    yargs "^16.0.0"

cli-spinners@^2.5.0:
  version "2.6.0"
  resolved "https://registry.npm.taobao.org/cli-spinners/download/cli-spinners-2.6.0.tgz?cache=0&sync_timestamp=1616091539101&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcli-spinners%2Fdownload%2Fcli-spinners-2.6.0.tgz"
  integrity sha1-NsfcmPtqmna9YjjsP3fiQlYn6Tk=

cli-table3@0.5.1:
  version "0.5.1"
  resolved "https://registry.npm.taobao.org/cli-table3/download/cli-table3-0.5.1.tgz"
  integrity sha1-AlI3LZTfxA29jfBgBfSPMfZW8gI=
  dependencies:
    object-assign "^4.1.0"
    string-width "^2.1.1"
  optionalDependencies:
    colors "^1.1.2"

cli-width@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/cli-width/download/cli-width-3.0.0.tgz"
  integrity sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=

cliui@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npm.taobao.org/cliui/download/cliui-6.0.0.tgz"
  integrity sha1-UR1wLAxOQcoVbX0OlgIfI+EyJbE=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^6.2.0"

cliui@^7.0.2:
  version "7.0.4"
  resolved "https://registry.npm.taobao.org/cliui/download/cliui-7.0.4.tgz"
  integrity sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^7.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "https://registry.nlark.com/clone/download/clone-1.0.4.tgz"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

co@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npm.taobao.org/co/download/co-4.6.0.tgz"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/code-point-at/download/code-point-at-1.1.0.tgz"
  integrity sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=

codepage@~1.15.0:
  version "1.15.0"
  resolved "https://registry.npmmirror.com/codepage/-/codepage-1.15.0.tgz"
  integrity sha512-3g6NUTPd/YtuuGrhMnOMRjFc+LJw/bnMp3+0r/Wcz3IXUuCosKRJvMphm5+Q+bvTVGcJJuRvVLuYba+WojaFaA==

collect-v8-coverage@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/collect-v8-coverage/download/collect-v8-coverage-1.0.1.tgz"
  integrity sha1-zCyOlPwYu9/+ZNZTRXDIpnOyf1k=

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/collection-visit/download/collection-visit-1.0.0.tgz"
  integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.9.0:
  version "1.9.3"
  resolved "https://registry.nlark.com/color-convert/download/color-convert-1.9.3.tgz"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/color-convert/download/color-convert-2.0.1.tgz"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "https://registry.nlark.com/color-name/download/color-name-1.1.3.tgz"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.nlark.com/color-name/download/color-name-1.1.4.tgz"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

colorette@^1.2.2:
  version "1.2.2"
  resolved "https://registry.nlark.com/colorette/download/colorette-1.2.2.tgz"
  integrity sha1-y8x51emcrqLb8Q6zom/Ys+as+pQ=

colors@^1.1.2:
  version "1.4.0"
  resolved "https://registry.nlark.com/colors/download/colors-1.4.0.tgz"
  integrity sha1-xQSRR51MG9rtLJztMs98fcI2D3g=

combined-stream@^1.0.6, combined-stream@^1.0.8, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "https://registry.nlark.com/combined-stream/download/combined-stream-1.0.8.tgz"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@*:
  version "8.1.0"
  resolved "https://registry.nlark.com/commander/download/commander-8.1.0.tgz?cache=0&sync_timestamp=1627359190297&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcommander%2Fdownload%2Fcommander-8.1.0.tgz"
  integrity sha1-2zbj5m7fJP9ZHWOYYsarLFJmQ2I=

commander@4.1.1:
  version "4.1.1"
  resolved "https://registry.nlark.com/commander/download/commander-4.1.1.tgz?cache=0&sync_timestamp=1627359190297&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcommander%2Fdownload%2Fcommander-4.1.1.tgz"
  integrity sha1-n9YCvZNilOnp70aj9NaWQESxgGg=

commander@^2.20.0:
  version "2.20.3"
  resolved "https://registry.nlark.com/commander/download/commander-2.20.3.tgz?cache=0&sync_timestamp=1627359190297&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcommander%2Fdownload%2Fcommander-2.20.3.tgz"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

component-bind@1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/component-bind/download/component-bind-1.0.0.tgz"
  integrity sha1-AMYIq33Nk4l8AAllGx06jh5zu9E=

component-emitter@1.2.1:
  version "1.2.1"
  resolved "https://registry.npm.taobao.org/component-emitter/download/component-emitter-1.2.1.tgz"
  integrity sha1-E3kY1teCg/ffemt8WmPhQOaUJeY=

component-emitter@^1.2.1, component-emitter@^1.3.0, component-emitter@~1.3.0:
  version "1.3.0"
  resolved "https://registry.npm.taobao.org/component-emitter/download/component-emitter-1.3.0.tgz"
  integrity sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=

component-inherit@0.0.3:
  version "0.0.3"
  resolved "https://registry.npm.taobao.org/component-inherit/download/component-inherit-0.0.3.tgz"
  integrity sha1-ZF/ErfWLcrZJ1crmUTVhnbJv8UM=

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.npm.taobao.org/concat-map/download/concat-map-0.0.1.tgz"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^1.5.2:
  version "1.6.2"
  resolved "https://registry.npm.taobao.org/concat-stream/download/concat-stream-1.6.2.tgz"
  integrity sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

consola@^2.15.0:
  version "2.15.3"
  resolved "https://registry.npm.taobao.org/consola/download/consola-2.15.3.tgz"
  integrity sha1-LhH5jWpL5x/3LgvfB70j4Sy2FVA=

console-control-strings@^1.0.0, console-control-strings@~1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/console-control-strings/download/console-control-strings-1.1.0.tgz"
  integrity sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=

content-disposition@0.5.3:
  version "0.5.3"
  resolved "https://registry.nlark.com/content-disposition/download/content-disposition-0.5.3.tgz"
  integrity sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70=
  dependencies:
    safe-buffer "5.1.2"

content-type@~1.0.4:
  version "1.0.4"
  resolved "https://registry.nlark.com/content-type/download/content-type-1.0.4.tgz"
  integrity sha1-4TjMdeBAxyexlm/l5fjJruJW/js=

convert-source-map@^1.4.0, convert-source-map@^1.6.0, convert-source-map@^1.7.0:
  version "1.8.0"
  resolved "https://registry.nlark.com/convert-source-map/download/convert-source-map-1.8.0.tgz?cache=0&sync_timestamp=1624045451791&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fconvert-source-map%2Fdownload%2Fconvert-source-map-1.8.0.tgz"
  integrity sha1-8zc8MtIbTXgN2ABFFGhPt5HKQ2k=
  dependencies:
    safe-buffer "~5.1.1"

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://registry.nlark.com/cookie-signature/download/cookie-signature-1.0.6.tgz"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@0.4.0:
  version "0.4.0"
  resolved "https://registry.nlark.com/cookie/download/cookie-0.4.0.tgz"
  integrity sha1-vrQ35wIrO21JAZ0IhmUwPr6cFLo=

cookie@~0.4.1:
  version "0.4.1"
  resolved "https://registry.nlark.com/cookie/download/cookie-0.4.1.tgz"
  integrity sha1-r9cT/ibr0hupXOth+agRblClN9E=

cookiejar@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npm.taobao.org/cookiejar/download/cookiejar-2.1.2.tgz"
  integrity sha1-3YojVTB1L5iPmghE8/xYnjERElw=

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "https://registry.npm.taobao.org/copy-descriptor/download/copy-descriptor-0.1.1.tgz"
  integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=

core-util-is@1.0.2, core-util-is@~1.0.0:
  version "1.0.2"
  resolved "https://registry.nlark.com/core-util-is/download/core-util-is-1.0.2.tgz"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

cors@2.8.5, cors@~2.8.5:
  version "2.8.5"
  resolved "https://registry.nlark.com/cors/download/cors-2.8.5.tgz"
  integrity sha1-6sEdpRWS3Ya58G9uesKTs9+HXSk=
  dependencies:
    object-assign "^4"
    vary "^1"

cosmiconfig@^6.0.0:
  version "6.0.0"
  resolved "https://registry.nlark.com/cosmiconfig/download/cosmiconfig-6.0.0.tgz"
  integrity sha1-2k/uhTxS9rHmk19BwaL8UL1KmYI=
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.1.0"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.7.2"

cpu-features@0.0.2:
  version "0.0.2"
  resolved "https://registry.npmmirror.com/cpu-features/-/cpu-features-0.0.2.tgz#9f636156f1155fd04bdbaa028bb3c2fbef3cea7a"
  integrity sha512-/2yieBqvMcRj8McNzkycjW2v3OIUOibBfd2dLEJ0nWts8NobAxwiyw9phVNS6oDL8x8tz9F7uNVFEVpJncQpeA==
  dependencies:
    nan "^2.14.1"

crc-32@~1.2.0, crc-32@~1.2.1:
  version "1.2.2"
  resolved "https://registry.npmmirror.com/crc-32/-/crc-32-1.2.2.tgz"
  integrity sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ==

create-require@^1.1.0:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/create-require/download/create-require-1.1.1.tgz?cache=0&sync_timestamp=1606399137064&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcreate-require%2Fdownload%2Fcreate-require-1.1.1.tgz"
  integrity sha1-wdfo8eX2z8n/ZfnNNS03NIdWwzM=

cross-spawn@^6.0.0:
  version "6.0.5"
  resolved "https://registry.nlark.com/cross-spawn/download/cross-spawn-6.0.5.tgz"
  integrity sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^7.0.0, cross-spawn@^7.0.2:
  version "7.0.3"
  resolved "https://registry.nlark.com/cross-spawn/download/cross-spawn-7.0.3.tgz"
  integrity sha1-9zqFudXUHQRVUcF34ogtSshXKKY=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypt@0.0.2:
  version "0.0.2"
  resolved "https://registry.npmmirror.com/crypt/-/crypt-0.0.2.tgz"
  integrity sha512-mCxBlsHFYh9C+HVpiEacem8FEBnMXgU9gy4zmNC+SXAZNB/1idgp/aulFJ4FgCi7GPEVbfyng092GqL2k2rmow==

cssom@^0.4.4:
  version "0.4.4"
  resolved "https://registry.nlark.com/cssom/download/cssom-0.4.4.tgz"
  integrity sha1-WmbPk9LQtmHYC/akT7ZfXC5OChA=

cssom@~0.3.6:
  version "0.3.8"
  resolved "https://registry.nlark.com/cssom/download/cssom-0.3.8.tgz"
  integrity sha1-nxJ29bK0Y/IRTT8sdSUK+MGjb0o=

cssstyle@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npm.taobao.org/cssstyle/download/cssstyle-2.3.0.tgz"
  integrity sha1-/2ZaDdvcMYZLCWR/NBY0Q9kLCFI=
  dependencies:
    cssom "~0.3.6"

dashdash@^1.12.0:
  version "1.14.1"
  resolved "https://registry.nlark.com/dashdash/download/dashdash-1.14.1.tgz"
  integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
  dependencies:
    assert-plus "^1.0.0"

data-urls@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/data-urls/download/data-urls-2.0.0.tgz?cache=0&sync_timestamp=1626722876103&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdata-urls%2Fdownload%2Fdata-urls-2.0.0.tgz"
  integrity sha1-FWSFpyljqXD11YIar2Qr7yvy25s=
  dependencies:
    abab "^2.0.3"
    whatwg-mimetype "^2.3.0"
    whatwg-url "^8.0.0"

debug@2.6.9, debug@^2.2.0, debug@^2.3.3:
  version "2.6.9"
  resolved "https://registry.nlark.com/debug/download/debug-2.6.9.tgz?cache=0&sync_timestamp=1625374675284&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@3.1.0, debug@~3.1.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/debug/download/debug-3.1.0.tgz?cache=0&sync_timestamp=1625374675284&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdebug%2Fdownload%2Fdebug-3.1.0.tgz"
  integrity sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=
  dependencies:
    ms "2.0.0"

debug@4, debug@^4.0.1, debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@~4.3.1:
  version "4.3.2"
  resolved "https://registry.nlark.com/debug/download/debug-4.3.2.tgz?cache=0&sync_timestamp=1625374675284&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdebug%2Fdownload%2Fdebug-4.3.2.tgz"
  integrity sha1-8KScGKyHeeMdSgxgKd+3aHPHQos=
  dependencies:
    ms "2.1.2"

debug@^3.2.6:
  version "3.2.7"
  resolved "https://registry.nlark.com/debug/download/debug-3.2.7.tgz?cache=0&sync_timestamp=1625374675284&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdebug%2Fdownload%2Fdebug-3.2.7.tgz"
  integrity sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=
  dependencies:
    ms "^2.1.1"

debug@~4.1.0:
  version "4.1.1"
  resolved "https://registry.nlark.com/debug/download/debug-4.1.1.tgz?cache=0&sync_timestamp=1625374675284&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdebug%2Fdownload%2Fdebug-4.1.1.tgz"
  integrity sha1-O3ImAlUQnGtYnO4FDx1RYTlmR5E=
  dependencies:
    ms "^2.1.1"

decamelize@^1.2.0:
  version "1.2.0"
  resolved "https://registry.nlark.com/decamelize/download/decamelize-1.2.0.tgz"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decimal.js@^10.2.1:
  version "10.3.1"
  resolved "https://registry.nlark.com/decimal.js/download/decimal.js-10.3.1.tgz"
  integrity sha1-2MOkRKnGd0umDKatcmHDqU/V54M=

decode-uri-component@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.taobao.org/decode-uri-component/download/decode-uri-component-0.2.0.tgz"
  integrity sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=

deep-extend@^0.6.0:
  version "0.6.0"
  resolved "https://registry.npm.taobao.org/deep-extend/download/deep-extend-0.6.0.tgz"
  integrity sha1-xPp8lUBKF6nD6Mp+FTcxK3NjMKw=

deep-is@^0.1.3, deep-is@~0.1.3:
  version "0.1.3"
  resolved "https://registry.npm.taobao.org/deep-is/download/deep-is-0.1.3.tgz"
  integrity sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ=

deepmerge@^4.2.2:
  version "4.2.2"
  resolved "https://registry.nlark.com/deepmerge/download/deepmerge-4.2.2.tgz"
  integrity sha1-RNLqNnm49NT/ujPwPYZfwee/SVU=

defaults@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/defaults/download/defaults-1.0.3.tgz"
  integrity sha1-xlYFHpgX2f8I7YgUd/P+QBnz730=
  dependencies:
    clone "^1.0.2"

define-property@^0.2.5:
  version "0.2.5"
  resolved "https://registry.npm.taobao.org/define-property/download/define-property-0.2.5.tgz"
  integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/define-property/download/define-property-1.0.0.tgz"
  integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npm.taobao.org/define-property/download/define-property-2.0.2.tgz"
  integrity sha1-1Flono1lS6d+AqgX+HENcCyxbp0=
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/delayed-stream/download/delayed-stream-1.0.0.tgz"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

delegates@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/delegates/download/delegates-1.0.0.tgz"
  integrity sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=

denque@^1.4.1:
  version "1.5.0"
  resolved "https://registry.npm.taobao.org/denque/download/denque-1.5.0.tgz"
  integrity sha1-dz3gaG/y2Owv+SkUMWpHtzscc94=

depd@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/depd/download/depd-1.1.2.tgz"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

destroy@~1.0.4:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/destroy/download/destroy-1.0.4.tgz"
  integrity sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=

detect-libc@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/detect-libc/download/detect-libc-1.0.3.tgz"
  integrity sha1-+hN8S9aY7fVc1c0CrFWfkaTEups=

detect-newline@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/detect-newline/download/detect-newline-3.1.0.tgz"
  integrity sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE=

dicer@0.2.5:
  version "0.2.5"
  resolved "https://registry.npm.taobao.org/dicer/download/dicer-0.2.5.tgz"
  integrity sha1-WZbAhrszIYyBLAkL3cCc0S+stw8=
  dependencies:
    readable-stream "1.1.x"
    streamsearch "0.1.2"

diff-sequences@^26.6.2:
  version "26.6.2"
  resolved "https://registry.nlark.com/diff-sequences/download/diff-sequences-26.6.2.tgz"
  integrity sha1-SLqZFX3hkjQS7tQdtrbUqpynwLE=

diff@^4.0.1:
  version "4.0.2"
  resolved "https://registry.nlark.com/diff/download/diff-4.0.2.tgz"
  integrity sha1-YPOuy4nV+uUgwRqhnvwruYKq3n0=

dijkstrajs@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/dijkstrajs/-/dijkstrajs-1.0.2.tgz"
  integrity sha512-QV6PMaHTCNmKSeP6QoXhVTw9snc9VD8MulTT0Bd99Pacp4SS1cjcrYPgBPmibqKVtMJJfqC6XvOXgPMEEPH/fg==

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://registry.nlark.com/dir-glob/download/dir-glob-3.0.1.tgz"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/doctrine/download/doctrine-3.0.0.tgz"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

domexception@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/domexception/download/domexception-2.0.1.tgz"
  integrity sha1-+0Su+6eT4VdLCvau0oAdBXUp8wQ=
  dependencies:
    webidl-conversions "^5.0.0"

dotenv@^8.2.0:
  version "8.6.0"
  resolved "https://registry.nlark.com/dotenv/download/dotenv-8.6.0.tgz?cache=0&sync_timestamp=1621633079842&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdotenv%2Fdownload%2Fdotenv-8.6.0.tgz"
  integrity sha1-Bhr2ZNGff02PxuT/m1hM4jety4s=

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "https://registry.nlark.com/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz"
  integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

ecdsa-sig-formatter@1.0.11:
  version "1.0.11"
  resolved "https://registry.npm.taobao.org/ecdsa-sig-formatter/download/ecdsa-sig-formatter-1.0.11.tgz"
  integrity sha1-rg8PothQRe8UqBfao86azQSJ5b8=
  dependencies:
    safe-buffer "^5.0.1"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/ee-first/download/ee-first-1.1.1.tgz"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

electron-to-chromium@^1.3.793:
  version "1.3.799"
  resolved "https://registry.nlark.com/electron-to-chromium/download/electron-to-chromium-1.3.799.tgz?cache=0&sync_timestamp=1628301859140&other_urls=https%3A%2F%2Fregistry.nlark.com%2Felectron-to-chromium%2Fdownload%2Felectron-to-chromium-1.3.799.tgz"
  integrity sha1-bpkRsl5+zVqh5U3LaPgqPgLQDwk=

emittery@^0.7.1:
  version "0.7.2"
  resolved "https://registry.nlark.com/emittery/download/emittery-0.7.2.tgz?cache=0&sync_timestamp=1622640852431&other_urls=https%3A%2F%2Fregistry.nlark.com%2Femittery%2Fdownload%2Femittery-0.7.2.tgz"
  integrity sha1-JVlZCOE68PVnSrQZOW4vs5TN+oI=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.nlark.com/emoji-regex/download/emoji-regex-8.0.0.tgz"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/emojis-list/download/emojis-list-3.0.0.tgz"
  integrity sha1-VXBmIEatKeLpFucariYKvf9Pang=

encode-utf8@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/encode-utf8/-/encode-utf8-1.0.3.tgz"
  integrity sha512-ucAnuBEhUK4boH2HjVYG5Q2mQyPorvv0u/ocS+zhdw0S8AlHYY+GOFhP1Gio5z4icpP2ivFSvhtFjQi8+T9ppw==

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/encodeurl/download/encodeurl-1.0.2.tgz"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "https://registry.npm.taobao.org/end-of-stream/download/end-of-stream-1.4.4.tgz"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

engine.io-client@~3.5.0:
  version "3.5.2"
  resolved "https://registry.nlark.com/engine.io-client/download/engine.io-client-3.5.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fengine.io-client%2Fdownload%2Fengine.io-client-3.5.2.tgz"
  integrity sha1-DvRzYhKUAE6c7r5zzvCvnjby9fo=
  dependencies:
    component-emitter "~1.3.0"
    component-inherit "0.0.3"
    debug "~3.1.0"
    engine.io-parser "~2.2.0"
    has-cors "1.1.0"
    indexof "0.0.1"
    parseqs "0.0.6"
    parseuri "0.0.6"
    ws "~7.4.2"
    xmlhttprequest-ssl "~1.6.2"
    yeast "0.1.2"

engine.io-parser@~2.2.0:
  version "2.2.1"
  resolved "https://registry.npm.taobao.org/engine.io-parser/download/engine.io-parser-2.2.1.tgz?cache=0&sync_timestamp=1607330710445&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fengine.io-parser%2Fdownload%2Fengine.io-parser-2.2.1.tgz"
  integrity sha1-V85WEdk3DulPmWQbWJ+UyX5PXac=
  dependencies:
    after "0.8.2"
    arraybuffer.slice "~0.0.7"
    base64-arraybuffer "0.1.4"
    blob "0.0.5"
    has-binary2 "~1.0.2"

engine.io-parser@~4.0.0:
  version "4.0.2"
  resolved "https://registry.npm.taobao.org/engine.io-parser/download/engine.io-parser-4.0.2.tgz?cache=0&sync_timestamp=1607330710445&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fengine.io-parser%2Fdownload%2Fengine.io-parser-4.0.2.tgz"
  integrity sha1-5B0LP7Zve/SjZx0gOKFUAk7bUB4=
  dependencies:
    base64-arraybuffer "0.1.4"

engine.io@~3.5.0:
  version "3.5.0"
  resolved "https://registry.nlark.com/engine.io/download/engine.io-3.5.0.tgz?cache=0&sync_timestamp=1621204854469&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fengine.io%2Fdownload%2Fengine.io-3.5.0.tgz"
  integrity sha1-nWuYXIo5sf6HzZHrAU3gVSJZghs=
  dependencies:
    accepts "~1.3.4"
    base64id "2.0.0"
    cookie "~0.4.1"
    debug "~4.1.0"
    engine.io-parser "~2.2.0"
    ws "~7.4.2"

engine.io@~5.1.1:
  version "5.1.1"
  resolved "https://registry.nlark.com/engine.io/download/engine.io-5.1.1.tgz?cache=0&sync_timestamp=1621204854469&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fengine.io%2Fdownload%2Fengine.io-5.1.1.tgz"
  integrity sha1-ofl+Ud3xDL1NuLX/SxZarTdgzdM=
  dependencies:
    accepts "~1.3.4"
    base64id "2.0.0"
    cookie "~0.4.1"
    cors "~2.8.5"
    debug "~4.3.1"
    engine.io-parser "~4.0.0"
    ws "~7.4.2"

enhanced-resolve@^4.0.0:
  version "4.5.0"
  resolved "https://registry.nlark.com/enhanced-resolve/download/enhanced-resolve-4.5.0.tgz?cache=0&sync_timestamp=1620663202047&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fenhanced-resolve%2Fdownload%2Fenhanced-resolve-4.5.0.tgz"
  integrity sha1-Lzz9hNvjtIfxjy2y7x4GSlccpew=
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.5.0"
    tapable "^1.0.0"

enhanced-resolve@^5.7.0:
  version "5.8.2"
  resolved "https://registry.nlark.com/enhanced-resolve/download/enhanced-resolve-5.8.2.tgz?cache=0&sync_timestamp=1620663202047&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fenhanced-resolve%2Fdownload%2Fenhanced-resolve-5.8.2.tgz"
  integrity sha1-Fd3HeTRcu3PpfGEc0AwBwee/TYs=
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

enquirer@^2.3.5:
  version "2.3.6"
  resolved "https://registry.npm.taobao.org/enquirer/download/enquirer-2.3.6.tgz"
  integrity sha1-Kn/l3WNKHkElqXXsmU/1RW3Dc00=
  dependencies:
    ansi-colors "^4.1.1"

errno@^0.1.3:
  version "0.1.8"
  resolved "https://registry.npm.taobao.org/errno/download/errno-0.1.8.tgz"
  integrity sha1-i7Ppx9Rjvkl2/4iPdrSAnrwugR8=
  dependencies:
    prr "~1.0.1"

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://registry.npm.taobao.org/error-ex/download/error-ex-1.3.2.tgz"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

es-module-lexer@^0.4.0:
  version "0.4.1"
  resolved "https://registry.nlark.com/es-module-lexer/download/es-module-lexer-0.4.1.tgz?cache=0&sync_timestamp=1625523919137&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fes-module-lexer%2Fdownload%2Fes-module-lexer-0.4.1.tgz"
  integrity sha1-3ajGoU2PNAok40Mx4Pqwy1BDjg4=

escalade@^3.1.1:
  version "3.1.1"
  resolved "https://registry.nlark.com/escalade/download/escalade-3.1.1.tgz"
  integrity sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/escape-html/download/escape-html-1.0.3.tgz"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npm.taobao.org/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/escape-string-regexp/download/escape-string-regexp-2.0.0.tgz"
  integrity sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

escodegen@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/escodegen/download/escodegen-2.0.0.tgz"
  integrity sha1-XjKxKDPoqo+jXhvwvvqJOASEx90=
  dependencies:
    esprima "^4.0.1"
    estraverse "^5.2.0"
    esutils "^2.0.2"
    optionator "^0.8.1"
  optionalDependencies:
    source-map "~0.6.1"

eslint-config-prettier@^8.1.0:
  version "8.3.0"
  resolved "https://registry.nlark.com/eslint-config-prettier/download/eslint-config-prettier-8.3.0.tgz"
  integrity sha1-90cbILb+ipqSVMxoRFQgKIai3Xo=

eslint-plugin-prettier@^3.3.1:
  version "3.4.0"
  resolved "https://registry.npm.taobao.org/eslint-plugin-prettier/download/eslint-plugin-prettier-3.4.0.tgz"
  integrity sha1-zbrTvx29Kxd+mCVzf+Y7R2oI8Mc=
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "https://registry.nlark.com/eslint-scope/download/eslint-scope-5.1.1.tgz"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-utils@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/eslint-utils/download/eslint-utils-2.1.0.tgz"
  integrity sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc=
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-utils@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/eslint-utils/download/eslint-utils-3.0.0.tgz"
  integrity sha1-iuuvrOc0W7M1WdsKHxOh0tSMNnI=
  dependencies:
    eslint-visitor-keys "^2.0.0"

eslint-visitor-keys@^1.1.0, eslint-visitor-keys@^1.3.0:
  version "1.3.0"
  resolved "https://registry.nlark.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz"
  integrity sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=

eslint-visitor-keys@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/eslint-visitor-keys/download/eslint-visitor-keys-2.1.0.tgz"
  integrity sha1-9lMoJZMFknOSyTjtROsKXJsr0wM=

eslint@^7.22.0:
  version "7.32.0"
  resolved "https://registry.nlark.com/eslint/download/eslint-7.32.0.tgz?cache=0&sync_timestamp=1627685236231&other_urls=https%3A%2F%2Fregistry.nlark.com%2Feslint%2Fdownload%2Feslint-7.32.0.tgz"
  integrity sha1-xtMooUvj+wjI0dIeEsAv23oqgS0=
  dependencies:
    "@babel/code-frame" "7.12.11"
    "@eslint/eslintrc" "^0.4.3"
    "@humanwhocodes/config-array" "^0.5.0"
    ajv "^6.10.0"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.0.1"
    doctrine "^3.0.0"
    enquirer "^2.3.5"
    escape-string-regexp "^4.0.0"
    eslint-scope "^5.1.1"
    eslint-utils "^2.1.0"
    eslint-visitor-keys "^2.0.0"
    espree "^7.3.1"
    esquery "^1.4.0"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^5.1.2"
    globals "^13.6.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    js-yaml "^3.13.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.0.4"
    natural-compare "^1.4.0"
    optionator "^0.9.1"
    progress "^2.0.0"
    regexpp "^3.1.0"
    semver "^7.2.1"
    strip-ansi "^6.0.0"
    strip-json-comments "^3.1.0"
    table "^6.0.9"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^7.3.0, espree@^7.3.1:
  version "7.3.1"
  resolved "https://registry.nlark.com/espree/download/espree-7.3.1.tgz"
  integrity sha1-8t8zC3Usb1UBn4vYm3ZgA5wbu7Y=
  dependencies:
    acorn "^7.4.0"
    acorn-jsx "^5.3.1"
    eslint-visitor-keys "^1.3.0"

esprima@^4.0.0, esprima@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/esprima/download/esprima-4.0.1.tgz"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npm.taobao.org/esquery/download/esquery-1.4.0.tgz"
  integrity sha1-IUj/w4uC6McFff7UhCWz5h8PJKU=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://registry.nlark.com/esrecurse/download/esrecurse-4.3.0.tgz"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://registry.nlark.com/estraverse/download/estraverse-4.3.0.tgz"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.2.0"
  resolved "https://registry.nlark.com/estraverse/download/estraverse-5.2.0.tgz"
  integrity sha1-MH30JUfmzHMk088DwVXVzbjFOIA=

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npm.taobao.org/esutils/download/esutils-2.0.3.tgz"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@~1.8.1:
  version "1.8.1"
  resolved "https://registry.nlark.com/etag/download/etag-1.8.1.tgz"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

events@^3.2.0:
  version "3.3.0"
  resolved "https://registry.nlark.com/events/download/events-3.3.0.tgz"
  integrity sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=

exec-sh@^0.3.2:
  version "0.3.6"
  resolved "https://registry.nlark.com/exec-sh/download/exec-sh-0.3.6.tgz"
  integrity sha1-/yZPnjJVGaYMteJzaSlDSDzKY7w=

execa@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/execa/download/execa-1.0.0.tgz"
  integrity sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^4.0.0, execa@^4.0.2:
  version "4.1.0"
  resolved "https://registry.nlark.com/execa/download/execa-4.1.0.tgz"
  integrity sha1-TlSRrRVy8vF6d9OIxshXE1sihHo=
  dependencies:
    cross-spawn "^7.0.0"
    get-stream "^5.0.0"
    human-signals "^1.1.1"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.0"
    onetime "^5.1.0"
    signal-exit "^3.0.2"
    strip-final-newline "^2.0.0"

exit@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npm.taobao.org/exit/download/exit-0.1.2.tgz"
  integrity sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "https://registry.nlark.com/expand-brackets/download/expand-brackets-2.1.4.tgz"
  integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

expect@^26.6.2:
  version "26.6.2"
  resolved "https://registry.nlark.com/expect/download/expect-26.6.2.tgz?cache=0&sync_timestamp=1624900209408&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fexpect%2Fdownload%2Fexpect-26.6.2.tgz"
  integrity sha1-xrmWvya/P+GLZ7LQ9R/JgbqTRBc=
  dependencies:
    "@jest/types" "^26.6.2"
    ansi-styles "^4.0.0"
    jest-get-type "^26.3.0"
    jest-matcher-utils "^26.6.2"
    jest-message-util "^26.6.2"
    jest-regex-util "^26.0.0"

express@4.17.1:
  version "4.17.1"
  resolved "https://registry.npm.taobao.org/express/download/express-4.17.1.tgz?cache=0&sync_timestamp=1596722127254&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fexpress%2Fdownload%2Fexpress-4.17.1.tgz"
  integrity sha1-RJH8OGBc9R+GKdOcK10Cb5ikwTQ=
  dependencies:
    accepts "~1.3.7"
    array-flatten "1.1.1"
    body-parser "1.19.0"
    content-disposition "0.5.3"
    content-type "~1.0.4"
    cookie "0.4.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "~1.1.2"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "~1.1.2"
    fresh "0.5.2"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.5"
    qs "6.7.0"
    range-parser "~1.2.1"
    safe-buffer "5.1.2"
    send "0.17.1"
    serve-static "1.14.1"
    setprototypeof "1.1.1"
    statuses "~1.5.0"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/extend-shallow/download/extend-shallow-2.0.1.tgz"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "https://registry.nlark.com/extend-shallow/download/extend-shallow-3.0.2.tgz"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend@~3.0.2:
  version "3.0.2"
  resolved "https://registry.nlark.com/extend/download/extend-3.0.2.tgz"
  integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=

external-editor@^3.0.3:
  version "3.1.0"
  resolved "https://registry.nlark.com/external-editor/download/external-editor-3.1.0.tgz"
  integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extglob@^2.0.4:
  version "2.0.4"
  resolved "https://registry.npm.taobao.org/extglob/download/extglob-2.0.4.tgz"
  integrity sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

extsprintf@1.3.0:
  version "1.3.0"
  resolved "https://registry.nlark.com/extsprintf/download/extsprintf-1.3.0.tgz"
  integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=

extsprintf@^1.2.0:
  version "1.4.0"
  resolved "https://registry.nlark.com/extsprintf/download/extsprintf-1.4.0.tgz"
  integrity sha1-4mifjzVvrWLMplo6kcXfX5VRaS8=

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npm.taobao.org/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-diff@^1.1.2:
  version "1.2.0"
  resolved "https://registry.nlark.com/fast-diff/download/fast-diff-1.2.0.tgz"
  integrity sha1-c+4RmC2Gyq95WYKNUZz+kn+sXwM=

fast-glob@^3.1.1:
  version "3.2.7"
  resolved "https://registry.nlark.com/fast-glob/download/fast-glob-3.2.7.tgz"
  integrity sha1-/Wy3otfpqnp4RhEehaGW1rL3ZqE=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@2.1.0, fast-json-stable-stringify@2.x, fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6, fast-levenshtein@~2.0.6:
  version "2.0.6"
  resolved "https://registry.npm.taobao.org/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz?cache=0&sync_timestamp=1605292839055&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffast-levenshtein%2Fdownload%2Ffast-levenshtein-2.0.6.tgz"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fast-safe-stringify@2.0.7:
  version "2.0.7"
  resolved "https://registry.nlark.com/fast-safe-stringify/download/fast-safe-stringify-2.0.7.tgz?cache=0&sync_timestamp=1625835811932&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffast-safe-stringify%2Fdownload%2Ffast-safe-stringify-2.0.7.tgz"
  integrity sha1-EkqohYmSYfaK7bQqfAgN6dpgh0M=

fast-safe-stringify@^2.0.7:
  version "2.0.8"
  resolved "https://registry.nlark.com/fast-safe-stringify/download/fast-safe-stringify-2.0.8.tgz?cache=0&sync_timestamp=1625835811932&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffast-safe-stringify%2Fdownload%2Ffast-safe-stringify-2.0.8.tgz"
  integrity sha1-3Cr0jEbPcStoPoSbK71EazLek28=

fastq@^1.6.0:
  version "1.11.1"
  resolved "https://registry.nlark.com/fastq/download/fastq-1.11.1.tgz?cache=0&sync_timestamp=1625392906410&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffastq%2Fdownload%2Ffastq-1.11.1.tgz"
  integrity sha1-XYF1quF9thlH+LFiz8f2MmTSKAc=
  dependencies:
    reusify "^1.0.4"

fb-watchman@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/fb-watchman/download/fb-watchman-2.0.1.tgz"
  integrity sha1-/IT7OdJwnPP/bXQ3BhV7tXCKioU=
  dependencies:
    bser "2.1.1"

figlet@^1.1.1:
  version "1.5.0"
  resolved "https://registry.npm.taobao.org/figlet/download/figlet-1.5.0.tgz"
  integrity sha1-LbTQClhOUVWpYIBjLbkZITw+ADw=

figures@^3.0.0:
  version "3.2.0"
  resolved "https://registry.nlark.com/figures/download/figures-3.2.0.tgz?cache=0&sync_timestamp=1625254307578&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffigures%2Fdownload%2Ffigures-3.2.0.tgz"
  integrity sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npm.taobao.org/file-entry-cache/download/file-entry-cache-6.0.1.tgz"
  integrity sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=
  dependencies:
    flat-cache "^3.0.4"

fill-range@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/fill-range/download/fill-range-4.0.0.tgz"
  integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://registry.nlark.com/fill-range/download/fill-range-7.0.1.tgz"
  integrity sha1-GRmmp8df44ssfHflGYU12prN2kA=
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@~1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/finalhandler/download/finalhandler-1.1.2.tgz"
  integrity sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0=
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    statuses "~1.5.0"
    unpipe "~1.0.0"

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npm.taobao.org/find-up/download/find-up-4.1.0.tgz"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npm.taobao.org/flat-cache/download/flat-cache-3.0.4.tgz"
  integrity sha1-YbAzgwKy/p+Vfcwy/CqH8cMEixE=
  dependencies:
    flatted "^3.1.0"
    rimraf "^3.0.2"

flatted@^3.1.0:
  version "3.2.2"
  resolved "https://registry.nlark.com/flatted/download/flatted-3.2.2.tgz"
  integrity sha1-ZL/tXLaP48p4s+shStl7Y77c5WE=

fn-name@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/fn-name/-/fn-name-3.0.0.tgz"
  integrity sha512-eNMNr5exLoavuAMhIUVsOKF79SWd/zG104ef6sxBTSw+cZc6BXdQXDvYcGvp0VbxVVSp1XDUNoz7mg1xMtSznA==

follow-redirects@^1.10.0:
  version "1.14.1"
  resolved "https://registry.nlark.com/follow-redirects/download/follow-redirects-1.14.1.tgz?cache=0&sync_timestamp=1620555300559&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffollow-redirects%2Fdownload%2Ffollow-redirects-1.14.1.tgz"
  integrity sha1-2RFN7Qoc/dM04WTmZirQK/2R/0M=

for-in@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/for-in/download/for-in-1.0.2.tgz"
  integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "https://registry.nlark.com/forever-agent/download/forever-agent-0.6.1.tgz"
  integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=

fork-ts-checker-webpack-plugin@6.2.0:
  version "6.2.0"
  resolved "https://registry.nlark.com/fork-ts-checker-webpack-plugin/download/fork-ts-checker-webpack-plugin-6.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffork-ts-checker-webpack-plugin%2Fdownload%2Ffork-ts-checker-webpack-plugin-6.2.0.tgz"
  integrity sha1-0TrwLiTRsX92mva99BwehJ4WFcw=
  dependencies:
    "@babel/code-frame" "^7.8.3"
    "@types/json-schema" "^7.0.5"
    chalk "^4.1.0"
    chokidar "^3.4.2"
    cosmiconfig "^6.0.0"
    deepmerge "^4.2.2"
    fs-extra "^9.0.0"
    memfs "^3.1.2"
    minimatch "^3.0.4"
    schema-utils "2.7.0"
    semver "^7.3.2"
    tapable "^1.0.0"

form-data@^3.0.0, form-data@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npm.taobao.org/form-data/download/form-data-3.0.1.tgz"
  integrity sha1-69U3kbeDVqma+aMA1CgsTV65dV8=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

form-data@~2.3.2:
  version "2.3.3"
  resolved "https://registry.npm.taobao.org/form-data/download/form-data-2.3.3.tgz"
  integrity sha1-3M5SwF9kTymManq5Nr1yTO/786Y=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

formidable@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npm.taobao.org/formidable/download/formidable-1.2.2.tgz"
  integrity sha1-v2muopcpgmdfAIZTQrmCmG9rjdk=

forwarded@0.2.0:
  version "0.2.0"
  resolved "https://registry.nlark.com/forwarded/download/forwarded-0.2.0.tgz"
  integrity sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=

frac@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/frac/-/frac-1.1.2.tgz"
  integrity sha512-w/XBfkibaTl3YDqASwfDUqkna4Z2p9cFSr1aHDt0WoMTECnRfBOv2WArlZILlqgWlmdIlALXGpM2AOhEk5W3IA==

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "https://registry.nlark.com/fragment-cache/download/fragment-cache-0.2.1.tgz"
  integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
  dependencies:
    map-cache "^0.2.2"

fresh@0.5.2:
  version "0.5.2"
  resolved "https://registry.nlark.com/fresh/download/fresh-0.5.2.tgz?cache=0&sync_timestamp=1618847131116&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffresh%2Fdownload%2Ffresh-0.5.2.tgz"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

fs-extra@9.1.0, fs-extra@^9.0.0:
  version "9.1.0"
  resolved "https://registry.nlark.com/fs-extra/download/fs-extra-9.1.0.tgz"
  integrity sha1-WVRGDHZKjaIJS6NVS/g55rmnyG0=
  dependencies:
    at-least-node "^1.0.0"
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-minipass@^1.2.5:
  version "1.2.7"
  resolved "https://registry.nlark.com/fs-minipass/download/fs-minipass-1.2.7.tgz"
  integrity sha1-zP+FcIQef+QmVpPaiJNsVa7X98c=
  dependencies:
    minipass "^2.6.0"

fs-monkey@1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/fs-monkey/download/fs-monkey-1.0.3.tgz"
  integrity sha1-rjrJLVO7Mo7+DpodlUH2rY1I4tM=

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/fs.realpath/download/fs.realpath-1.0.0.tgz"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^2.1.2, fsevents@~2.3.1, fsevents@~2.3.2:
  version "2.3.3"
  resolved "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6"
  integrity sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==

fstream@^1.0.0, fstream@^1.0.12:
  version "1.0.12"
  resolved "https://registry.nlark.com/fstream/download/fstream-1.0.12.tgz"
  integrity sha1-Touo7i1Ivk99DeUFRVVI6uWTIEU=
  dependencies:
    graceful-fs "^4.1.2"
    inherits "~2.0.0"
    mkdirp ">=0.5 0"
    rimraf "2"

function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/function-bind/download/function-bind-1.1.1.tgz"
  integrity sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

gauge@~2.7.3:
  version "2.7.4"
  resolved "https://registry.nlark.com/gauge/download/gauge-2.7.4.tgz"
  integrity sha1-LANAXHU4w51+s3sxcCLjJfsBi/c=
  dependencies:
    aproba "^1.0.3"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.0"
    object-assign "^4.1.0"
    signal-exit "^3.0.0"
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wide-align "^1.1.0"

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://registry.nlark.com/gensync/download/gensync-1.0.0-beta.2.tgz"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-caller-file@^2.0.1, get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "https://registry.nlark.com/get-caller-file/download/get-caller-file-2.0.5.tgz"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-intrinsic@^1.0.2:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/get-intrinsic/download/get-intrinsic-1.1.1.tgz?cache=0&sync_timestamp=1612364352840&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fget-intrinsic%2Fdownload%2Fget-intrinsic-1.1.1.tgz"
  integrity sha1-FfWfN2+FXERpY5SPDSTNNje0q8Y=
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"

get-package-type@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npm.taobao.org/get-package-type/download/get-package-type-0.1.0.tgz"
  integrity sha1-jeLYA8/0TfO8bEVuZmizbDkm4Ro=

get-stream@^4.0.0:
  version "4.1.0"
  resolved "https://registry.nlark.com/get-stream/download/get-stream-4.1.0.tgz"
  integrity sha1-wbJVV189wh1Zv8ec09K0axw6VLU=
  dependencies:
    pump "^3.0.0"

get-stream@^5.0.0:
  version "5.2.0"
  resolved "https://registry.nlark.com/get-stream/download/get-stream-5.2.0.tgz"
  integrity sha1-SWaheV7lrOZecGxLe+txJX1uItM=
  dependencies:
    pump "^3.0.0"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "https://registry.nlark.com/get-value/download/get-value-2.0.6.tgz"
  integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=

getpass@^0.1.1:
  version "0.1.7"
  resolved "https://registry.nlark.com/getpass/download/getpass-0.1.7.tgz"
  integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
  dependencies:
    assert-plus "^1.0.0"

glob-parent@^5.1.2, glob-parent@~5.1.0, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.nlark.com/glob-parent/download/glob-parent-5.1.2.tgz?cache=0&sync_timestamp=1626760200164&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fglob-parent%2Fdownload%2Fglob-parent-5.1.2.tgz"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-to-regexp@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npm.taobao.org/glob-to-regexp/download/glob-to-regexp-0.4.1.tgz"
  integrity sha1-x1KXCHyFG5pXi9IX3VmpL1n+VG4=

glob@^7.0.0, glob@^7.0.3, glob@^7.1.1, glob@^7.1.2, glob@^7.1.3, glob@^7.1.4, glob@^7.1.6:
  version "7.1.7"
  resolved "https://registry.nlark.com/glob/download/glob-7.1.7.tgz?cache=0&sync_timestamp=1620337382269&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fglob%2Fdownload%2Fglob-7.1.7.tgz"
  integrity sha1-Oxk+kjPwHULQs/eClLvutBj5SpA=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://registry.nlark.com/globals/download/globals-11.12.0.tgz?cache=0&sync_timestamp=1625655787416&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fglobals%2Fdownload%2Fglobals-11.12.0.tgz"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^13.6.0, globals@^13.9.0:
  version "13.10.0"
  resolved "https://registry.nlark.com/globals/download/globals-13.10.0.tgz?cache=0&sync_timestamp=1625655787416&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fglobals%2Fdownload%2Fglobals-13.10.0.tgz"
  integrity sha1-YLpWw6wsqEXPv0+uynJ62d0gRnY=
  dependencies:
    type-fest "^0.20.2"

globby@^11.0.3:
  version "11.0.4"
  resolved "https://registry.nlark.com/globby/download/globby-11.0.4.tgz"
  integrity sha1-LLr/d8Lypi5x6bKBOme5ejowAaU=
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.1.1"
    ignore "^5.1.4"
    merge2 "^1.3.0"
    slash "^3.0.0"

graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.4:
  version "4.2.8"
  resolved "https://registry.nlark.com/graceful-fs/download/graceful-fs-4.2.8.tgz?cache=0&sync_timestamp=1628194078324&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fgraceful-fs%2Fdownload%2Fgraceful-fs-4.2.8.tgz"
  integrity sha1-5BK40z9eAGWTy9PO5t+fLOu+gCo=

growly@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npm.taobao.org/growly/download/growly-1.3.0.tgz"
  integrity sha1-8QdIy+dq+WS3yWyTxrzCivEgwIE=

har-schema@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/har-schema/download/har-schema-2.0.0.tgz"
  integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=

har-validator@~5.1.3:
  version "5.1.5"
  resolved "https://registry.npm.taobao.org/har-validator/download/har-validator-5.1.5.tgz"
  integrity sha1-HwgDufjLIMD6E4It8ezds2veHv0=
  dependencies:
    ajv "^6.12.3"
    har-schema "^2.0.0"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/has-ansi/download/has-ansi-2.0.0.tgz"
  integrity sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=
  dependencies:
    ansi-regex "^2.0.0"

has-binary2@~1.0.2:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/has-binary2/download/has-binary2-1.0.3.tgz"
  integrity sha1-d3asYn8+p3JQz8My2rfd9eT10R0=
  dependencies:
    isarray "2.0.1"

has-cors@1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/has-cors/download/has-cors-1.1.0.tgz"
  integrity sha1-XkdHk/fqmEPRu5nCPu9J/xJv/zk=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/has-flag/download/has-flag-3.0.0.tgz"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/has-flag/download/has-flag-4.0.0.tgz"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-symbols@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/has-symbols/download/has-symbols-1.0.2.tgz"
  integrity sha1-Fl0wcMADCXUqEjakeTMeOsVvFCM=

has-unicode@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/has-unicode/download/has-unicode-2.0.1.tgz"
  integrity sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=

has-value@^0.3.1:
  version "0.3.1"
  resolved "https://registry.nlark.com/has-value/download/has-value-0.3.1.tgz"
  integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/has-value/download/has-value-1.0.0.tgz"
  integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npm.taobao.org/has-values/download/has-values-0.1.4.tgz"
  integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=

has-values@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/has-values/download/has-values-1.0.0.tgz"
  integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.3:
  version "1.0.3"
  resolved "https://registry.nlark.com/has/download/has-1.0.3.tgz"
  integrity sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=
  dependencies:
    function-bind "^1.1.1"

highlight.js@^10.7.1:
  version "10.7.3"
  resolved "https://registry.nlark.com/highlight.js/download/highlight.js-10.7.3.tgz"
  integrity sha1-aXJy45kTVuQMPKxWanTu9oF1ZTE=

hosted-git-info@^2.1.4:
  version "2.8.9"
  resolved "https://registry.nlark.com/hosted-git-info/download/hosted-git-info-2.8.9.tgz"
  integrity sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=

html-encoding-sniffer@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/html-encoding-sniffer/download/html-encoding-sniffer-2.0.1.tgz"
  integrity sha1-QqbcT9M/ACgRduiyN1nKTk+hhfM=
  dependencies:
    whatwg-encoding "^1.0.5"

html-escaper@^2.0.0:
  version "2.0.2"
  resolved "https://registry.nlark.com/html-escaper/download/html-escaper-2.0.2.tgz"
  integrity sha1-39YAJ9o2o238viNiYsAKWCJoFFM=

http-errors@1.7.2:
  version "1.7.2"
  resolved "https://registry.nlark.com/http-errors/download/http-errors-1.7.2.tgz"
  integrity sha1-T1ApzxMjnzEDblsuVSkrz7zIXI8=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.1"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-errors@~1.7.2:
  version "1.7.3"
  resolved "https://registry.nlark.com/http-errors/download/http-errors-1.7.3.tgz"
  integrity sha1-bGGeT5xgMIw4UZSYwU+7EKrOuwY=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.4"
    setprototypeof "1.1.1"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-proxy-agent@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/http-proxy-agent/download/http-proxy-agent-4.0.1.tgz"
  integrity sha1-ioyO9/WTLM+VPClsqCkblap0qjo=
  dependencies:
    "@tootallnate/once" "1"
    agent-base "6"
    debug "4"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/http-signature/download/http-signature-1.2.0.tgz?cache=0&sync_timestamp=1600868441269&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-signature%2Fdownload%2Fhttp-signature-1.2.0.tgz"
  integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

https-proxy-agent@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npm.taobao.org/https-proxy-agent/download/https-proxy-agent-5.0.0.tgz"
  integrity sha1-4qkFQqu2inYuCghQ9sntrf2FBrI=
  dependencies:
    agent-base "6"
    debug "4"

human-signals@^1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/human-signals/download/human-signals-1.1.1.tgz?cache=0&sync_timestamp=1624364695595&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhuman-signals%2Fdownload%2Fhuman-signals-1.1.1.tgz"
  integrity sha1-xbHNFPUK6uCatsWf5jujOV/k36M=

iconv-lite@0.4.24, iconv-lite@^0.4.24, iconv-lite@^0.4.4:
  version "0.4.24"
  resolved "https://registry.nlark.com/iconv-lite/download/iconv-lite-0.4.24.tgz"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

ieee754@^1.1.13, ieee754@^1.2.1:
  version "1.2.1"
  resolved "https://registry.nlark.com/ieee754/download/ieee754-1.2.1.tgz"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

ignore-walk@^3.0.1:
  version "3.0.4"
  resolved "https://registry.nlark.com/ignore-walk/download/ignore-walk-3.0.4.tgz"
  integrity sha1-yaCfabfHtHml10rBo8DUI20qYzU=
  dependencies:
    minimatch "^3.0.4"

ignore@^4.0.6:
  version "4.0.6"
  resolved "https://registry.nlark.com/ignore/download/ignore-4.0.6.tgz"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

ignore@^5.1.4:
  version "5.1.8"
  resolved "https://registry.nlark.com/ignore/download/ignore-5.1.8.tgz"
  integrity sha1-8VCotQo0KJsz4i9YiavU2AFvDlc=

import-fresh@^3.0.0, import-fresh@^3.1.0, import-fresh@^3.2.1:
  version "3.3.0"
  resolved "https://registry.npm.taobao.org/import-fresh/download/import-fresh-3.3.0.tgz?cache=0&sync_timestamp=1608469472392&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fimport-fresh%2Fdownload%2Fimport-fresh-3.3.0.tgz"
  integrity sha1-NxYsJfy566oublPVtNiM4X2eDCs=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-local@^3.0.2:
  version "3.0.2"
  resolved "https://registry.nlark.com/import-local/download/import-local-3.0.2.tgz"
  integrity sha1-qM/QQx0d5KIZlwPQA+PmI2T6bbY=
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.nlark.com/imurmurhash/download/imurmurhash-0.1.4.tgz"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indexof@0.0.1:
  version "0.0.1"
  resolved "https://registry.nlark.com/indexof/download/indexof-0.0.1.tgz"
  integrity sha1-gtwzbSMrkGIXnQWrMpOmYFn9Q10=

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.nlark.com/inflight/download/inflight-1.0.6.tgz"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.0, inherits@~2.0.1, inherits@~2.0.3:
  version "2.0.4"
  resolved "https://registry.nlark.com/inherits/download/inherits-2.0.4.tgz"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inherits@2.0.3:
  version "2.0.3"
  resolved "https://registry.nlark.com/inherits/download/inherits-2.0.3.tgz"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

ini@~1.3.0:
  version "1.3.8"
  resolved "https://registry.nlark.com/ini/download/ini-1.3.8.tgz"
  integrity sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=

inquirer@7.3.3:
  version "7.3.3"
  resolved "https://registry.nlark.com/inquirer/download/inquirer-7.3.3.tgz"
  integrity sha1-BNF2sq8Er8FXqD/XwQDpjuCq0AM=
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.19"
    mute-stream "0.0.8"
    run-async "^2.4.0"
    rxjs "^6.6.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"

interpret@^1.0.0:
  version "1.4.0"
  resolved "https://registry.nlark.com/interpret/download/interpret-1.4.0.tgz"
  integrity sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4=

ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "https://registry.nlark.com/ipaddr.js/download/ipaddr.js-1.9.1.tgz"
  integrity sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=

is-accessor-descriptor@^0.1.6:
  version "0.1.6"
  resolved "https://registry.npm.taobao.org/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz"
  integrity sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=
  dependencies:
    kind-of "^3.0.2"

is-accessor-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz"
  integrity sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=
  dependencies:
    kind-of "^6.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npm.taobao.org/is-arrayish/download/is-arrayish-0.2.1.tgz"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/is-binary-path/download/is-binary-path-2.1.0.tgz"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-buffer@^1.1.5, is-buffer@~1.1.6:
  version "1.1.6"
  resolved "https://registry.npm.taobao.org/is-buffer/download/is-buffer-1.1.6.tgz?cache=0&sync_timestamp=1604432378894&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-buffer%2Fdownload%2Fis-buffer-1.1.6.tgz"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-ci@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/is-ci/download/is-ci-2.0.0.tgz?cache=0&sync_timestamp=1613631987391&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-ci%2Fdownload%2Fis-ci-2.0.0.tgz"
  integrity sha1-a8YzQYGBDgS1wis9WJ/cpVAmQEw=
  dependencies:
    ci-info "^2.0.0"

is-class@^0.0.9:
  version "0.0.9"
  resolved "https://registry.npm.taobao.org/is-class/download/is-class-0.0.9.tgz"
  integrity sha1-MCTBexz49r3B8+yhuuUH6pkrbZM=

is-core-module@^2.2.0:
  version "2.5.0"
  resolved "https://registry.nlark.com/is-core-module/download/is-core-module-2.5.0.tgz?cache=0&sync_timestamp=1626158736739&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-core-module%2Fdownload%2Fis-core-module-2.5.0.tgz"
  integrity sha1-91SENhfHC/0pt72HMnQAzaXBhJE=
  dependencies:
    has "^1.0.3"

is-data-descriptor@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npm.taobao.org/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz"
  integrity sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=
  dependencies:
    kind-of "^3.0.2"

is-data-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz"
  integrity sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=
  dependencies:
    kind-of "^6.0.0"

is-descriptor@^0.1.0:
  version "0.1.6"
  resolved "https://registry.nlark.com/is-descriptor/download/is-descriptor-0.1.6.tgz"
  integrity sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=
  dependencies:
    is-accessor-descriptor "^0.1.6"
    is-data-descriptor "^0.1.4"
    kind-of "^5.0.0"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/is-descriptor/download/is-descriptor-1.0.2.tgz"
  integrity sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-docker@^2.0.0:
  version "2.2.1"
  resolved "https://registry.npm.taobao.org/is-docker/download/is-docker-2.2.1.tgz?cache=0&sync_timestamp=1617958843085&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-docker%2Fdownload%2Fis-docker-2.2.1.tgz"
  integrity sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "https://registry.nlark.com/is-extendable/download/is-extendable-0.1.1.tgz"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/is-extendable/download/is-extendable-1.0.1.tgz"
  integrity sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.nlark.com/is-extglob/download/is-extglob-2.1.1.tgz"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz?cache=0&sync_timestamp=1618552489864&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-fullwidth-code-point%2Fdownload%2Fis-fullwidth-code-point-1.0.0.tgz"
  integrity sha1-754xOG8DGn8NZDr4L95QxFfvAMs=
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz?cache=0&sync_timestamp=1618552489864&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-fullwidth-code-point%2Fdownload%2Fis-fullwidth-code-point-2.0.0.tgz"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz?cache=0&sync_timestamp=1618552489864&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-fullwidth-code-point%2Fdownload%2Fis-fullwidth-code-point-3.0.0.tgz"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-generator-fn@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/is-generator-fn/download/is-generator-fn-2.1.0.tgz"
  integrity sha1-fRQK3DiarzARqPKipM+m+q3/sRg=

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@~4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/is-glob/download/is-glob-4.0.1.tgz"
  integrity sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw=
  dependencies:
    is-extglob "^2.1.1"

is-interactive@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/is-interactive/download/is-interactive-1.0.0.tgz"
  integrity sha1-zqbmrlyHCnsKAAQHC3tYfgJSkS4=

is-number@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/is-number/download/is-number-3.0.0.tgz"
  integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
  dependencies:
    kind-of "^3.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npm.taobao.org/is-number/download/is-number-7.0.0.tgz"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "https://registry.nlark.com/is-plain-object/download/is-plain-object-2.0.4.tgz"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-potential-custom-element-name@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/is-potential-custom-element-name/download/is-potential-custom-element-name-1.0.1.tgz"
  integrity sha1-Fx7W8Z46xVQ5Tt94yqBXhKRb67U=

is-stream@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/is-stream/download/is-stream-1.1.0.tgz"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/is-stream/download/is-stream-2.0.1.tgz"
  integrity sha1-+sHj1TuXrVqdCunO8jifWBClwHc=

is-typedarray@^1.0.0, is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/is-typedarray/download/is-typedarray-1.0.0.tgz"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-unicode-supported@^0.1.0:
  version "0.1.0"
  resolved "https://registry.nlark.com/is-unicode-supported/download/is-unicode-supported-0.1.0.tgz?cache=0&sync_timestamp=1625294161966&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-unicode-supported%2Fdownload%2Fis-unicode-supported-0.1.0.tgz"
  integrity sha1-PybHaoCVk7Ur+i7LVxDtJ3m1Iqc=

is-windows@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/is-windows/download/is-windows-1.0.2.tgz"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

is-wsl@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/is-wsl/download/is-wsl-2.2.0.tgz"
  integrity sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=
  dependencies:
    is-docker "^2.0.0"

is_js@^0.9.0:
  version "0.9.0"
  resolved "https://registry.npmmirror.com/is_js/-/is_js-0.9.0.tgz"
  integrity sha512-8Y5EHSH+TonfUHX2g3pMJljdbGavg55q4jmHzghJCdqYDbdNROC8uw/YFQwIRCRqRJT1EY3pJefz+kglw+o7sg==

isarray@0.0.1:
  version "0.0.1"
  resolved "https://registry.npm.taobao.org/isarray/download/isarray-0.0.1.tgz"
  integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=

isarray@1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/isarray/download/isarray-1.0.0.tgz"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isarray@2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/isarray/download/isarray-2.0.1.tgz"
  integrity sha1-o32U7ZzaLVmGXJ92/llu4fM4dB4=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/isexe/download/isexe-2.0.0.tgz"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isobject@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/isobject/download/isobject-2.1.0.tgz"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npm.taobao.org/isobject/download/isobject-3.0.1.tgz"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

isstream@~0.1.2:
  version "0.1.2"
  resolved "https://registry.npm.taobao.org/isstream/download/isstream-0.1.2.tgz"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

istanbul-lib-coverage@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/istanbul-lib-coverage/download/istanbul-lib-coverage-3.0.0.tgz"
  integrity sha1-9ZRKN8cLVQsCp4pcOyBVsoDOyOw=

istanbul-lib-instrument@^4.0.0, istanbul-lib-instrument@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npm.taobao.org/istanbul-lib-instrument/download/istanbul-lib-instrument-4.0.3.tgz"
  integrity sha1-hzxv/4l0UBGCIndGlqPyiQLXfB0=
  dependencies:
    "@babel/core" "^7.7.5"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-coverage "^3.0.0"
    semver "^6.3.0"

istanbul-lib-report@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/istanbul-lib-report/download/istanbul-lib-report-3.0.0.tgz"
  integrity sha1-dRj+UupE3jcvRgp2tezan/tz2KY=
  dependencies:
    istanbul-lib-coverage "^3.0.0"
    make-dir "^3.0.0"
    supports-color "^7.1.0"

istanbul-lib-source-maps@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/istanbul-lib-source-maps/download/istanbul-lib-source-maps-4.0.0.tgz"
  integrity sha1-dXQ85tlruG3H7kNSz2Nmoj8LGtk=
  dependencies:
    debug "^4.1.1"
    istanbul-lib-coverage "^3.0.0"
    source-map "^0.6.1"

istanbul-reports@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npm.taobao.org/istanbul-reports/download/istanbul-reports-3.0.2.tgz"
  integrity sha1-1ZMhDlAAaDdQywn8BkTktuJ/1Ts=
  dependencies:
    html-escaper "^2.0.0"
    istanbul-lib-report "^3.0.0"

iterare@1.2.1:
  version "1.2.1"
  resolved "https://registry.npm.taobao.org/iterare/download/iterare-1.2.1.tgz"
  integrity sha1-E5xAD/c2NpDjOr/6M8u6iSDwAEI=

jest-changed-files@^26.6.2:
  version "26.6.2"
  resolved "https://registry.nlark.com/jest-changed-files/download/jest-changed-files-26.6.2.tgz?cache=0&sync_timestamp=1624900201608&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-changed-files%2Fdownload%2Fjest-changed-files-26.6.2.tgz"
  integrity sha1-9hmEeeHMZvIvmuHiKsqgtCnAQtA=
  dependencies:
    "@jest/types" "^26.6.2"
    execa "^4.0.0"
    throat "^5.0.0"

jest-cli@^26.6.3:
  version "26.6.3"
  resolved "https://registry.nlark.com/jest-cli/download/jest-cli-26.6.3.tgz"
  integrity sha1-QxF8/vJLxM1pGhdKh5alMuE16So=
  dependencies:
    "@jest/core" "^26.6.3"
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    chalk "^4.0.0"
    exit "^0.1.2"
    graceful-fs "^4.2.4"
    import-local "^3.0.2"
    is-ci "^2.0.0"
    jest-config "^26.6.3"
    jest-util "^26.6.2"
    jest-validate "^26.6.2"
    prompts "^2.0.1"
    yargs "^15.4.1"

jest-config@^26.6.3:
  version "26.6.3"
  resolved "https://registry.nlark.com/jest-config/download/jest-config-26.6.3.tgz?cache=0&sync_timestamp=1624900217320&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-config%2Fdownload%2Fjest-config-26.6.3.tgz"
  integrity sha1-ZPQURO756wPcUdXFO3XIxx9kU0k=
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/test-sequencer" "^26.6.3"
    "@jest/types" "^26.6.2"
    babel-jest "^26.6.3"
    chalk "^4.0.0"
    deepmerge "^4.2.2"
    glob "^7.1.1"
    graceful-fs "^4.2.4"
    jest-environment-jsdom "^26.6.2"
    jest-environment-node "^26.6.2"
    jest-get-type "^26.3.0"
    jest-jasmine2 "^26.6.3"
    jest-regex-util "^26.0.0"
    jest-resolve "^26.6.2"
    jest-util "^26.6.2"
    jest-validate "^26.6.2"
    micromatch "^4.0.2"
    pretty-format "^26.6.2"

jest-diff@^26.0.0, jest-diff@^26.6.2:
  version "26.6.2"
  resolved "https://registry.nlark.com/jest-diff/download/jest-diff-26.6.2.tgz?cache=0&sync_timestamp=1624900066336&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-diff%2Fdownload%2Fjest-diff-26.6.2.tgz"
  integrity sha1-GqdGi1LDpo19XF/c381eSb0WQ5Q=
  dependencies:
    chalk "^4.0.0"
    diff-sequences "^26.6.2"
    jest-get-type "^26.3.0"
    pretty-format "^26.6.2"

jest-docblock@^26.0.0:
  version "26.0.0"
  resolved "https://registry.nlark.com/jest-docblock/download/jest-docblock-26.0.0.tgz?cache=0&sync_timestamp=1624900203204&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-docblock%2Fdownload%2Fjest-docblock-26.0.0.tgz"
  integrity sha1-Pi+iCJn8koyxO9D/aL03EaNoibU=
  dependencies:
    detect-newline "^3.0.0"

jest-each@^26.6.2:
  version "26.6.2"
  resolved "https://registry.nlark.com/jest-each/download/jest-each-26.6.2.tgz?cache=0&sync_timestamp=1624900207820&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-each%2Fdownload%2Fjest-each-26.6.2.tgz"
  integrity sha1-AlJkOKd6Z0AcimOC3+WZmVLBZ8s=
  dependencies:
    "@jest/types" "^26.6.2"
    chalk "^4.0.0"
    jest-get-type "^26.3.0"
    jest-util "^26.6.2"
    pretty-format "^26.6.2"

jest-environment-jsdom@^26.6.2:
  version "26.6.2"
  resolved "https://registry.nlark.com/jest-environment-jsdom/download/jest-environment-jsdom-26.6.2.tgz?cache=0&sync_timestamp=1624900212261&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-environment-jsdom%2Fdownload%2Fjest-environment-jsdom-26.6.2.tgz"
  integrity sha1-eNCf6c8BmjVwCbm34fEB0jvR2j4=
  dependencies:
    "@jest/environment" "^26.6.2"
    "@jest/fake-timers" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    jest-mock "^26.6.2"
    jest-util "^26.6.2"
    jsdom "^16.4.0"

jest-environment-node@^26.6.2:
  version "26.6.2"
  resolved "https://registry.nlark.com/jest-environment-node/download/jest-environment-node-26.6.2.tgz?cache=0&sync_timestamp=1624900212583&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-environment-node%2Fdownload%2Fjest-environment-node-26.6.2.tgz"
  integrity sha1-gk5Mf7SURkY1bxGsdbIpsANfKww=
  dependencies:
    "@jest/environment" "^26.6.2"
    "@jest/fake-timers" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    jest-mock "^26.6.2"
    jest-util "^26.6.2"

jest-get-type@^26.3.0:
  version "26.3.0"
  resolved "https://registry.nlark.com/jest-get-type/download/jest-get-type-26.3.0.tgz"
  integrity sha1-6X3Dw/U8K0Bsp6+u1Ek7HQmRmeA=

jest-haste-map@^26.6.2:
  version "26.6.2"
  resolved "https://registry.nlark.com/jest-haste-map/download/jest-haste-map-26.6.2.tgz?cache=0&sync_timestamp=1624900208778&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-haste-map%2Fdownload%2Fjest-haste-map-26.6.2.tgz"
  integrity sha1-3X5g/n3A6fkRoj15xf9/tcLK/qo=
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/graceful-fs" "^4.1.2"
    "@types/node" "*"
    anymatch "^3.0.3"
    fb-watchman "^2.0.0"
    graceful-fs "^4.2.4"
    jest-regex-util "^26.0.0"
    jest-serializer "^26.6.2"
    jest-util "^26.6.2"
    jest-worker "^26.6.2"
    micromatch "^4.0.2"
    sane "^4.0.3"
    walker "^1.0.7"
  optionalDependencies:
    fsevents "^2.1.2"

jest-jasmine2@^26.6.3:
  version "26.6.3"
  resolved "https://registry.nlark.com/jest-jasmine2/download/jest-jasmine2-26.6.3.tgz?cache=0&sync_timestamp=1624900216626&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-jasmine2%2Fdownload%2Fjest-jasmine2-26.6.3.tgz"
  integrity sha1-rcPPkV3qy1ISyTufNUfNEpWPLt0=
  dependencies:
    "@babel/traverse" "^7.1.0"
    "@jest/environment" "^26.6.2"
    "@jest/source-map" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    chalk "^4.0.0"
    co "^4.6.0"
    expect "^26.6.2"
    is-generator-fn "^2.0.0"
    jest-each "^26.6.2"
    jest-matcher-utils "^26.6.2"
    jest-message-util "^26.6.2"
    jest-runtime "^26.6.3"
    jest-snapshot "^26.6.2"
    jest-util "^26.6.2"
    pretty-format "^26.6.2"
    throat "^5.0.0"

jest-leak-detector@^26.6.2:
  version "26.6.2"
  resolved "https://registry.nlark.com/jest-leak-detector/download/jest-leak-detector-26.6.2.tgz?cache=0&sync_timestamp=1624900208111&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-leak-detector%2Fdownload%2Fjest-leak-detector-26.6.2.tgz"
  integrity sha1-dxfPEYuSI48uumUFTIoMnGU6ka8=
  dependencies:
    jest-get-type "^26.3.0"
    pretty-format "^26.6.2"

jest-matcher-utils@^26.6.2:
  version "26.6.2"
  resolved "https://registry.nlark.com/jest-matcher-utils/download/jest-matcher-utils-26.6.2.tgz?cache=0&sync_timestamp=1624900209066&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-matcher-utils%2Fdownload%2Fjest-matcher-utils-26.6.2.tgz"
  integrity sha1-jm/W6GPIstMaxkcu6yN7xZXlPno=
  dependencies:
    chalk "^4.0.0"
    jest-diff "^26.6.2"
    jest-get-type "^26.3.0"
    pretty-format "^26.6.2"

jest-message-util@^26.6.2:
  version "26.6.2"
  resolved "https://registry.nlark.com/jest-message-util/download/jest-message-util-26.6.2.tgz?cache=0&sync_timestamp=1624900207199&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-message-util%2Fdownload%2Fjest-message-util-26.6.2.tgz"
  integrity sha1-WBc3RK1vwFBrXSEVC5vlbvABygc=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@jest/types" "^26.6.2"
    "@types/stack-utils" "^2.0.0"
    chalk "^4.0.0"
    graceful-fs "^4.2.4"
    micromatch "^4.0.2"
    pretty-format "^26.6.2"
    slash "^3.0.0"
    stack-utils "^2.0.2"

jest-mock@^26.6.2:
  version "26.6.2"
  resolved "https://registry.nlark.com/jest-mock/download/jest-mock-26.6.2.tgz?cache=0&sync_timestamp=1624900204865&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-mock%2Fdownload%2Fjest-mock-26.6.2.tgz"
  integrity sha1-1stxKwQe1H/g2bb8NHS8ZUP+swI=
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/node" "*"

jest-pnp-resolver@^1.2.2:
  version "1.2.2"
  resolved "https://registry.nlark.com/jest-pnp-resolver/download/jest-pnp-resolver-1.2.2.tgz"
  integrity sha1-twSsCuAoqJEIpNBAs/kZ393I4zw=

jest-regex-util@^26.0.0:
  version "26.0.0"
  resolved "https://registry.nlark.com/jest-regex-util/download/jest-regex-util-26.0.0.tgz?cache=0&sync_timestamp=1624900201901&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-regex-util%2Fdownload%2Fjest-regex-util-26.0.0.tgz"
  integrity sha1-0l5xhLNuOf1GbDvEG+CXHoIf7ig=

jest-resolve-dependencies@^26.6.3:
  version "26.6.3"
  resolved "https://registry.nlark.com/jest-resolve-dependencies/download/jest-resolve-dependencies-26.6.3.tgz?cache=0&sync_timestamp=1624900213982&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-resolve-dependencies%2Fdownload%2Fjest-resolve-dependencies-26.6.3.tgz"
  integrity sha1-ZoCFnuXSLuXc2WH+SHH1n0x4T7Y=
  dependencies:
    "@jest/types" "^26.6.2"
    jest-regex-util "^26.0.0"
    jest-snapshot "^26.6.2"

jest-resolve@^26.6.2:
  version "26.6.2"
  resolved "https://registry.nlark.com/jest-resolve/download/jest-resolve-26.6.2.tgz?cache=0&sync_timestamp=1624900209782&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-resolve%2Fdownload%2Fjest-resolve-26.6.2.tgz"
  integrity sha1-o6sVFyF/RptQTxtWYDxbtUH7tQc=
  dependencies:
    "@jest/types" "^26.6.2"
    chalk "^4.0.0"
    graceful-fs "^4.2.4"
    jest-pnp-resolver "^1.2.2"
    jest-util "^26.6.2"
    read-pkg-up "^7.0.1"
    resolve "^1.18.1"
    slash "^3.0.0"

jest-runner@^26.6.3:
  version "26.6.3"
  resolved "https://registry.nlark.com/jest-runner/download/jest-runner-26.6.3.tgz?cache=0&sync_timestamp=1624900215910&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-runner%2Fdownload%2Fjest-runner-26.6.3.tgz"
  integrity sha1-LR/tPUbhDyM/0dvTv6o/6JJL4Vk=
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/environment" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    chalk "^4.0.0"
    emittery "^0.7.1"
    exit "^0.1.2"
    graceful-fs "^4.2.4"
    jest-config "^26.6.3"
    jest-docblock "^26.0.0"
    jest-haste-map "^26.6.2"
    jest-leak-detector "^26.6.2"
    jest-message-util "^26.6.2"
    jest-resolve "^26.6.2"
    jest-runtime "^26.6.3"
    jest-util "^26.6.2"
    jest-worker "^26.6.2"
    source-map-support "^0.5.6"
    throat "^5.0.0"

jest-runtime@^26.6.3:
  version "26.6.3"
  resolved "https://registry.nlark.com/jest-runtime/download/jest-runtime-26.6.3.tgz?cache=0&sync_timestamp=1624900215464&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-runtime%2Fdownload%2Fjest-runtime-26.6.3.tgz"
  integrity sha1-T2TvvPrDmDMbdLSzyC0n1AG4+is=
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/environment" "^26.6.2"
    "@jest/fake-timers" "^26.6.2"
    "@jest/globals" "^26.6.2"
    "@jest/source-map" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/yargs" "^15.0.0"
    chalk "^4.0.0"
    cjs-module-lexer "^0.6.0"
    collect-v8-coverage "^1.0.0"
    exit "^0.1.2"
    glob "^7.1.3"
    graceful-fs "^4.2.4"
    jest-config "^26.6.3"
    jest-haste-map "^26.6.2"
    jest-message-util "^26.6.2"
    jest-mock "^26.6.2"
    jest-regex-util "^26.0.0"
    jest-resolve "^26.6.2"
    jest-snapshot "^26.6.2"
    jest-util "^26.6.2"
    jest-validate "^26.6.2"
    slash "^3.0.0"
    strip-bom "^4.0.0"
    yargs "^15.4.1"

jest-serializer@^26.6.2:
  version "26.6.2"
  resolved "https://registry.nlark.com/jest-serializer/download/jest-serializer-26.6.2.tgz?cache=0&sync_timestamp=1624900202593&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-serializer%2Fdownload%2Fjest-serializer-26.6.2.tgz"
  integrity sha1-0Tmq/UaVfTpEjzps2r4pGboHQtE=
  dependencies:
    "@types/node" "*"
    graceful-fs "^4.2.4"

jest-snapshot@^26.6.2:
  version "26.6.2"
  resolved "https://registry.nlark.com/jest-snapshot/download/jest-snapshot-26.6.2.tgz?cache=0&sync_timestamp=1624900213636&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-snapshot%2Fdownload%2Fjest-snapshot-26.6.2.tgz"
  integrity sha1-87CvGssiMxaFC9FOG+6pg3+znIQ=
  dependencies:
    "@babel/types" "^7.0.0"
    "@jest/types" "^26.6.2"
    "@types/babel__traverse" "^7.0.4"
    "@types/prettier" "^2.0.0"
    chalk "^4.0.0"
    expect "^26.6.2"
    graceful-fs "^4.2.4"
    jest-diff "^26.6.2"
    jest-get-type "^26.3.0"
    jest-haste-map "^26.6.2"
    jest-matcher-utils "^26.6.2"
    jest-message-util "^26.6.2"
    jest-resolve "^26.6.2"
    natural-compare "^1.4.0"
    pretty-format "^26.6.2"
    semver "^7.3.2"

jest-util@^26.1.0, jest-util@^26.6.2:
  version "26.6.2"
  resolved "https://registry.nlark.com/jest-util/download/jest-util-26.6.2.tgz?cache=0&sync_timestamp=1624900206796&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-util%2Fdownload%2Fjest-util-26.6.2.tgz"
  integrity sha1-kHU12+TVpstMR6ybkm9q8pV2y8E=
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    chalk "^4.0.0"
    graceful-fs "^4.2.4"
    is-ci "^2.0.0"
    micromatch "^4.0.2"

jest-validate@^26.6.2:
  version "26.6.2"
  resolved "https://registry.nlark.com/jest-validate/download/jest-validate-26.6.2.tgz?cache=0&sync_timestamp=1624900207558&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-validate%2Fdownload%2Fjest-validate-26.6.2.tgz"
  integrity sha1-I9OAlxWHFQRnNCkRw9e0rFerIOw=
  dependencies:
    "@jest/types" "^26.6.2"
    camelcase "^6.0.0"
    chalk "^4.0.0"
    jest-get-type "^26.3.0"
    leven "^3.1.0"
    pretty-format "^26.6.2"

jest-watcher@^26.6.2:
  version "26.6.2"
  resolved "https://registry.nlark.com/jest-watcher/download/jest-watcher-26.6.2.tgz?cache=0&sync_timestamp=1624900210552&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest-watcher%2Fdownload%2Fjest-watcher-26.6.2.tgz"
  integrity sha1-pbaDuPnWjbyx19rjIXLSzKBZKXU=
  dependencies:
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    jest-util "^26.6.2"
    string-length "^4.0.1"

jest-worker@^26.6.2:
  version "26.6.2"
  resolved "https://registry.nlark.com/jest-worker/download/jest-worker-26.6.2.tgz"
  integrity sha1-f3LLxNZDw2Xie5/XdfnQ6qnHqO0=
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^7.0.0"

jest-worker@^27.0.2:
  version "27.0.6"
  resolved "https://registry.nlark.com/jest-worker/download/jest-worker-27.0.6.tgz"
  integrity sha1-pf2x4UrTTrIoz+Fi2fcpzb+iiu0=
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest@^26.6.3:
  version "26.6.3"
  resolved "https://registry.nlark.com/jest/download/jest-26.6.3.tgz?cache=0&sync_timestamp=1624900218373&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjest%2Fdownload%2Fjest-26.6.3.tgz"
  integrity sha1-QOj9vkjwDfofDOgSHKdLiKyRSO8=
  dependencies:
    "@jest/core" "^26.6.3"
    import-local "^3.0.2"
    jest-cli "^26.6.3"

js-base64@^3.7.2:
  version "3.7.2"
  resolved "https://registry.npmmirror.com/js-base64/-/js-base64-3.7.2.tgz"
  integrity sha512-NnRs6dsyqUXejqk/yv2aiXlAvOs56sLkX6nUdeaNezI5LFFLlsZjOThmwnrcwh5ZZRwZlCMnVAY3CvhIhoVEKQ==

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/js-tokens/download/js-tokens-4.0.0.tgz?cache=0&sync_timestamp=1619345098261&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjs-tokens%2Fdownload%2Fjs-tokens-4.0.0.tgz"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "https://registry.nlark.com/js-yaml/download/js-yaml-3.14.1.tgz"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^4.0.0:
  version "4.1.0"
  resolved "https://registry.nlark.com/js-yaml/download/js-yaml-4.1.0.tgz"
  integrity sha1-wftl+PUBeQHN0slRhkuhhFihBgI=
  dependencies:
    argparse "^2.0.1"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "https://registry.nlark.com/jsbn/download/jsbn-0.1.1.tgz"
  integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=

jsdom@^16.4.0:
  version "16.7.0"
  resolved "https://registry.nlark.com/jsdom/download/jsdom-16.7.0.tgz?cache=0&sync_timestamp=1627851380169&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjsdom%2Fdownload%2Fjsdom-16.7.0.tgz"
  integrity sha1-kYrnGWVCSxl8gZ+Bg6dU4Yl3txA=
  dependencies:
    abab "^2.0.5"
    acorn "^8.2.4"
    acorn-globals "^6.0.0"
    cssom "^0.4.4"
    cssstyle "^2.3.0"
    data-urls "^2.0.0"
    decimal.js "^10.2.1"
    domexception "^2.0.1"
    escodegen "^2.0.0"
    form-data "^3.0.0"
    html-encoding-sniffer "^2.0.1"
    http-proxy-agent "^4.0.1"
    https-proxy-agent "^5.0.0"
    is-potential-custom-element-name "^1.0.1"
    nwsapi "^2.2.0"
    parse5 "6.0.1"
    saxes "^5.0.1"
    symbol-tree "^3.2.4"
    tough-cookie "^4.0.0"
    w3c-hr-time "^1.0.2"
    w3c-xmlserializer "^2.0.0"
    webidl-conversions "^6.1.0"
    whatwg-encoding "^1.0.5"
    whatwg-mimetype "^2.3.0"
    whatwg-url "^8.5.0"
    ws "^7.4.6"
    xml-name-validator "^3.0.0"

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://registry.npm.taobao.org/jsesc/download/jsesc-2.5.2.tgz?cache=0&sync_timestamp=1603891161295&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjsesc%2Fdownload%2Fjsesc-2.5.2.tgz"
  integrity sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=

json-parse-better-errors@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "https://registry.npm.taobao.org/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npm.taobao.org/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz"
  integrity sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=

json-schema@0.2.3:
  version "0.2.3"
  resolved "https://registry.nlark.com/json-schema/download/json-schema-0.2.3.tgz"
  integrity sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "https://registry.nlark.com/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

json5@2.x, json5@^2.1.2, json5@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/json5/download/json5-2.2.0.tgz?cache=0&sync_timestamp=1612146079519&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjson5%2Fdownload%2Fjson5-2.2.0.tgz"
  integrity sha1-Lf7+cgxrpSXZ69kJlQ8FFTFsiaM=
  dependencies:
    minimist "^1.2.5"

json5@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/json5/download/json5-1.0.1.tgz?cache=0&sync_timestamp=1612146079519&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjson5%2Fdownload%2Fjson5-1.0.1.tgz"
  integrity sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4=
  dependencies:
    minimist "^1.2.0"

jsonc-parser@3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/jsonc-parser/download/jsonc-parser-3.0.0.tgz?cache=0&sync_timestamp=1605282527742&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjsonc-parser%2Fdownload%2Fjsonc-parser-3.0.0.tgz"
  integrity sha1-q914VwHH5+rKip7IzwcMpRp0WiI=

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npm.taobao.org/jsonfile/download/jsonfile-6.1.0.tgz?cache=0&sync_timestamp=1604161844511&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjsonfile%2Fdownload%2Fjsonfile-6.1.0.tgz"
  integrity sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonwebtoken@8.5.1, jsonwebtoken@^8.2.0:
  version "8.5.1"
  resolved "https://registry.nlark.com/jsonwebtoken/download/jsonwebtoken-8.5.1.tgz"
  integrity sha1-AOceC431TCEhofJhN98igGc7zA0=
  dependencies:
    jws "^3.2.2"
    lodash.includes "^4.3.0"
    lodash.isboolean "^3.0.3"
    lodash.isinteger "^4.0.4"
    lodash.isnumber "^3.0.3"
    lodash.isplainobject "^4.0.6"
    lodash.isstring "^4.0.1"
    lodash.once "^4.0.0"
    ms "^2.1.1"
    semver "^5.6.0"

jsprim@^1.2.2:
  version "1.4.1"
  resolved "https://registry.nlark.com/jsprim/download/jsprim-1.4.1.tgz"
  integrity sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.2.3"
    verror "1.10.0"

jwa@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npm.taobao.org/jwa/download/jwa-1.4.1.tgz"
  integrity sha1-dDwymFy56YZVUw1TZBtmyGRbA5o=
  dependencies:
    buffer-equal-constant-time "1.0.1"
    ecdsa-sig-formatter "1.0.11"
    safe-buffer "^5.0.1"

jws@^3.2.2:
  version "3.2.2"
  resolved "https://registry.npm.taobao.org/jws/download/jws-3.2.2.tgz"
  integrity sha1-ABCZ82OUaMlBQADpmZX6UvtHgwQ=
  dependencies:
    jwa "^1.4.1"
    safe-buffer "^5.0.1"

kareem@2.3.2:
  version "2.3.2"
  resolved "https://registry.npm.taobao.org/kareem/download/kareem-2.3.2.tgz"
  integrity sha1-eMRQiJSYW404oNwV4ajhEHjyypM=

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "https://registry.nlark.com/kind-of/download/kind-of-3.2.2.tgz"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/kind-of/download/kind-of-4.0.0.tgz"
  integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0:
  version "5.1.0"
  resolved "https://registry.nlark.com/kind-of/download/kind-of-5.1.0.tgz"
  integrity sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=

kind-of@^6.0.0, kind-of@^6.0.2:
  version "6.0.3"
  resolved "https://registry.nlark.com/kind-of/download/kind-of-6.0.3.tgz"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

kleur@^3.0.3:
  version "3.0.3"
  resolved "https://registry.nlark.com/kleur/download/kleur-3.0.3.tgz"
  integrity sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=

leven@^3.1.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/leven/download/leven-3.1.0.tgz"
  integrity sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=

levn@^0.4.1:
  version "0.4.1"
  resolved "https://registry.nlark.com/levn/download/levn-0.4.1.tgz"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

levn@~0.3.0:
  version "0.3.0"
  resolved "https://registry.nlark.com/levn/download/levn-0.3.0.tgz"
  integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

lines-and-columns@^1.1.6:
  version "1.1.6"
  resolved "https://registry.npm.taobao.org/lines-and-columns/download/lines-and-columns-1.1.6.tgz"
  integrity sha1-HADHQ7QzzQpOgHWPe2SldEDZ/wA=

loader-runner@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npm.taobao.org/loader-runner/download/loader-runner-4.2.0.tgz"
  integrity sha1-1wIjgNZtFMX7HUlriYZOvP1Hg4Q=

loader-utils@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/loader-utils/download/loader-utils-2.0.0.tgz"
  integrity sha1-5MrOW4FtQloWa18JfhDNErNgZLA=
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^2.1.2"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://registry.nlark.com/locate-path/download/locate-path-5.0.0.tgz"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

lodash-es@^4.17.11:
  version "4.17.21"
  resolved "https://registry.npmmirror.com/lodash-es/-/lodash-es-4.17.21.tgz"
  integrity sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==

lodash.clonedeep@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npm.taobao.org/lodash.clonedeep/download/lodash.clonedeep-4.5.0.tgz"
  integrity sha1-4j8/nE+Pvd6HJSnBBxhXoIblzO8=

lodash.includes@^4.3.0:
  version "4.3.0"
  resolved "https://registry.nlark.com/lodash.includes/download/lodash.includes-4.3.0.tgz"
  integrity sha1-YLuYqHy5I8aMoeUTJUgzFISfVT8=

lodash.isboolean@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npm.taobao.org/lodash.isboolean/download/lodash.isboolean-3.0.3.tgz"
  integrity sha1-bC4XHbKiV82WgC/UOwGyDV9YcPY=

lodash.isinteger@^4.0.4:
  version "4.0.4"
  resolved "https://registry.npm.taobao.org/lodash.isinteger/download/lodash.isinteger-4.0.4.tgz"
  integrity sha1-YZwK89A/iwTDH1iChAt3sRzWg0M=

lodash.isnumber@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npm.taobao.org/lodash.isnumber/download/lodash.isnumber-3.0.3.tgz"
  integrity sha1-POdoEMWSjQM1IwGsKHMX8RwLH/w=

lodash.isplainobject@^4.0.6:
  version "4.0.6"
  resolved "https://registry.npm.taobao.org/lodash.isplainobject/download/lodash.isplainobject-4.0.6.tgz"
  integrity sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs=

lodash.isstring@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/lodash.isstring/download/lodash.isstring-4.0.1.tgz"
  integrity sha1-1SfftUVuynzJu5XV2ur4i6VKVFE=

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://registry.npm.taobao.org/lodash.merge/download/lodash.merge-4.6.2.tgz"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash.once@^4.0.0:
  version "4.1.1"
  resolved "https://registry.nlark.com/lodash.once/download/lodash.once-4.1.1.tgz"
  integrity sha1-DdOXEhPHxW34gJd9UEyI+0cal6w=

lodash.toarray@^4.4.0:
  version "4.4.0"
  resolved "https://registry.npm.taobao.org/lodash.toarray/download/lodash.toarray-4.4.0.tgz"
  integrity sha1-JMS/zWsvuji/0FlNsRedjptlZWE=

lodash.truncate@^4.4.2:
  version "4.4.2"
  resolved "https://registry.npm.taobao.org/lodash.truncate/download/lodash.truncate-4.4.2.tgz"
  integrity sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM=

lodash@4.17.21, lodash@4.x, lodash@^4.17.15, lodash@^4.17.19, lodash@^4.17.20, lodash@^4.17.21, lodash@^4.7.0:
  version "4.17.21"
  resolved "https://registry.nlark.com/lodash/download/lodash-4.17.21.tgz?cache=0&sync_timestamp=1624543041613&other_urls=https%3A%2F%2Fregistry.nlark.com%2Flodash%2Fdownload%2Flodash-4.17.21.tgz"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

log-symbols@^4.0.0, log-symbols@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npm.taobao.org/log-symbols/download/log-symbols-4.1.0.tgz?cache=0&sync_timestamp=1618723146520&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Flog-symbols%2Fdownload%2Flog-symbols-4.1.0.tgz"
  integrity sha1-P727lbRoOsn8eFER55LlWNSr1QM=
  dependencies:
    chalk "^4.1.0"
    is-unicode-supported "^0.1.0"

loglevel@^1.7.0:
  version "1.7.1"
  resolved "https://registry.nlark.com/loglevel/download/loglevel-1.7.1.tgz"
  integrity sha1-AF/eL15uRwaPk1/yhXPhJe9y8Zc=

lru-cache@6.0.0, lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://registry.nlark.com/lru-cache/download/lru-cache-6.0.0.tgz"
  integrity sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=
  dependencies:
    yallist "^4.0.0"

macos-release@^2.2.0:
  version "2.5.0"
  resolved "https://registry.nlark.com/macos-release/download/macos-release-2.5.0.tgz?cache=0&sync_timestamp=1623136645369&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fmacos-release%2Fdownload%2Fmacos-release-2.5.0.tgz"
  integrity sha1-BnwsiLXz+zxWo3Wy7JOCYiD6H/I=

magic-string@0.25.7:
  version "0.25.7"
  resolved "https://registry.npm.taobao.org/magic-string/download/magic-string-0.25.7.tgz"
  integrity sha1-P0l9b9NMZpxnmNy4IfLvMfVEUFE=
  dependencies:
    sourcemap-codec "^1.4.4"

make-dir@^3.0.0, make-dir@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/make-dir/download/make-dir-3.1.0.tgz"
  integrity sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=
  dependencies:
    semver "^6.0.0"

make-error@1.x, make-error@^1.1.1:
  version "1.3.6"
  resolved "https://registry.npm.taobao.org/make-error/download/make-error-1.3.6.tgz"
  integrity sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=

makeerror@1.0.x:
  version "1.0.11"
  resolved "https://registry.nlark.com/makeerror/download/makeerror-1.0.11.tgz"
  integrity sha1-4BpckQnyr3lmDk6LlYd5AYT1qWw=
  dependencies:
    tmpl "1.0.x"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "https://registry.nlark.com/map-cache/download/map-cache-0.2.2.tgz"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

map-visit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/map-visit/download/map-visit-1.0.0.tgz"
  integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
  dependencies:
    object-visit "^1.0.0"

md5@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/md5/-/md5-2.3.0.tgz"
  integrity sha512-T1GITYmFaKuO91vxyoQMFETst+O71VUPEU3ze5GNzDm0OWdP8v1ziTaAEPUr/3kLsY3Sftgz242A1SetQiDL7g==
  dependencies:
    charenc "0.0.2"
    crypt "0.0.2"
    is-buffer "~1.1.6"

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://registry.npm.taobao.org/media-typer/download/media-typer-0.3.0.tgz"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

memfs@^3.1.2:
  version "3.2.2"
  resolved "https://registry.npm.taobao.org/memfs/download/memfs-3.2.2.tgz?cache=0&sync_timestamp=1617600238024&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmemfs%2Fdownload%2Fmemfs-3.2.2.tgz"
  integrity sha1-XeRhOJ1Zbj8j1Iu3wq+2Fh9N9A4=
  dependencies:
    fs-monkey "1.0.3"

memory-fs@^0.5.0:
  version "0.5.0"
  resolved "https://registry.npm.taobao.org/memory-fs/download/memory-fs-0.5.0.tgz"
  integrity sha1-MkwBKIuIZSlm0WHbd4OHIIRajjw=
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

memory-pager@^1.0.2:
  version "1.5.0"
  resolved "https://registry.npm.taobao.org/memory-pager/download/memory-pager-1.5.0.tgz"
  integrity sha1-2HUWVdItOEaCdByXLyw9bfo+ZrU=

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/merge-descriptors/download/merge-descriptors-1.0.1.tgz"
  integrity sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/merge-stream/download/merge-stream-2.0.0.tgz"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.3.0:
  version "1.4.1"
  resolved "https://registry.npm.taobao.org/merge2/download/merge2-1.4.1.tgz"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

methods@^1.1.2, methods@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npm.taobao.org/methods/download/methods-1.1.2.tgz"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromatch@^3.1.4:
  version "3.1.10"
  resolved "https://registry.npm.taobao.org/micromatch/download/micromatch-3.1.10.tgz?cache=0&sync_timestamp=1618054787196&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmicromatch%2Fdownload%2Fmicromatch-3.1.10.tgz"
  integrity sha1-cIWbyVyYQJUvNZoGij/En57PrCM=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

micromatch@^4.0.0, micromatch@^4.0.2, micromatch@^4.0.4:
  version "4.0.4"
  resolved "https://registry.npm.taobao.org/micromatch/download/micromatch-4.0.4.tgz?cache=0&sync_timestamp=1618054787196&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmicromatch%2Fdownload%2Fmicromatch-4.0.4.tgz"
  integrity sha1-iW1Rnf6dsl/OlM63pQCRm/iB6/k=
  dependencies:
    braces "^3.0.1"
    picomatch "^2.2.3"

mime-db@1.49.0:
  version "1.49.0"
  resolved "https://registry.nlark.com/mime-db/download/mime-db-1.49.0.tgz"
  integrity sha1-89/eYMmenPO8lwHWh3ePU3ABy+0=

mime-types@^2.1.12, mime-types@^2.1.27, mime-types@~2.1.19, mime-types@~2.1.24:
  version "2.1.32"
  resolved "https://registry.nlark.com/mime-types/download/mime-types-2.1.32.tgz?cache=0&sync_timestamp=1627407819001&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fmime-types%2Fdownload%2Fmime-types-2.1.32.tgz"
  integrity sha1-HQDonn3n/gIAjbYQAdngKFJnD9U=
  dependencies:
    mime-db "1.49.0"

mime@1.6.0:
  version "1.6.0"
  resolved "https://registry.nlark.com/mime/download/mime-1.6.0.tgz"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mime@^2.4.6:
  version "2.5.2"
  resolved "https://registry.nlark.com/mime/download/mime-2.5.2.tgz"
  integrity sha1-bj3GzCuVEGQ4MOXxnVy3U9pe6r4=

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/mimic-fn/download/mimic-fn-2.1.0.tgz"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

minimatch@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npm.taobao.org/minimatch/download/minimatch-3.0.4.tgz"
  integrity sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=
  dependencies:
    brace-expansion "^1.1.7"

minimist@1.2.5, minimist@^1.1.1, minimist@^1.2.0, minimist@^1.2.5:
  version "1.2.5"
  resolved "https://registry.nlark.com/minimist/download/minimist-1.2.5.tgz?cache=0&sync_timestamp=1618847003091&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fminimist%2Fdownload%2Fminimist-1.2.5.tgz"
  integrity sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=

minipass@^2.6.0, minipass@^2.8.6, minipass@^2.9.0:
  version "2.9.0"
  resolved "https://registry.npm.taobao.org/minipass/download/minipass-2.9.0.tgz"
  integrity sha1-5xN2Ln0+Mv7YAxFc+T4EvKn8yaY=
  dependencies:
    safe-buffer "^5.1.2"
    yallist "^3.0.0"

minizlib@^1.2.1:
  version "1.3.3"
  resolved "https://registry.npm.taobao.org/minizlib/download/minizlib-1.3.3.tgz"
  integrity sha1-IpDeloGKNMKVUcio0wEha9Zahh0=
  dependencies:
    minipass "^2.9.0"

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "https://registry.nlark.com/mixin-deep/download/mixin-deep-1.3.2.tgz"
  integrity sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mkdirp@1.x, mkdirp@^1.0.4:
  version "1.0.4"
  resolved "https://registry.nlark.com/mkdirp/download/mkdirp-1.0.4.tgz"
  integrity sha1-PrXtYmInVteaXw4qIh3+utdcL34=

"mkdirp@>=0.5 0", mkdirp@^0.5.0, mkdirp@^0.5.1:
  version "0.5.5"
  resolved "https://registry.nlark.com/mkdirp/download/mkdirp-0.5.5.tgz"
  integrity sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=
  dependencies:
    minimist "^1.2.5"

mockjs@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/mockjs/download/mockjs-1.1.0.tgz"
  integrity sha1-5qDDeOkZBtuv8gkRzAJzs8fXWwY=
  dependencies:
    commander "*"

moment-timezone@^0.5.38:
  version "0.5.38"
  resolved "https://registry.npmmirror.com/moment-timezone/-/moment-timezone-0.5.38.tgz"
  integrity sha512-nMIrzGah4+oYZPflDvLZUgoVUO4fvAqHstvG3xAUnMolWncuAiLDWNnJZj6EwJGMGfb1ZcuTFE6GI3hNOVWI/Q==
  dependencies:
    moment ">= 2.9.0"

"moment@>= 2.9.0", moment@^2.29.1:
  version "2.29.1"
  resolved "https://registry.npm.taobao.org/moment/download/moment-2.29.1.tgz?cache=0&sync_timestamp=1601983320283&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmoment%2Fdownload%2Fmoment-2.29.1.tgz"
  integrity sha1-sr52n6MZQL6e7qZGnAdeNQBvo9M=

mongodb@3.6.10:
  version "3.6.10"
  resolved "https://registry.nlark.com/mongodb/download/mongodb-3.6.10.tgz?cache=0&sync_timestamp=1628181636832&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fmongodb%2Fdownload%2Fmongodb-3.6.10.tgz"
  integrity sha1-8Q6ZARPIaxlcivBZm5s6kHSLbuQ=
  dependencies:
    bl "^2.2.1"
    bson "^1.1.4"
    denque "^1.4.1"
    optional-require "^1.0.3"
    safe-buffer "^5.1.2"
  optionalDependencies:
    saslprep "^1.0.0"

mongoose-legacy-pluralize@1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/mongoose-legacy-pluralize/download/mongoose-legacy-pluralize-1.0.2.tgz"
  integrity sha1-O6n5H6UHtRhtOZ+0CFS/8Y+1Y+Q=

mongoose@*, mongoose@^5.12.12:
  version "5.13.5"
  resolved "https://registry.nlark.com/mongoose/download/mongoose-5.13.5.tgz"
  integrity sha1-nIwLRtNRFt1OpHaD36g2E35HUkM=
  dependencies:
    "@types/mongodb" "^3.5.27"
    bson "^1.1.4"
    kareem "2.3.2"
    mongodb "3.6.10"
    mongoose-legacy-pluralize "1.0.2"
    mpath "0.8.3"
    mquery "3.2.5"
    ms "2.1.2"
    optional-require "1.0.x"
    regexp-clone "1.0.0"
    safe-buffer "5.2.1"
    sift "13.5.2"
    sliced "1.0.1"

mpath@0.8.3:
  version "0.8.3"
  resolved "https://registry.npm.taobao.org/mpath/download/mpath-0.8.3.tgz?cache=0&sync_timestamp=1609342170967&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmpath%2Fdownload%2Fmpath-0.8.3.tgz"
  integrity sha1-gorA0Yf39CZ0g510khlwl5q73Y8=

mquery@3.2.5:
  version "3.2.5"
  resolved "https://registry.npm.taobao.org/mquery/download/mquery-3.2.5.tgz"
  integrity sha1-jyMFYy5LsZf2j2DAz/ohqvQGDFE=
  dependencies:
    bluebird "3.5.1"
    debug "3.1.0"
    regexp-clone "^1.0.0"
    safe-buffer "5.1.2"
    sliced "1.0.1"

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/ms/download/ms-2.0.0.tgz?cache=0&sync_timestamp=1607433872491&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fms%2Fdownload%2Fms-2.0.0.tgz"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/ms/download/ms-2.1.1.tgz?cache=0&sync_timestamp=1607433872491&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fms%2Fdownload%2Fms-2.1.1.tgz"
  integrity sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo=

ms@2.1.2:
  version "2.1.2"
  resolved "https://registry.npm.taobao.org/ms/download/ms-2.1.2.tgz?cache=0&sync_timestamp=1607433872491&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fms%2Fdownload%2Fms-2.1.2.tgz"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

ms@^2.1.1:
  version "2.1.3"
  resolved "https://registry.npm.taobao.org/ms/download/ms-2.1.3.tgz?cache=0&sync_timestamp=1607433872491&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fms%2Fdownload%2Fms-2.1.3.tgz"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

multer@1.4.2:
  version "1.4.2"
  resolved "https://registry.nlark.com/multer/download/multer-1.4.2.tgz"
  integrity sha1-Lx9NEtuu66dMs35iPyNL9NPSBXo=
  dependencies:
    append-field "^1.0.0"
    busboy "^0.2.11"
    concat-stream "^1.5.2"
    mkdirp "^0.5.1"
    object-assign "^4.1.1"
    on-finished "^2.3.0"
    type-is "^1.6.4"
    xtend "^4.0.0"

mute-stream@0.0.8:
  version "0.0.8"
  resolved "https://registry.nlark.com/mute-stream/download/mute-stream-0.0.8.tgz"
  integrity sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=

mz@^2.4.0:
  version "2.7.0"
  resolved "https://registry.npm.taobao.org/mz/download/mz-2.7.0.tgz"
  integrity sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nan@^2.14.1, nan@^2.15.0:
  version "2.19.0"
  resolved "https://registry.npmmirror.com/nan/-/nan-2.19.0.tgz#bb58122ad55a6c5bc973303908d5b16cfdd5a8c0"
  integrity sha512-nO1xXxfh/RWNxfd/XPfbIfFk5vgLsAxUR9y5O0cHMJu/AW9U95JLXqthYHjEp+8gQ5p96K9jUp8nbVOxCdRbtw==

nanomatch@^1.2.9:
  version "1.2.13"
  resolved "https://registry.nlark.com/nanomatch/download/nanomatch-1.2.13.tgz"
  integrity sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npm.taobao.org/natural-compare/download/natural-compare-1.4.0.tgz"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

needle@^2.2.1:
  version "2.8.0"
  resolved "https://registry.nlark.com/needle/download/needle-2.8.0.tgz?cache=0&sync_timestamp=1625636253830&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fneedle%2Fdownload%2Fneedle-2.8.0.tgz"
  integrity sha1-HI75waLCncweg9c4Cde8aByAoEg=
  dependencies:
    debug "^3.2.6"
    iconv-lite "^0.4.4"
    sax "^1.2.4"

negotiator@0.6.2:
  version "0.6.2"
  resolved "https://registry.nlark.com/negotiator/download/negotiator-0.6.2.tgz"
  integrity sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs=

neo-async@^2.6.2:
  version "2.6.2"
  resolved "https://registry.nlark.com/neo-async/download/neo-async-2.6.2.tgz"
  integrity sha1-tKr7k+OustgXTKU88WOrfXMIMF8=

nestjs-mongoose-crud@^2.1.2:
  version "2.1.2"
  resolved "https://registry.nlark.com/nestjs-mongoose-crud/download/nestjs-mongoose-crud-2.1.2.tgz"
  integrity sha1-1tCEz8vcVVp89CS6JT6cSBvnYzU=

nestjs-typegoose@^7.1.38:
  version "7.1.38"
  resolved "https://registry.nlark.com/nestjs-typegoose/download/nestjs-typegoose-7.1.38.tgz"
  integrity sha1-yFzO7w3EwxRTSO64xKp1SbkMiKw=
  dependencies:
    is-class "^0.0.9"
    reflect-metadata "^0.1.13"

nice-try@^1.0.4:
  version "1.0.5"
  resolved "https://registry.nlark.com/nice-try/download/nice-try-1.0.5.tgz"
  integrity sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=

node-addon-api@^3.0.0:
  version "3.2.1"
  resolved "https://registry.nlark.com/node-addon-api/download/node-addon-api-3.2.1.tgz"
  integrity sha1-gTJeCiEXeJwBKNq2Xn448HzroWE=

node-emoji@1.10.0:
  version "1.10.0"
  resolved "https://registry.npm.taobao.org/node-emoji/download/node-emoji-1.10.0.tgz"
  integrity sha1-iIar0l2ce7YYAqZYUj0fjSqJsto=
  dependencies:
    lodash.toarray "^4.4.0"

node-fetch@^2.6.1:
  version "2.6.1"
  resolved "https://registry.nlark.com/node-fetch/download/node-fetch-2.6.1.tgz?cache=0&sync_timestamp=1626684812087&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fnode-fetch%2Fdownload%2Fnode-fetch-2.6.1.tgz"
  integrity sha1-BFvTI2Mfdu0uK1VXM5RBa2OaAFI=

node-gyp@3.x:
  version "3.8.0"
  resolved "https://registry.nlark.com/node-gyp/download/node-gyp-3.8.0.tgz?cache=0&sync_timestamp=1622168102506&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fnode-gyp%2Fdownload%2Fnode-gyp-3.8.0.tgz"
  integrity sha1-VAMEJhwzDoDQ1e3OJTpoyzlkIYw=
  dependencies:
    fstream "^1.0.0"
    glob "^7.0.3"
    graceful-fs "^4.1.2"
    mkdirp "^0.5.0"
    nopt "2 || 3"
    npmlog "0 || 1 || 2 || 3 || 4"
    osenv "0"
    request "^2.87.0"
    rimraf "2"
    semver "~5.3.0"
    tar "^2.0.0"
    which "1"

node-int64@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npm.taobao.org/node-int64/download/node-int64-0.4.0.tgz"
  integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=

node-modules-regexp@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/node-modules-regexp/download/node-modules-regexp-1.0.0.tgz"
  integrity sha1-jZ2+KJZKSsVxLpExZCEHxx6Q7EA=

node-notifier@^8.0.0:
  version "8.0.2"
  resolved "https://registry.nlark.com/node-notifier/download/node-notifier-8.0.2.tgz?cache=0&sync_timestamp=1621962189467&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fnode-notifier%2Fdownload%2Fnode-notifier-8.0.2.tgz"
  integrity sha1-8xZ6OO8NLIqGaoPjGMG6Dv63AsU=
  dependencies:
    growly "^1.3.0"
    is-wsl "^2.2.0"
    semver "^7.3.2"
    shellwords "^0.1.1"
    uuid "^8.3.0"
    which "^2.0.2"

node-pre-gyp@^0.11.0:
  version "0.11.0"
  resolved "https://registry.nlark.com/node-pre-gyp/download/node-pre-gyp-0.11.0.tgz"
  integrity sha1-2x8zIVJy9pLNOPAyOOPptHxd0FQ=
  dependencies:
    detect-libc "^1.0.2"
    mkdirp "^0.5.1"
    needle "^2.2.1"
    nopt "^4.0.1"
    npm-packlist "^1.1.6"
    npmlog "^4.0.2"
    rc "^1.2.7"
    rimraf "^2.6.1"
    semver "^5.3.0"
    tar "^4"

node-releases@^1.1.73:
  version "1.1.73"
  resolved "https://registry.nlark.com/node-releases/download/node-releases-1.1.73.tgz"
  integrity sha1-3U6B3dUnf/hGuAtSu0DEnt96eyA=

node-ssh@^12.0.4:
  version "12.0.4"
  resolved "https://registry.npmmirror.com/node-ssh/-/node-ssh-12.0.4.tgz"
  integrity sha512-5M3FBeAWjEpAQvVakQde6CeviEoEiYb6IjJL9mrMen9at63GAv0Q5vOFHFP+SM1Y7pTN3EBvJ/I+oxn2Lpydbw==
  dependencies:
    is-stream "^2.0.0"
    make-dir "^3.1.0"
    sb-promise-queue "^2.1.0"
    sb-scandir "^3.1.0"
    shell-escape "^0.2.0"
    ssh2 "^1.5.0"

"nopt@2 || 3":
  version "3.0.6"
  resolved "https://registry.npm.taobao.org/nopt/download/nopt-3.0.6.tgz?cache=0&sync_timestamp=1597649942437&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnopt%2Fdownload%2Fnopt-3.0.6.tgz"
  integrity sha1-xkZdvwirzU2zWTF/eaxopkayj/k=
  dependencies:
    abbrev "1"

nopt@^4.0.1:
  version "4.0.3"
  resolved "https://registry.npm.taobao.org/nopt/download/nopt-4.0.3.tgz?cache=0&sync_timestamp=1597649942437&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnopt%2Fdownload%2Fnopt-4.0.3.tgz"
  integrity sha1-o3XK2dAv2SEnjZVMIlTVqlfhXkg=
  dependencies:
    abbrev "1"
    osenv "^0.1.4"

normalize-package-data@^2.5.0:
  version "2.5.0"
  resolved "https://registry.nlark.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^2.1.1:
  version "2.1.1"
  resolved "https://registry.nlark.com/normalize-path/download/normalize-path-2.1.1.tgz"
  integrity sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=
  dependencies:
    remove-trailing-separator "^1.0.1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/normalize-path/download/normalize-path-3.0.0.tgz"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

npm-bundled@^1.0.1:
  version "1.1.2"
  resolved "https://registry.nlark.com/npm-bundled/download/npm-bundled-1.1.2.tgz"
  integrity sha1-lEx4eJvXOQNbcLqiylzDK42GC8E=
  dependencies:
    npm-normalize-package-bin "^1.0.1"

npm-normalize-package-bin@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/npm-normalize-package-bin/download/npm-normalize-package-bin-1.0.1.tgz"
  integrity sha1-bnmkHyP9I1wGIyGCKNp9nCO49uI=

npm-packlist@^1.1.6:
  version "1.4.8"
  resolved "https://registry.nlark.com/npm-packlist/download/npm-packlist-1.4.8.tgz?cache=0&sync_timestamp=1620345779424&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fnpm-packlist%2Fdownload%2Fnpm-packlist-1.4.8.tgz"
  integrity sha1-Vu5swTW5+YrT1Rwcldoiu7my7z4=
  dependencies:
    ignore-walk "^3.0.1"
    npm-bundled "^1.0.1"
    npm-normalize-package-bin "^1.0.1"

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "https://registry.nlark.com/npm-run-path/download/npm-run-path-2.0.2.tgz"
  integrity sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=
  dependencies:
    path-key "^2.0.0"

npm-run-path@^4.0.0:
  version "4.0.1"
  resolved "https://registry.nlark.com/npm-run-path/download/npm-run-path-4.0.1.tgz"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

"npmlog@0 || 1 || 2 || 3 || 4", npmlog@^4.0.2:
  version "4.1.2"
  resolved "https://registry.nlark.com/npmlog/download/npmlog-4.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fnpmlog%2Fdownload%2Fnpmlog-4.1.2.tgz"
  integrity sha1-CKfyqL9zRgR3mp76StXMcXq7lUs=
  dependencies:
    are-we-there-yet "~1.1.2"
    console-control-strings "~1.1.0"
    gauge "~2.7.3"
    set-blocking "~2.0.0"

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/number-is-nan/download/number-is-nan-1.0.1.tgz?cache=0&sync_timestamp=1617776177506&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnumber-is-nan%2Fdownload%2Fnumber-is-nan-1.0.1.tgz"
  integrity sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=

nwsapi@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/nwsapi/download/nwsapi-2.2.0.tgz"
  integrity sha1-IEh5qePQaP8qVROcLHcngGgaOLc=

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "https://registry.nlark.com/oauth-sign/download/oauth-sign-0.9.0.tgz"
  integrity sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=

object-assign@^4, object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://registry.nlark.com/object-assign/download/object-assign-4.1.1.tgz"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-copy@^0.1.0:
  version "0.1.0"
  resolved "https://registry.nlark.com/object-copy/download/object-copy-0.1.0.tgz"
  integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-hash@2.1.1:
  version "2.1.1"
  resolved "https://registry.nlark.com/object-hash/download/object-hash-2.1.1.tgz"
  integrity sha1-lEfQJ5tPz4DP8yWb9modxzr6vgk=

object-inspect@^1.9.0:
  version "1.11.0"
  resolved "https://registry.nlark.com/object-inspect/download/object-inspect-1.11.0.tgz?cache=0&sync_timestamp=1626120241132&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fobject-inspect%2Fdownload%2Fobject-inspect-1.11.0.tgz"
  integrity sha1-nc6xRs7dQUig2eUauI00z1CZIrE=

object-visit@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/object-visit/download/object-visit-1.0.1.tgz"
  integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
  dependencies:
    isobject "^3.0.0"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npm.taobao.org/object.pick/download/object.pick-1.3.0.tgz"
  integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
  dependencies:
    isobject "^3.0.1"

on-finished@^2.3.0, on-finished@~2.3.0:
  version "2.3.0"
  resolved "https://registry.nlark.com/on-finished/download/on-finished-2.3.0.tgz"
  integrity sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=
  dependencies:
    ee-first "1.1.1"

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://registry.nlark.com/once/download/once-1.4.0.tgz"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^5.1.0:
  version "5.1.2"
  resolved "https://registry.nlark.com/onetime/download/onetime-5.1.2.tgz"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

optional-require@1.0.x:
  version "1.0.3"
  resolved "https://registry.nlark.com/optional-require/download/optional-require-1.0.3.tgz"
  integrity sha1-J1uOnfHcahetFVNpwkIqRA+Jywc=

optional-require@^1.0.3:
  version "1.1.6"
  resolved "https://registry.nlark.com/optional-require/download/optional-require-1.1.6.tgz"
  integrity sha1-iGaTwi0WbHk3cTfftgzWU/pJqWc=
  dependencies:
    require-at "^1.0.6"

optional@0.1.4:
  version "0.1.4"
  resolved "https://registry.npm.taobao.org/optional/download/optional-0.1.4.tgz"
  integrity sha1-zbGpvtxzfSAl9pDO61DgSURP1bM=

optionator@^0.8.1:
  version "0.8.3"
  resolved "https://registry.npm.taobao.org/optionator/download/optionator-0.8.3.tgz"
  integrity sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.6"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    word-wrap "~1.2.3"

optionator@^0.9.1:
  version "0.9.1"
  resolved "https://registry.npm.taobao.org/optionator/download/optionator-0.9.1.tgz"
  integrity sha1-TyNqY3Pa4FZqbUPhMmZ09QwpFJk=
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.3"

ora@5.3.0:
  version "5.3.0"
  resolved "https://registry.nlark.com/ora/download/ora-5.3.0.tgz"
  integrity sha1-+4MomdOhNy/nHIssU0u/50lhu28=
  dependencies:
    bl "^4.0.3"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-spinners "^2.5.0"
    is-interactive "^1.0.0"
    log-symbols "^4.0.0"
    strip-ansi "^6.0.0"
    wcwidth "^1.0.1"

ora@5.4.0:
  version "5.4.0"
  resolved "https://registry.nlark.com/ora/download/ora-5.4.0.tgz"
  integrity sha1-Qu2khVg1uc0U0zhkyXo8laP1a/Q=
  dependencies:
    bl "^4.1.0"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-spinners "^2.5.0"
    is-interactive "^1.0.0"
    is-unicode-supported "^0.1.0"
    log-symbols "^4.1.0"
    strip-ansi "^6.0.0"
    wcwidth "^1.0.1"

os-homedir@^1.0.0:
  version "1.0.2"
  resolved "https://registry.nlark.com/os-homedir/download/os-homedir-1.0.2.tgz"
  integrity sha1-/7xJiDNuDoM94MFox+8VISGqf7M=

os-name@4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/os-name/download/os-name-4.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fos-name%2Fdownload%2Fos-name-4.0.0.tgz"
  integrity sha1-bAXAnEHBWEjqdGWNEslgbw8oZZk=
  dependencies:
    macos-release "^2.2.0"
    windows-release "^4.0.0"

os-tmpdir@^1.0.0, os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/os-tmpdir/download/os-tmpdir-1.0.2.tgz"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

osenv@0, osenv@^0.1.4:
  version "0.1.5"
  resolved "https://registry.npm.taobao.org/osenv/download/osenv-0.1.5.tgz"
  integrity sha1-hc36+uso6Gd/QW4odZK18/SepBA=
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.0"

p-each-series@^2.1.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/p-each-series/download/p-each-series-2.2.0.tgz"
  integrity sha1-EFqwNXznKyAqiouUkzZyZXteKpo=

p-finally@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/p-finally/download/p-finally-1.0.0.tgz?cache=0&sync_timestamp=1617947695861&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fp-finally%2Fdownload%2Fp-finally-1.0.0.tgz"
  integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=

p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://registry.npm.taobao.org/p-limit/download/p-limit-2.3.0.tgz?cache=0&sync_timestamp=1606288352885&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fp-limit%2Fdownload%2Fp-limit-2.3.0.tgz"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-limit@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/p-limit/download/p-limit-3.1.0.tgz?cache=0&sync_timestamp=1606288352885&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fp-limit%2Fdownload%2Fp-limit-3.1.0.tgz"
  integrity sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://registry.nlark.com/p-locate/download/p-locate-4.1.0.tgz"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/p-try/download/p-try-2.2.0.tgz"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/parent-module/download/parent-module-1.0.1.tgz"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parent-require@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/parent-require/download/parent-require-1.0.0.tgz"
  integrity sha1-dGoWdjgIOoYLDu9nMssn7UbDKXc=

parse-json@^5.0.0:
  version "5.2.0"
  resolved "https://registry.nlark.com/parse-json/download/parse-json-5.2.0.tgz"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse5-htmlparser2-tree-adapter@^6.0.0:
  version "6.0.1"
  resolved "https://registry.nlark.com/parse5-htmlparser2-tree-adapter/download/parse5-htmlparser2-tree-adapter-6.0.1.tgz"
  integrity sha1-LN+a2CMyEUA3DU2/XT6Sx8jdxuY=
  dependencies:
    parse5 "^6.0.1"

parse5@6.0.1, parse5@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npm.taobao.org/parse5/download/parse5-6.0.1.tgz"
  integrity sha1-4aHAhcVps9wIMhGE8Zo5zCf3wws=

parse5@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npm.taobao.org/parse5/download/parse5-5.1.1.tgz"
  integrity sha1-9o5OW6GFKsLK3AD0VV//bCq7YXg=

parseqs@0.0.6:
  version "0.0.6"
  resolved "https://registry.npm.taobao.org/parseqs/download/parseqs-0.0.6.tgz"
  integrity sha1-jku1oZ0c3IRKCKyXTTTic6+mcNU=

parseuri@0.0.6:
  version "0.0.6"
  resolved "https://registry.npm.taobao.org/parseuri/download/parseuri-0.0.6.tgz"
  integrity sha1-4Ulugp46wv9H85pN0ESzKCPEolo=

parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://registry.nlark.com/parseurl/download/parseurl-1.3.3.tgz"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npm.taobao.org/pascalcase/download/pascalcase-0.1.1.tgz"
  integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=

passport-jwt@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/passport-jwt/-/passport-jwt-4.0.0.tgz"
  integrity sha512-BwC0n2GP/1hMVjR4QpnvqA61TxenUMlmfNjYNgK0ZAs0HK4SOQkHcSv4L328blNTLtHq7DbmvyNJiH+bn6C5Mg==
  dependencies:
    jsonwebtoken "^8.2.0"
    passport-strategy "^1.0.0"

passport-local@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/passport-local/-/passport-local-1.0.0.tgz"
  integrity sha512-9wCE6qKznvf9mQYYbgJ3sVOHmCWoUNMVFoZzNoznmISbhnNNPhN9xfY3sLmScHMetEJeoY7CXwfhCe7argfQow==
  dependencies:
    passport-strategy "1.x.x"

passport-strategy@1.x.x, passport-strategy@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/passport-strategy/download/passport-strategy-1.0.0.tgz"
  integrity sha1-tVOaqPwiWj0a0XlHbd8ja0QPUuQ=

passport@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmmirror.com/passport/-/passport-0.4.1.tgz"
  integrity sha512-IxXgZZs8d7uFSt3eqNjM9NQ3g3uQCW5avD8mRNoXV99Yig50vjuaez6dQK2qC0kVWPRTujxY0dWgGfT09adjYg==
  dependencies:
    passport-strategy "1.x.x"
    pause "0.0.1"

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/path-exists/download/path-exists-4.0.0.tgz"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/path-key/download/path-key-2.0.1.tgz"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.nlark.com/path-key/download/path-key-3.1.1.tgz"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.6:
  version "1.0.7"
  resolved "https://registry.nlark.com/path-parse/download/path-parse-1.0.7.tgz"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://registry.npm.taobao.org/path-to-regexp/download/path-to-regexp-0.1.7.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-to-regexp%2Fdownload%2Fpath-to-regexp-0.1.7.tgz"
  integrity sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=

path-to-regexp@3.2.0:
  version "3.2.0"
  resolved "https://registry.npm.taobao.org/path-to-regexp/download/path-to-regexp-3.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-to-regexp%2Fdownload%2Fpath-to-regexp-3.2.0.tgz"
  integrity sha1-+nh37LxJXGAZB1YiIkU8Q8wgSl8=

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/path-type/download/path-type-4.0.0.tgz"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

pause@0.0.1:
  version "0.0.1"
  resolved "https://registry.npm.taobao.org/pause/download/pause-0.0.1.tgz"
  integrity sha1-HUCLP9t2kjuVQ9lvtMnf1TXZy10=

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/performance-now/download/performance-now-2.1.0.tgz"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.2.3:
  version "2.3.0"
  resolved "https://registry.nlark.com/picomatch/download/picomatch-2.3.0.tgz"
  integrity sha1-8fBh3o9qS/AiiS4tEoI0+5gwKXI=

pirates@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/pirates/download/pirates-4.0.1.tgz"
  integrity sha1-ZDqSyviUVm+RsrmG0sZpUKji+4c=
  dependencies:
    node-modules-regexp "^1.0.0"

pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "https://registry.nlark.com/pkg-dir/download/pkg-dir-4.2.0.tgz"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

pluralize@8.0.0:
  version "8.0.0"
  resolved "https://registry.nlark.com/pluralize/download/pluralize-8.0.0.tgz"
  integrity sha1-Gm+hajjRKhkB4DIPoBcFHFOc47E=

pngjs@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/pngjs/-/pngjs-5.0.0.tgz"
  integrity sha512-40QW5YalBNfQo5yRYmiw7Yz6TKKVr3h6970B2YE+3fQpsWcrbj1PzJgxeJ19DRQjhMbKPIuMY8rFaXc8moolVw==

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "https://registry.nlark.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz"
  integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "https://registry.nlark.com/prelude-ls/download/prelude-ls-1.2.1.tgz"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/prelude-ls/download/prelude-ls-1.1.2.tgz"
  integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/prettier-linter-helpers/download/prettier-linter-helpers-1.0.0.tgz"
  integrity sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=
  dependencies:
    fast-diff "^1.1.2"

prettier@^2.2.1:
  version "2.3.2"
  resolved "https://registry.nlark.com/prettier/download/prettier-2.3.2.tgz?cache=0&sync_timestamp=1624696193562&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fprettier%2Fdownload%2Fprettier-2.3.2.tgz"
  integrity sha1-7ygKBewlNxLkhiM9tcbyNEHnNC0=

pretty-format@^26.0.0, pretty-format@^26.6.2:
  version "26.6.2"
  resolved "https://registry.nlark.com/pretty-format/download/pretty-format-26.6.2.tgz"
  integrity sha1-41wnBfFMt/4v6U+geDRbREEg/JM=
  dependencies:
    "@jest/types" "^26.6.2"
    ansi-regex "^5.0.0"
    ansi-styles "^4.0.0"
    react-is "^17.0.1"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/process-nextick-args/download/process-nextick-args-2.0.1.tgz"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

progress@^2.0.0:
  version "2.0.3"
  resolved "https://registry.nlark.com/progress/download/progress-2.0.3.tgz"
  integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=

prompts@^2.0.1:
  version "2.4.1"
  resolved "https://registry.npm.taobao.org/prompts/download/prompts-2.4.1.tgz?cache=0&sync_timestamp=1617240041932&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fprompts%2Fdownload%2Fprompts-2.4.1.tgz"
  integrity sha1-vv07EZW6BS+f0v3opIbE6C7nf2E=
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

property-expr@^2.0.2:
  version "2.0.5"
  resolved "https://registry.npmmirror.com/property-expr/-/property-expr-2.0.5.tgz"
  integrity sha512-IJUkICM5dP5znhCckHSv30Q4b5/JA5enCtkRHYaOVOAocnH/1BQEYTC5NMfT3AVl/iXKdr3aqQbQn9DxyWknwA==

proxy-addr@~2.0.5:
  version "2.0.7"
  resolved "https://registry.nlark.com/proxy-addr/download/proxy-addr-2.0.7.tgz?cache=0&sync_timestamp=1622509170257&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fproxy-addr%2Fdownload%2Fproxy-addr-2.0.7.tgz"
  integrity sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

prr@~1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/prr/download/prr-1.0.1.tgz"
  integrity sha1-0/wRS6BplaRexok/SEzrHXj19HY=

psl@^1.1.28, psl@^1.1.33:
  version "1.8.0"
  resolved "https://registry.npm.taobao.org/psl/download/psl-1.8.0.tgz"
  integrity sha1-kyb4vPsBOtzABf3/BWrM4CDlHCQ=

pump@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/pump/download/pump-3.0.0.tgz"
  integrity sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode@^2.1.0, punycode@^2.1.1:
  version "2.1.1"
  resolved "https://registry.nlark.com/punycode/download/punycode-2.1.1.tgz"
  integrity sha1-tYsBCsQMIsVldhbI0sLALHv0eew=

qrcode@^1.5.0:
  version "1.5.0"
  resolved "https://registry.npmmirror.com/qrcode/-/qrcode-1.5.0.tgz"
  integrity sha512-9MgRpgVc+/+47dFvQeD6U2s0Z92EsKzcHogtum4QB+UNd025WOJSHvn/hjk9xmzj7Stj95CyUAs31mrjxliEsQ==
  dependencies:
    dijkstrajs "^1.0.1"
    encode-utf8 "^1.0.3"
    pngjs "^5.0.0"
    yargs "^15.3.1"

qs@6.7.0:
  version "6.7.0"
  resolved "https://registry.nlark.com/qs/download/qs-6.7.0.tgz"
  integrity sha1-QdwaAV49WB8WIXdr4xr7KHapsbw=

qs@^6.9.4:
  version "6.10.1"
  resolved "https://registry.nlark.com/qs/download/qs-6.10.1.tgz"
  integrity sha1-STFIL6jWR6Wqt5nFJx0hM7mB+2o=
  dependencies:
    side-channel "^1.0.4"

qs@~6.5.2:
  version "6.5.2"
  resolved "https://registry.nlark.com/qs/download/qs-6.5.2.tgz"
  integrity sha1-yzroBuh0BERYTvFUzo7pjUA/PjY=

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://registry.npm.taobao.org/queue-microtask/download/queue-microtask-1.2.3.tgz?cache=0&sync_timestamp=1616391510274&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fqueue-microtask%2Fdownload%2Fqueue-microtask-1.2.3.tgz"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/randombytes/download/randombytes-2.1.0.tgz"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://registry.npm.taobao.org/range-parser/download/range-parser-1.2.1.tgz"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

raw-body@2.4.0:
  version "2.4.0"
  resolved "https://registry.nlark.com/raw-body/download/raw-body-2.4.0.tgz"
  integrity sha1-oc5vucm8NWylLoklarWQWeE9AzI=
  dependencies:
    bytes "3.1.0"
    http-errors "1.7.2"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

rc@^1.2.7:
  version "1.2.8"
  resolved "https://registry.npm.taobao.org/rc/download/rc-1.2.8.tgz?cache=0&sync_timestamp=1593529723659&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frc%2Fdownload%2Frc-1.2.8.tgz"
  integrity sha1-zZJL9SAKB1uDwYjNa54hG3/A0+0=
  dependencies:
    deep-extend "^0.6.0"
    ini "~1.3.0"
    minimist "^1.2.0"
    strip-json-comments "~2.0.1"

react-is@^17.0.1:
  version "17.0.2"
  resolved "https://registry.nlark.com/react-is/download/react-is-17.0.2.tgz?cache=0&sync_timestamp=1628266625985&other_urls=https%3A%2F%2Fregistry.nlark.com%2Freact-is%2Fdownload%2Freact-is-17.0.2.tgz"
  integrity sha1-5pHUqOnHiTZWVVOas3J2Kw77VPA=

read-pkg-up@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npm.taobao.org/read-pkg-up/download/read-pkg-up-7.0.1.tgz"
  integrity sha1-86YTV1hFlzOuK5VjgFbhhU5+9Qc=
  dependencies:
    find-up "^4.1.0"
    read-pkg "^5.2.0"
    type-fest "^0.8.1"

read-pkg@^5.2.0:
  version "5.2.0"
  resolved "https://registry.nlark.com/read-pkg/download/read-pkg-5.2.0.tgz"
  integrity sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^2.5.0"
    parse-json "^5.0.0"
    type-fest "^0.6.0"

readable-stream@1.1.x:
  version "1.1.14"
  resolved "https://registry.npm.taobao.org/readable-stream/download/readable-stream-1.1.14.tgz"
  integrity sha1-fPTFTvZI44EwhMY23SB54WbAgdk=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readable-stream@^2.0.1, readable-stream@^2.0.6, readable-stream@^2.2.2, readable-stream@^2.3.5:
  version "2.3.7"
  resolved "https://registry.npm.taobao.org/readable-stream/download/readable-stream-2.3.7.tgz"
  integrity sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.4.0, readable-stream@^3.6.0:
  version "3.6.0"
  resolved "https://registry.npm.taobao.org/readable-stream/download/readable-stream-3.6.0.tgz"
  integrity sha1-M3u9o63AcGvT4CRCaihtS0sskZg=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@~3.5.0:
  version "3.5.0"
  resolved "https://registry.nlark.com/readdirp/download/readdirp-3.5.0.tgz"
  integrity sha1-m6dMAZsV02UnjS6Ru4xI17TULJ4=
  dependencies:
    picomatch "^2.2.1"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.nlark.com/readdirp/download/readdirp-3.6.0.tgz"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

rechoir@^0.6.2:
  version "0.6.2"
  resolved "https://registry.nlark.com/rechoir/download/rechoir-0.6.2.tgz?cache=0&sync_timestamp=1627101702123&other_urls=https%3A%2F%2Fregistry.nlark.com%2Frechoir%2Fdownload%2Frechoir-0.6.2.tgz"
  integrity sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q=
  dependencies:
    resolve "^1.1.6"

reflect-metadata@^0.1.13:
  version "0.1.13"
  resolved "https://registry.npm.taobao.org/reflect-metadata/download/reflect-metadata-0.1.13.tgz"
  integrity sha1-Z648pXyXKiqhZCsQ/jY/4y1J3Ag=

regenerator-runtime@^0.13.4:
  version "0.13.9"
  resolved "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.13.9.tgz"
  integrity sha512-p3VT+cOEgxFsRRA9X4lkI1E+k2/CtnKtU4gcxyaCUreilL/vqI6CdZ3wxVUx3UOUg+gnUOQQcRI7BmSI656MYA==

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/regex-not/download/regex-not-1.0.2.tgz"
  integrity sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexp-clone@1.0.0, regexp-clone@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/regexp-clone/download/regexp-clone-1.0.0.tgz"
  integrity sha1-Ii25Z2IydwViYLmSYmNUoEzpv2M=

regexpp@^3.1.0:
  version "3.2.0"
  resolved "https://registry.nlark.com/regexpp/download/regexpp-3.2.0.tgz?cache=0&sync_timestamp=1623668835507&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fregexpp%2Fdownload%2Fregexpp-3.2.0.tgz"
  integrity sha1-BCWido2PI7rXDKS5BGH6LxIT4bI=

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "https://registry.nlark.com/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz"
  integrity sha1-wkvOKig62tW8P1jg1IJJuSN52O8=

repeat-element@^1.1.2:
  version "1.1.4"
  resolved "https://registry.npm.taobao.org/repeat-element/download/repeat-element-1.1.4.tgz"
  integrity sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek=

repeat-string@^1.6.1:
  version "1.6.1"
  resolved "https://registry.npm.taobao.org/repeat-string/download/repeat-string-1.6.1.tgz"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

request-ip@^2.1.3:
  version "2.1.3"
  resolved "https://registry.npmmirror.com/request-ip/-/request-ip-2.1.3.tgz"
  integrity sha512-J3qdE/IhVM3BXkwMIVO4yFrvhJlU3H7JH16+6yHucadT4fePnR8dyh+vEs6FIx0S2x5TCt2ptiPfHcn0sqhbYQ==
  dependencies:
    is_js "^0.9.0"

request@^2.87.0:
  version "2.88.2"
  resolved "https://registry.npm.taobao.org/request/download/request-2.88.2.tgz"
  integrity sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.3"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.5.0"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-at@^1.0.6:
  version "1.0.6"
  resolved "https://registry.nlark.com/require-at/download/require-at-1.0.6.tgz?cache=0&sync_timestamp=1626971357009&other_urls=https%3A%2F%2Fregistry.nlark.com%2Frequire-at%2Fdownload%2Frequire-at-1.0.6.tgz"
  integrity sha1-nrfjxeAHJ/WkdEBwp/Vg1N5Pbmo=

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/require-directory/download/require-directory-2.1.1.tgz"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npm.taobao.org/require-from-string/download/require-from-string-2.0.2.tgz"
  integrity sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/require-main-filename/download/require-main-filename-2.0.0.tgz"
  integrity sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=

resolve-cwd@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/resolve-cwd/download/resolve-cwd-3.0.0.tgz"
  integrity sha1-DwB18bslRHZs9zumpuKt/ryxPy0=
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/resolve-from/download/resolve-from-4.0.0.tgz"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npm.taobao.org/resolve-from/download/resolve-from-5.0.0.tgz"
  integrity sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npm.taobao.org/resolve-url/download/resolve-url-0.2.1.tgz"
  integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=

resolve@^1.1.6, resolve@^1.10.0, resolve@^1.18.1:
  version "1.20.0"
  resolved "https://registry.nlark.com/resolve/download/resolve-1.20.0.tgz"
  integrity sha1-YpoBP7P3B1XW8LeTXMHCxTeLGXU=
  dependencies:
    is-core-module "^2.2.0"
    path-parse "^1.0.6"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/restore-cursor/download/restore-cursor-3.1.0.tgz"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "https://registry.nlark.com/ret/download/ret-0.1.15.tgz"
  integrity sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://registry.nlark.com/reusify/download/reusify-1.0.4.tgz"
  integrity sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=

rimraf@2, rimraf@^2.6.1:
  version "2.7.1"
  resolved "https://registry.npm.taobao.org/rimraf/download/rimraf-2.7.1.tgz"
  integrity sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=
  dependencies:
    glob "^7.1.3"

rimraf@3.0.2, rimraf@^3.0.0, rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npm.taobao.org/rimraf/download/rimraf-3.0.2.tgz"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

rsvp@^4.8.4:
  version "4.8.5"
  resolved "https://registry.npm.taobao.org/rsvp/download/rsvp-4.8.5.tgz"
  integrity sha1-yPFVMR0Wf2jyHhaN9x7FsIMRNzQ=

run-async@^2.4.0:
  version "2.4.1"
  resolved "https://registry.npm.taobao.org/run-async/download/run-async-2.4.1.tgz"
  integrity sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/run-parallel/download/run-parallel-1.2.0.tgz?cache=0&sync_timestamp=1612926037406&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frun-parallel%2Fdownload%2Frun-parallel-1.2.0.tgz"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

rxjs@6.6.3:
  version "6.6.3"
  resolved "https://registry.nlark.com/rxjs/download/rxjs-6.6.3.tgz"
  integrity sha1-jKhGNcTaqQDA05Z6buesYCce5VI=
  dependencies:
    tslib "^1.9.0"

rxjs@^6.6.0, rxjs@^6.6.6:
  version "6.6.7"
  resolved "https://registry.nlark.com/rxjs/download/rxjs-6.6.7.tgz"
  integrity sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk=
  dependencies:
    tslib "^1.9.0"

safe-buffer@5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://registry.nlark.com/safe-buffer/download/safe-buffer-5.1.2.tgz"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-buffer@5.2.1, safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.1, safe-buffer@^5.1.2:
  version "5.2.1"
  resolved "https://registry.nlark.com/safe-buffer/download/safe-buffer-5.2.1.tgz"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/safe-regex/download/safe-regex-1.1.0.tgz"
  integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
  dependencies:
    ret "~0.1.10"

"safer-buffer@>= 2.1.2 < 3", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "https://registry.nlark.com/safer-buffer/download/safer-buffer-2.1.2.tgz"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sane@^4.0.3:
  version "4.1.0"
  resolved "https://registry.nlark.com/sane/download/sane-4.1.0.tgz?cache=0&sync_timestamp=1624844585538&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsane%2Fdownload%2Fsane-4.1.0.tgz"
  integrity sha1-7Ygf2SJzOmxGG8GJ3CtsAG8//e0=
  dependencies:
    "@cnakazawa/watch" "^1.0.3"
    anymatch "^2.0.0"
    capture-exit "^2.0.0"
    exec-sh "^0.3.2"
    execa "^1.0.0"
    fb-watchman "^2.0.0"
    micromatch "^3.1.4"
    minimist "^1.1.1"
    walker "~1.0.5"

saslprep@^1.0.0:
  version "1.0.3"
  resolved "https://registry.nlark.com/saslprep/download/saslprep-1.0.3.tgz"
  integrity sha1-TAL5RrVs9UKX40e6EJPnrKxM8iY=
  dependencies:
    sparse-bitfield "^3.0.3"

sax@>=0.6.0, sax@^1.2.4:
  version "1.2.4"
  resolved "https://registry.nlark.com/sax/download/sax-1.2.4.tgz"
  integrity sha1-KBYjTiN4vdxOU1T6tcqold9xANk=

saxes@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npm.taobao.org/saxes/download/saxes-5.0.1.tgz"
  integrity sha1-7rq5U/o7dgjb6U5drbFciI+maW0=
  dependencies:
    xmlchars "^2.2.0"

sb-promise-queue@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/sb-promise-queue/-/sb-promise-queue-2.1.0.tgz"
  integrity sha512-zwq4YuP1FQFkGx2Q7GIkZYZ6PqWpV+bg0nIO1sJhWOyGyhqbj0MsTvK6lCFo5TQwX5pZr6SCQ75e8PCDCuNvkg==

sb-scandir@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/sb-scandir/-/sb-scandir-3.1.0.tgz"
  integrity sha512-70BVm2xz9jn94zSQdpvYrEG101/UV9TVGcfWr9T5iob3QhCK4lYXeculfBqPGFv3XTeKgx4dpWyYIDeZUqo4kg==
  dependencies:
    sb-promise-queue "^2.1.0"

schema-utils@2.7.0:
  version "2.7.0"
  resolved "https://registry.nlark.com/schema-utils/download/schema-utils-2.7.0.tgz?cache=0&sync_timestamp=1626694740261&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fschema-utils%2Fdownload%2Fschema-utils-2.7.0.tgz"
  integrity sha1-FxUfdtjq5n+793lgwzxnatn078c=
  dependencies:
    "@types/json-schema" "^7.0.4"
    ajv "^6.12.2"
    ajv-keywords "^3.4.1"

schema-utils@^3.0.0:
  version "3.1.1"
  resolved "https://registry.nlark.com/schema-utils/download/schema-utils-3.1.1.tgz?cache=0&sync_timestamp=1626694740261&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fschema-utils%2Fdownload%2Fschema-utils-3.1.1.tgz"
  integrity sha1-vHTEtraZXB2I92qLd76nIZ4MgoE=
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

"semver@2 || 3 || 4 || 5", semver@^5.3.0, semver@^5.5.0, semver@^5.6.0:
  version "5.7.1"
  resolved "https://registry.npm.taobao.org/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1616463641178&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz"
  integrity sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=

semver@7.x, semver@^7.2.1, semver@^7.3.2, semver@^7.3.4, semver@^7.3.5:
  version "7.3.5"
  resolved "https://registry.npm.taobao.org/semver/download/semver-7.3.5.tgz?cache=0&sync_timestamp=1616463641178&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-7.3.5.tgz"
  integrity sha1-C2Ich5NI2JmOSw5L6Us/EuYBjvc=
  dependencies:
    lru-cache "^6.0.0"

semver@^6.0.0, semver@^6.3.0:
  version "6.3.0"
  resolved "https://registry.npm.taobao.org/semver/download/semver-6.3.0.tgz?cache=0&sync_timestamp=1616463641178&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-6.3.0.tgz"
  integrity sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=

semver@~5.3.0:
  version "5.3.0"
  resolved "https://registry.npm.taobao.org/semver/download/semver-5.3.0.tgz?cache=0&sync_timestamp=1616463641178&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-5.3.0.tgz"
  integrity sha1-myzl094C0XxgEq0yaqa00M9U+U8=

send@0.17.1:
  version "0.17.1"
  resolved "https://registry.npm.taobao.org/send/download/send-0.17.1.tgz"
  integrity sha1-wdiwWfeQD3Rm3Uk4vcROEd2zdsg=
  dependencies:
    debug "2.6.9"
    depd "~1.1.2"
    destroy "~1.0.4"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "~1.7.2"
    mime "1.6.0"
    ms "2.1.1"
    on-finished "~2.3.0"
    range-parser "~1.2.1"
    statuses "~1.5.0"

serialize-javascript@^6.0.0:
  version "6.0.0"
  resolved "https://registry.nlark.com/serialize-javascript/download/serialize-javascript-6.0.0.tgz"
  integrity sha1-765diPRdeSQUHai1w6en5mP+/rg=
  dependencies:
    randombytes "^2.1.0"

serve-static@1.14.1:
  version "1.14.1"
  resolved "https://registry.nlark.com/serve-static/download/serve-static-1.14.1.tgz"
  integrity sha1-Zm5jbcTwEPfvKZcKiKZ0MgiYsvk=
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.17.1"

set-blocking@^2.0.0, set-blocking@~2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/set-blocking/download/set-blocking-2.0.0.tgz"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/set-value/download/set-value-2.0.1.tgz"
  integrity sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setprototypeof@1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/setprototypeof/download/setprototypeof-1.1.1.tgz"
  integrity sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM=

sha.js@^2.4.11:
  version "2.4.11"
  resolved "https://registry.npm.taobao.org/sha.js/download/sha.js-2.4.11.tgz"
  integrity sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/shebang-command/download/shebang-command-1.2.0.tgz"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/shebang-command/download/shebang-command-2.0.0.tgz"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/shebang-regex/download/shebang-regex-1.0.0.tgz"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/shebang-regex/download/shebang-regex-3.0.0.tgz"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shell-escape@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmmirror.com/shell-escape/-/shell-escape-0.2.0.tgz"
  integrity sha512-uRRBT2MfEOyxuECseCZd28jC1AJ8hmqqneWQ4VWUTgCAFvb3wKU1jLqj6egC4Exrr88ogg3dp+zroH4wJuaXzw==

shelljs@0.8.4:
  version "0.8.4"
  resolved "https://registry.npm.taobao.org/shelljs/download/shelljs-0.8.4.tgz"
  integrity sha1-3naE/ut2f4cWsyYHiooAh1iQ48I=
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

shellwords@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npm.taobao.org/shellwords/download/shellwords-0.1.1.tgz"
  integrity sha1-1rkYHBpI05cyTISHHvvPxz/AZUs=

side-channel@^1.0.4:
  version "1.0.4"
  resolved "https://registry.nlark.com/side-channel/download/side-channel-1.0.4.tgz"
  integrity sha1-785cj9wQTudRslxY1CkAEfpeos8=
  dependencies:
    call-bind "^1.0.0"
    get-intrinsic "^1.0.2"
    object-inspect "^1.9.0"

sift@13.5.2:
  version "13.5.2"
  resolved "https://registry.nlark.com/sift/download/sift-13.5.2.tgz"
  integrity sha1-JKcV4TxhewhhZs0EkX0gSlkcnaY=

signal-exit@^3.0.0, signal-exit@^3.0.2:
  version "3.0.3"
  resolved "https://registry.nlark.com/signal-exit/download/signal-exit-3.0.3.tgz"
  integrity sha1-oUEMLt2PB3sItOJTyOrPyvBXRhw=

sisteransi@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npm.taobao.org/sisteransi/download/sisteransi-1.0.5.tgz"
  integrity sha1-E01oEpd1ZDfMBcoBNw06elcQde0=

slash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/slash/download/slash-3.0.0.tgz"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

slice-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/slice-ansi/download/slice-ansi-4.0.0.tgz"
  integrity sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

sliced@1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/sliced/download/sliced-1.0.1.tgz"
  integrity sha1-CzpmK10Ewxd7GSa+qCsD+Dei70E=

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "https://registry.nlark.com/snapdragon-node/download/snapdragon-node-2.1.1.tgz"
  integrity sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "https://registry.nlark.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz"
  integrity sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "https://registry.nlark.com/snapdragon/download/snapdragon-0.8.2.tgz"
  integrity sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

socket.io-adapter@~1.1.0:
  version "1.1.2"
  resolved "https://registry.nlark.com/socket.io-adapter/download/socket.io-adapter-1.1.2.tgz?cache=0&sync_timestamp=1621465246751&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsocket.io-adapter%2Fdownload%2Fsocket.io-adapter-1.1.2.tgz"
  integrity sha1-qz8Nb2a4/H/KOVmrWZH4IiF4m+k=

socket.io-adapter@~2.3.1:
  version "2.3.1"
  resolved "https://registry.nlark.com/socket.io-adapter/download/socket.io-adapter-2.3.1.tgz?cache=0&sync_timestamp=1621465246751&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsocket.io-adapter%2Fdownload%2Fsocket.io-adapter-2.3.1.tgz"
  integrity sha1-pEJyDLCaSCPPuBKH3aH5tS1MzbI=

socket.io-client@2.4.0:
  version "2.4.0"
  resolved "https://registry.nlark.com/socket.io-client/download/socket.io-client-2.4.0.tgz?cache=0&sync_timestamp=1625912013847&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsocket.io-client%2Fdownload%2Fsocket.io-client-2.4.0.tgz"
  integrity sha1-qvtdWUo8VaNDVVYvyK6iLtkRmjU=
  dependencies:
    backo2 "1.0.2"
    component-bind "1.0.0"
    component-emitter "~1.3.0"
    debug "~3.1.0"
    engine.io-client "~3.5.0"
    has-binary2 "~1.0.2"
    indexof "0.0.1"
    parseqs "0.0.6"
    parseuri "0.0.6"
    socket.io-parser "~3.3.0"
    to-array "0.1.4"

socket.io-parser@~3.3.0:
  version "3.3.2"
  resolved "https://registry.npm.taobao.org/socket.io-parser/download/socket.io-parser-3.3.2.tgz"
  integrity sha1-74cgCdCtz3BPL76DAZGhR1KtULY=
  dependencies:
    component-emitter "~1.3.0"
    debug "~3.1.0"
    isarray "2.0.1"

socket.io-parser@~3.4.0:
  version "3.4.1"
  resolved "https://registry.npm.taobao.org/socket.io-parser/download/socket.io-parser-3.4.1.tgz"
  integrity sha1-sGr4ODApdYN+qy3JgAN9okBU1ko=
  dependencies:
    component-emitter "1.2.1"
    debug "~4.1.0"
    isarray "2.0.1"

socket.io-parser@~4.0.4:
  version "4.0.4"
  resolved "https://registry.npm.taobao.org/socket.io-parser/download/socket.io-parser-4.0.4.tgz"
  integrity sha1-nqIbDWFQjRgZbvBKLGuatjD0wrA=
  dependencies:
    "@types/component-emitter" "^1.2.10"
    component-emitter "~1.3.0"
    debug "~4.3.1"

socket.io@*:
  version "4.1.3"
  resolved "https://registry.nlark.com/socket.io/download/socket.io-4.1.3.tgz"
  integrity sha1-0RQyjvJ6sxuIlhF5KVnD+m1QJQA=
  dependencies:
    "@types/cookie" "^0.4.0"
    "@types/cors" "^2.8.10"
    "@types/node" ">=10.0.0"
    accepts "~1.3.4"
    base64id "~2.0.0"
    debug "~4.3.1"
    engine.io "~5.1.1"
    socket.io-adapter "~2.3.1"
    socket.io-parser "~4.0.4"

socket.io@2.4.1:
  version "2.4.1"
  resolved "https://registry.nlark.com/socket.io/download/socket.io-2.4.1.tgz"
  integrity sha1-la2GHJpSNp1/Gmis8NShsW2kUdI=
  dependencies:
    debug "~4.1.0"
    engine.io "~3.5.0"
    has-binary2 "~1.0.2"
    socket.io-adapter "~1.1.0"
    socket.io-client "2.4.0"
    socket.io-parser "~3.4.0"

source-list-map@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/source-list-map/download/source-list-map-2.0.1.tgz"
  integrity sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ=

source-map-resolve@^0.5.0:
  version "0.5.3"
  resolved "https://registry.npm.taobao.org/source-map-resolve/download/source-map-resolve-0.5.3.tgz"
  integrity sha1-GQhmvs51U+H48mei7oLGBrVQmho=
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@^0.5.17, source-map-support@^0.5.6, source-map-support@~0.5.19:
  version "0.5.19"
  resolved "https://registry.nlark.com/source-map-support/download/source-map-support-0.5.19.tgz?cache=0&sync_timestamp=1618847050054&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsource-map-support%2Fdownload%2Fsource-map-support-0.5.19.tgz"
  integrity sha1-qYti+G3K9PZzmWSMCFKRq56P7WE=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.1"
  resolved "https://registry.npm.taobao.org/source-map-url/download/source-map-url-0.4.1.tgz?cache=0&sync_timestamp=1612211015749&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsource-map-url%2Fdownload%2Fsource-map-url-0.4.1.tgz"
  integrity sha1-CvZmBadFpaL5HPG7+KevvCg97FY=

source-map@0.7.3, source-map@^0.7.3, source-map@~0.7.2:
  version "0.7.3"
  resolved "https://registry.nlark.com/source-map/download/source-map-0.7.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsource-map%2Fdownload%2Fsource-map-0.7.3.tgz"
  integrity sha1-UwL4FpAxc1ImVECS5kmB91F1A4M=

source-map@^0.5.0, source-map@^0.5.6:
  version "0.5.7"
  resolved "https://registry.nlark.com/source-map/download/source-map-0.5.7.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsource-map%2Fdownload%2Fsource-map-0.5.7.tgz"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.1:
  version "0.6.1"
  resolved "https://registry.nlark.com/source-map/download/source-map-0.6.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsource-map%2Fdownload%2Fsource-map-0.6.1.tgz"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

sourcemap-codec@^1.4.4:
  version "1.4.8"
  resolved "https://registry.npm.taobao.org/sourcemap-codec/download/sourcemap-codec-1.4.8.tgz"
  integrity sha1-6oBL2UhXQC5pktBaOO8a41qatMQ=

sparse-bitfield@^3.0.3:
  version "3.0.3"
  resolved "https://registry.nlark.com/sparse-bitfield/download/sparse-bitfield-3.0.3.tgz"
  integrity sha1-/0rm5oZWBWuks+eSqzM004JzyhE=
  dependencies:
    memory-pager "^1.0.2"

spdx-correct@^3.0.0:
  version "3.1.1"
  resolved "https://registry.npm.taobao.org/spdx-correct/download/spdx-correct-3.1.1.tgz"
  integrity sha1-3s6BrJweZxPl99G28X1Gj6U9iak=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "https://registry.npm.taobao.org/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz"
  integrity sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npm.taobao.org/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz"
  integrity sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.9"
  resolved "https://registry.nlark.com/spdx-license-ids/download/spdx-license-ids-3.0.9.tgz?cache=0&sync_timestamp=1621652583280&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fspdx-license-ids%2Fdownload%2Fspdx-license-ids-3.0.9.tgz"
  integrity sha1-illRNd75WSvaaXCUdPHL7qfCRn8=

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/split-string/download/split-string-3.1.0.tgz"
  integrity sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=
  dependencies:
    extend-shallow "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/sprintf-js/download/sprintf-js-1.0.3.tgz"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

sqlite3@^5.0.2:
  version "5.0.2"
  resolved "https://registry.npmmirror.com/sqlite3/-/sqlite3-5.0.2.tgz"
  integrity sha512-1SdTNo+BVU211Xj1csWa8lV6KM0CtucDwRyA0VHl91wEH1Mgh7RxUpI4rVvG7OhHrzCSGaVyW5g8vKvlrk9DJA==
  dependencies:
    node-addon-api "^3.0.0"
    node-pre-gyp "^0.11.0"
  optionalDependencies:
    node-gyp "3.x"

ssf@~0.11.2:
  version "0.11.2"
  resolved "https://registry.npmmirror.com/ssf/-/ssf-0.11.2.tgz"
  integrity sha512-+idbmIXoYET47hH+d7dfm2epdOMUDjqcB4648sTZ+t2JwoyBFL/insLfB/racrDmsKB3diwsDA696pZMieAC5g==
  dependencies:
    frac "~1.1.2"

ssh2@^1.5.0:
  version "1.7.0"
  resolved "https://registry.npmmirror.com/ssh2/-/ssh2-1.7.0.tgz"
  integrity sha512-u1gdFfqKV1PTGR2szS5FImhFii40o+8FOUpg1M//iimNaS4BkTyUVfVdoydXS93M1SquOU02Z4KFhYDBNqQO+g==
  dependencies:
    asn1 "^0.2.4"
    bcrypt-pbkdf "^1.0.2"
  optionalDependencies:
    cpu-features "0.0.2"
    nan "^2.15.0"

sshpk@^1.7.0:
  version "1.16.1"
  resolved "https://registry.nlark.com/sshpk/download/sshpk-1.16.1.tgz"
  integrity sha1-+2YcC+8ps520B2nuOfpwCT1vaHc=
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

stack-utils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.nlark.com/stack-utils/download/stack-utils-2.0.3.tgz"
  integrity sha1-zV8DASb/EWt4zLPAJ/4wJxO2Enc=
  dependencies:
    escape-string-regexp "^2.0.0"

static-extend@^0.1.1:
  version "0.1.2"
  resolved "https://registry.nlark.com/static-extend/download/static-extend-0.1.2.tgz"
  integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

"statuses@>= 1.5.0 < 2", statuses@~1.5.0:
  version "1.5.0"
  resolved "https://registry.nlark.com/statuses/download/statuses-1.5.0.tgz"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

streamsearch@0.1.2:
  version "0.1.2"
  resolved "https://registry.npm.taobao.org/streamsearch/download/streamsearch-0.1.2.tgz"
  integrity sha1-gIudDlb8Jz2Am6VzOOkpkZoanxo=

string-length@^4.0.1:
  version "4.0.2"
  resolved "https://registry.nlark.com/string-length/download/string-length-4.0.2.tgz?cache=0&sync_timestamp=1622904918319&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstring-length%2Fdownload%2Fstring-length-4.0.2.tgz"
  integrity sha1-qKjce9XBqCubPIuH4SX2aHG25Xo=
  dependencies:
    char-regex "^1.0.2"
    strip-ansi "^6.0.0"

string-width@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/string-width/download/string-width-1.0.2.tgz"
  integrity sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

"string-width@^1.0.2 || 2", string-width@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/string-width/download/string-width-2.1.1.tgz"
  integrity sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^4.1.0, string-width@^4.2.0:
  version "4.2.2"
  resolved "https://registry.npm.taobao.org/string-width/download/string-width-4.2.2.tgz"
  integrity sha1-2v1PlVmnWFz7pSnGoKT3NIjr1MU=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.0"

string_decoder@^1.1.1, string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/string_decoder/download/string_decoder-1.1.1.tgz"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

string_decoder@~0.10.x:
  version "0.10.31"
  resolved "https://registry.nlark.com/string_decoder/download/string_decoder-0.10.31.tgz"
  integrity sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npm.taobao.org/strip-ansi/download/strip-ansi-3.0.1.tgz?cache=0&sync_timestamp=1618553299612&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-3.0.1.tgz"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/strip-ansi/download/strip-ansi-4.0.0.tgz?cache=0&sync_timestamp=1618553299612&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-4.0.0.tgz"
  integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npm.taobao.org/strip-ansi/download/strip-ansi-6.0.0.tgz?cache=0&sync_timestamp=1618553299612&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fstrip-ansi%2Fdownload%2Fstrip-ansi-6.0.0.tgz"
  integrity sha1-CxVx3XZpzNTz4G4U7x7tJiJa5TI=
  dependencies:
    ansi-regex "^5.0.0"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/strip-bom/download/strip-bom-3.0.0.tgz"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-bom@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/strip-bom/download/strip-bom-4.0.0.tgz"
  integrity sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg=

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/strip-eof/download/strip-eof-1.0.0.tgz"
  integrity sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz?cache=0&sync_timestamp=1620046435959&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fstrip-final-newline%2Fdownload%2Fstrip-final-newline-2.0.0.tgz"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

strip-json-comments@^3.1.0, strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npm.taobao.org/strip-json-comments/download/strip-json-comments-3.1.1.tgz"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

strip-json-comments@~2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/strip-json-comments/download/strip-json-comments-2.0.1.tgz"
  integrity sha1-PFMZQukIwml8DsNEhYwobHygpgo=

superagent@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npm.taobao.org/superagent/download/superagent-6.1.0.tgz"
  integrity sha1-CfCIB7xBEI7xZM+0vik869SA9KY=
  dependencies:
    component-emitter "^1.3.0"
    cookiejar "^2.1.2"
    debug "^4.1.1"
    fast-safe-stringify "^2.0.7"
    form-data "^3.0.0"
    formidable "^1.2.2"
    methods "^1.1.2"
    mime "^2.4.6"
    qs "^6.9.4"
    readable-stream "^3.6.0"
    semver "^7.3.2"

supertest@^6.1.3:
  version "6.1.4"
  resolved "https://registry.nlark.com/supertest/download/supertest-6.1.4.tgz?cache=0&sync_timestamp=1626841166051&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsupertest%2Fdownload%2Fsupertest-6.1.4.tgz"
  integrity sha1-6olTND4MoxboDpdbOTQJNPdU6wY=
  dependencies:
    methods "^1.1.2"
    superagent "^6.1.0"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/supports-color/download/supports-color-2.0.0.tgz?cache=0&sync_timestamp=1626703342506&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsupports-color%2Fdownload%2Fsupports-color-2.0.0.tgz"
  integrity sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://registry.nlark.com/supports-color/download/supports-color-5.5.0.tgz?cache=0&sync_timestamp=1626703342506&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsupports-color%2Fdownload%2Fsupports-color-5.5.0.tgz"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.0.0, supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://registry.nlark.com/supports-color/download/supports-color-7.2.0.tgz?cache=0&sync_timestamp=1626703342506&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsupports-color%2Fdownload%2Fsupports-color-7.2.0.tgz"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "https://registry.nlark.com/supports-color/download/supports-color-8.1.1.tgz?cache=0&sync_timestamp=1626703342506&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsupports-color%2Fdownload%2Fsupports-color-8.1.1.tgz"
  integrity sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=
  dependencies:
    has-flag "^4.0.0"

supports-hyperlinks@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/supports-hyperlinks/download/supports-hyperlinks-2.2.0.tgz"
  integrity sha1-T3e0JIh2WJF3S3DHm6vYf5vVlLs=
  dependencies:
    has-flag "^4.0.0"
    supports-color "^7.0.0"

swagger-ui-dist@^3.18.1:
  version "3.51.2"
  resolved "https://registry.nlark.com/swagger-ui-dist/download/swagger-ui-dist-3.51.2.tgz"
  integrity sha1-sPN37fkaf9H0Am9MzHXAcuphC3s=

swagger-ui-express@^4.1.6:
  version "4.1.6"
  resolved "https://registry.npm.taobao.org/swagger-ui-express/download/swagger-ui-express-4.1.6.tgz"
  integrity sha1-aCKUrz1ccPdKH6TWqbUDqe5V6oI=
  dependencies:
    swagger-ui-dist "^3.18.1"

symbol-observable@3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/symbol-observable/download/symbol-observable-3.0.0.tgz"
  integrity sha1-7qj2R4xlEBjgWQRCaDdcQIwVxTM=

symbol-tree@^3.2.4:
  version "3.2.4"
  resolved "https://registry.npm.taobao.org/symbol-tree/download/symbol-tree-3.2.4.tgz"
  integrity sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=

synchronous-promise@^2.0.13:
  version "2.0.15"
  resolved "https://registry.npmmirror.com/synchronous-promise/-/synchronous-promise-2.0.15.tgz"
  integrity sha512-k8uzYIkIVwmT+TcglpdN50pS2y1BDcUnBPK9iJeGu0Pl1lOI8pD6wtzgw91Pjpe+RxtTncw32tLxs/R0yNL2Mg==

table@^6.0.9:
  version "6.7.1"
  resolved "https://registry.nlark.com/table/download/table-6.7.1.tgz"
  integrity sha1-7gVZK3FDgxqMlPPO5qrkwczvM+I=
  dependencies:
    ajv "^8.0.1"
    lodash.clonedeep "^4.5.0"
    lodash.truncate "^4.4.2"
    slice-ansi "^4.0.0"
    string-width "^4.2.0"
    strip-ansi "^6.0.0"

tapable@^1.0.0:
  version "1.1.3"
  resolved "https://registry.npm.taobao.org/tapable/download/tapable-1.1.3.tgz?cache=0&sync_timestamp=1607088902003&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftapable%2Fdownload%2Ftapable-1.1.3.tgz"
  integrity sha1-ofzMBrWNth/XpF2i2kT186Pme6I=

tapable@^2.1.1, tapable@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/tapable/download/tapable-2.2.0.tgz?cache=0&sync_timestamp=1607088902003&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftapable%2Fdownload%2Ftapable-2.2.0.tgz"
  integrity sha1-XDc9KB2cZyhIIT0OA30cQWWrQms=

tar@^2.0.0:
  version "2.2.2"
  resolved "https://registry.nlark.com/tar/download/tar-2.2.2.tgz"
  integrity sha1-DKiEhWLHKZuLRG/2pNYM27I+3EA=
  dependencies:
    block-stream "*"
    fstream "^1.0.12"
    inherits "2"

tar@^4:
  version "4.4.15"
  resolved "https://registry.nlark.com/tar/download/tar-4.4.15.tgz"
  integrity sha1-PKztTznr1G3dpNYgPUhJOpGWl/g=
  dependencies:
    chownr "^1.1.1"
    fs-minipass "^1.2.5"
    minipass "^2.8.6"
    minizlib "^1.2.1"
    mkdirp "^0.5.0"
    safe-buffer "^5.1.2"
    yallist "^3.0.3"

terminal-link@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/terminal-link/download/terminal-link-2.1.1.tgz?cache=0&sync_timestamp=1618724348767&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fterminal-link%2Fdownload%2Fterminal-link-2.1.1.tgz"
  integrity sha1-FKZKJ6s8Dfkz6lRvulXy0HjtyZQ=
  dependencies:
    ansi-escapes "^4.2.1"
    supports-hyperlinks "^2.0.0"

terser-webpack-plugin@^5.1.1:
  version "5.1.4"
  resolved "https://registry.nlark.com/terser-webpack-plugin/download/terser-webpack-plugin-5.1.4.tgz?cache=0&sync_timestamp=1624624550780&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fterser-webpack-plugin%2Fdownload%2Fterser-webpack-plugin-5.1.4.tgz"
  integrity sha1-w2nPikeqmSK9DYqU/j09oRp2eKE=
  dependencies:
    jest-worker "^27.0.2"
    p-limit "^3.1.0"
    schema-utils "^3.0.0"
    serialize-javascript "^6.0.0"
    source-map "^0.6.1"
    terser "^5.7.0"

terser@^5.7.0:
  version "5.7.1"
  resolved "https://registry.nlark.com/terser/download/terser-5.7.1.tgz"
  integrity sha1-LcemEAm2a7Y4MFyyqCR2OxFr94Q=
  dependencies:
    commander "^2.20.0"
    source-map "~0.7.2"
    source-map-support "~0.5.19"

test-exclude@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npm.taobao.org/test-exclude/download/test-exclude-6.0.0.tgz"
  integrity sha1-BKhphmHYBepvopO2y55jrARO8V4=
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    glob "^7.1.4"
    minimatch "^3.0.4"

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://registry.nlark.com/text-table/download/text-table-0.2.0.tgz"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "https://registry.npm.taobao.org/thenify-all/download/thenify-all-1.6.0.tgz"
  integrity sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "https://registry.nlark.com/thenify/download/thenify-3.3.1.tgz"
  integrity sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=
  dependencies:
    any-promise "^1.0.0"

throat@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npm.taobao.org/throat/download/throat-5.0.0.tgz"
  integrity sha1-xRmSNYA6rRh1SmZ9ZZtecs4Wdks=

through@^2.3.6:
  version "2.3.8"
  resolved "https://registry.npm.taobao.org/through/download/through-2.3.8.tgz"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://registry.npm.taobao.org/tmp/download/tmp-0.0.33.tgz?cache=0&sync_timestamp=1592843137359&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftmp%2Fdownload%2Ftmp-0.0.33.tgz"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

tmpl@1.0.x:
  version "1.0.4"
  resolved "https://registry.npm.taobao.org/tmpl/download/tmpl-1.0.4.tgz"
  integrity sha1-I2QN17QtAEM5ERQIIOXPRA5SHdE=

to-array@0.1.4:
  version "0.1.4"
  resolved "https://registry.npm.taobao.org/to-array/download/to-array-0.1.4.tgz"
  integrity sha1-F+bBH3PdTz10zaek/zI46a2b+JA=

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/to-fast-properties/download/to-fast-properties-2.0.0.tgz"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "https://registry.nlark.com/to-object-path/download/to-object-path-0.3.0.tgz"
  integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "https://registry.nlark.com/to-regex-range/download/to-regex-range-2.1.1.tgz"
  integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.nlark.com/to-regex-range/download/to-regex-range-5.0.1.tgz"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "https://registry.nlark.com/to-regex/download/to-regex-3.0.2.tgz"
  integrity sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toidentifier@1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/toidentifier/download/toidentifier-1.0.0.tgz"
  integrity sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM=

toposort@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/toposort/-/toposort-2.0.2.tgz"
  integrity sha512-0a5EOkAUp8D4moMi2W8ZF8jcga7BgZd91O/yabJCFY8az+XSzeGyTKs0Aoo897iV1Nj6guFq8orWDS96z91oGg==

tough-cookie@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/tough-cookie/download/tough-cookie-4.0.0.tgz"
  integrity sha1-2CIjTuyogvmR8PkIgkrSYi3b7OQ=
  dependencies:
    psl "^1.1.33"
    punycode "^2.1.1"
    universalify "^0.1.2"

tough-cookie@~2.5.0:
  version "2.5.0"
  resolved "https://registry.nlark.com/tough-cookie/download/tough-cookie-2.5.0.tgz"
  integrity sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

tr46@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/tr46/download/tr46-2.1.0.tgz"
  integrity sha1-+oeqgcpdWUHajL8fm3SdyWmk4kA=
  dependencies:
    punycode "^2.1.1"

tree-kill@1.2.2:
  version "1.2.2"
  resolved "https://registry.nlark.com/tree-kill/download/tree-kill-1.2.2.tgz"
  integrity sha1-TKCakJLIi3OnzcXooBtQeweQoMw=

ts-jest@^26.5.4:
  version "26.5.6"
  resolved "https://registry.nlark.com/ts-jest/download/ts-jest-26.5.6.tgz"
  integrity sha1-wy4HRkJSdOHf4zP0PNPIAOAU7DU=
  dependencies:
    bs-logger "0.x"
    buffer-from "1.x"
    fast-json-stable-stringify "2.x"
    jest-util "^26.1.0"
    json5 "2.x"
    lodash "4.x"
    make-error "1.x"
    mkdirp "1.x"
    semver "7.x"
    yargs-parser "20.x"

ts-loader@^8.0.18:
  version "8.3.0"
  resolved "https://registry.nlark.com/ts-loader/download/ts-loader-8.3.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fts-loader%2Fdownload%2Fts-loader-8.3.0.tgz"
  integrity sha1-gzYEltb4AE+rNYJSeRMsk0Eu3zM=
  dependencies:
    chalk "^4.1.0"
    enhanced-resolve "^4.0.0"
    loader-utils "^2.0.0"
    micromatch "^4.0.0"
    semver "^7.3.4"

ts-md5@^1.2.11:
  version "1.2.11"
  resolved "https://registry.npmmirror.com/ts-md5/-/ts-md5-1.2.11.tgz"
  integrity sha512-vAwy9rEuRE6a8xa1MavIVkLFyyU0ydk4CLMFA5vOVccmQKLOuGb/BHm3oEN7XHf2FoqS+z0pSvhaad/ombd1Vg==

ts-node@^9.1.1:
  version "9.1.1"
  resolved "https://registry.nlark.com/ts-node/download/ts-node-9.1.1.tgz?cache=0&sync_timestamp=1625866710908&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fts-node%2Fdownload%2Fts-node-9.1.1.tgz"
  integrity sha1-UamkUKPpWUAb2l8ASnLVS5NtN20=
  dependencies:
    arg "^4.1.0"
    create-require "^1.1.0"
    diff "^4.0.1"
    make-error "^1.1.1"
    source-map-support "^0.5.17"
    yn "3.1.1"

tsconfig-paths-webpack-plugin@3.5.1:
  version "3.5.1"
  resolved "https://registry.npm.taobao.org/tsconfig-paths-webpack-plugin/download/tsconfig-paths-webpack-plugin-3.5.1.tgz"
  integrity sha1-5Nv0kqINypyqtgCG3ay3A6/CtyY=
  dependencies:
    chalk "^4.1.0"
    enhanced-resolve "^5.7.0"
    tsconfig-paths "^3.9.0"

tsconfig-paths@3.9.0:
  version "3.9.0"
  resolved "https://registry.nlark.com/tsconfig-paths/download/tsconfig-paths-3.9.0.tgz"
  integrity sha1-CYVHpsREiAfo/Ljq4IEGTumjyQs=
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.1"
    minimist "^1.2.0"
    strip-bom "^3.0.0"

tsconfig-paths@^3.9.0:
  version "3.10.1"
  resolved "https://registry.nlark.com/tsconfig-paths/download/tsconfig-paths-3.10.1.tgz"
  integrity sha1-ea5npowVKJ/fXFHLdPOXUi15Xtc=
  dependencies:
    json5 "^2.2.0"
    minimist "^1.2.0"
    strip-bom "^3.0.0"

tslib@2.2.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/tslib/download/tslib-2.2.0.tgz"
  integrity sha1-+yxHWXfjXiQTEe3iaTzuHsZpj1w=

tslib@^1.8.1, tslib@^1.9.0:
  version "1.14.1"
  resolved "https://registry.nlark.com/tslib/download/tslib-1.14.1.tgz"
  integrity sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=

tslib@^2.0.1, tslib@^2.1.0:
  version "2.3.0"
  resolved "https://registry.nlark.com/tslib/download/tslib-2.3.0.tgz"
  integrity sha1-gDuM2rPhK6WBpMpByIObuw2ssJ4=

tsutils@^3.21.0:
  version "3.21.0"
  resolved "https://registry.npm.taobao.org/tsutils/download/tsutils-3.21.0.tgz?cache=0&sync_timestamp=1615138205781&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ftsutils%2Fdownload%2Ftsutils-3.21.0.tgz"
  integrity sha1-tIcX05TOpsHglpg+7Vjp1hcVtiM=
  dependencies:
    tslib "^1.8.1"

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "https://registry.nlark.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "https://registry.npm.taobao.org/tweetnacl/download/tweetnacl-0.14.5.tgz"
  integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "https://registry.npm.taobao.org/type-check/download/type-check-0.4.0.tgz"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-check@~0.3.2:
  version "0.3.2"
  resolved "https://registry.npm.taobao.org/type-check/download/type-check-0.3.2.tgz"
  integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
  dependencies:
    prelude-ls "~1.1.2"

type-detect@4.0.8:
  version "4.0.8"
  resolved "https://registry.npm.taobao.org/type-detect/download/type-detect-4.0.8.tgz"
  integrity sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw=

type-fest@^0.20.2:
  version "0.20.2"
  resolved "https://registry.nlark.com/type-fest/download/type-fest-0.20.2.tgz?cache=0&sync_timestamp=1628211344858&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftype-fest%2Fdownload%2Ftype-fest-0.20.2.tgz"
  integrity sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=

type-fest@^0.21.3:
  version "0.21.3"
  resolved "https://registry.nlark.com/type-fest/download/type-fest-0.21.3.tgz?cache=0&sync_timestamp=1628211344858&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftype-fest%2Fdownload%2Ftype-fest-0.21.3.tgz"
  integrity sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=

type-fest@^0.6.0:
  version "0.6.0"
  resolved "https://registry.nlark.com/type-fest/download/type-fest-0.6.0.tgz?cache=0&sync_timestamp=1628211344858&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftype-fest%2Fdownload%2Ftype-fest-0.6.0.tgz"
  integrity sha1-jSojcNPfiG61yQraHFv2GIrPg4s=

type-fest@^0.8.1:
  version "0.8.1"
  resolved "https://registry.nlark.com/type-fest/download/type-fest-0.8.1.tgz?cache=0&sync_timestamp=1628211344858&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftype-fest%2Fdownload%2Ftype-fest-0.8.1.tgz"
  integrity sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=

type-is@^1.6.4, type-is@~1.6.17, type-is@~1.6.18:
  version "1.6.18"
  resolved "https://registry.nlark.com/type-is/download/type-is-1.6.18.tgz"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typedarray-to-buffer@^3.1.5:
  version "3.1.5"
  resolved "https://registry.npm.taobao.org/typedarray-to-buffer/download/typedarray-to-buffer-3.1.5.tgz"
  integrity sha1-qX7nqf9CaRufeD/xvFES/j/KkIA=
  dependencies:
    is-typedarray "^1.0.0"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "https://registry.nlark.com/typedarray/download/typedarray-0.0.6.tgz"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

typeorm@^0.2.34:
  version "0.2.36"
  resolved "https://registry.nlark.com/typeorm/download/typeorm-0.2.36.tgz"
  integrity sha1-AilUYkOLTxJ50m0V0ygjxSKuGgQ=
  dependencies:
    "@sqltools/formatter" "^1.2.2"
    app-root-path "^3.0.0"
    buffer "^6.0.3"
    chalk "^4.1.0"
    cli-highlight "^2.1.11"
    debug "^4.3.1"
    dotenv "^8.2.0"
    glob "^7.1.6"
    js-yaml "^4.0.0"
    mkdirp "^1.0.4"
    reflect-metadata "^0.1.13"
    sha.js "^2.4.11"
    tslib "^2.1.0"
    xml2js "^0.4.23"
    yargonaut "^1.1.4"
    yargs "^17.0.1"
    zen-observable-ts "^1.0.0"

typescript@4.2.3:
  version "4.2.3"
  resolved "https://registry.nlark.com/typescript/download/typescript-4.2.3.tgz?cache=0&sync_timestamp=1628320770103&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftypescript%2Fdownload%2Ftypescript-4.2.3.tgz"
  integrity sha1-OQYtgBmRLUNyYpjwlJPVmASMHOM=

typescript@^4.2.3:
  version "4.3.5"
  resolved "https://registry.nlark.com/typescript/download/typescript-4.3.5.tgz?cache=0&sync_timestamp=1628320770103&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftypescript%2Fdownload%2Ftypescript-4.3.5.tgz"
  integrity sha1-TRw3zBbok5c8RaBohrcRMjTxGfQ=

union-value@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/union-value/download/union-value-1.0.1.tgz"
  integrity sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

universalify@^0.1.2:
  version "0.1.2"
  resolved "https://registry.nlark.com/universalify/download/universalify-0.1.2.tgz"
  integrity sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=

universalify@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/universalify/download/universalify-2.0.0.tgz"
  integrity sha1-daSYTv7cSwiXXFrrc/Uw0C3yVxc=

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/unpipe/download/unpipe-1.0.0.tgz"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unset-value@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/unset-value/download/unset-value-1.0.0.tgz?cache=0&sync_timestamp=1616088539233&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Funset-value%2Fdownload%2Funset-value-1.0.0.tgz"
  integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://registry.npm.taobao.org/uri-js/download/uri-js-4.4.1.tgz?cache=0&sync_timestamp=1610237517218&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Furi-js%2Fdownload%2Furi-js-4.4.1.tgz"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

urix@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npm.taobao.org/urix/download/urix-0.1.0.tgz"
  integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=

use@^3.1.0:
  version "3.1.1"
  resolved "https://registry.nlark.com/use/download/use-3.1.1.tgz"
  integrity sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=

util-deprecate@^1.0.1, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.nlark.com/util-deprecate/download/util-deprecate-1.0.2.tgz"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/utils-merge/download/utils-merge-1.0.1.tgz"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@8.3.1:
  version "8.3.1"
  resolved "https://registry.nlark.com/uuid/download/uuid-8.3.1.tgz?cache=0&sync_timestamp=1622213136953&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fuuid%2Fdownload%2Fuuid-8.3.1.tgz"
  integrity sha1-K6LmygANpg/OWhlpVKskETHgWjE=

uuid@8.3.2, uuid@^8.3.0:
  version "8.3.2"
  resolved "https://registry.nlark.com/uuid/download/uuid-8.3.2.tgz?cache=0&sync_timestamp=1622213136953&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fuuid%2Fdownload%2Fuuid-8.3.2.tgz"
  integrity sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=

uuid@^3.3.2:
  version "3.4.0"
  resolved "https://registry.nlark.com/uuid/download/uuid-3.4.0.tgz?cache=0&sync_timestamp=1622213136953&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fuuid%2Fdownload%2Fuuid-3.4.0.tgz"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

v8-compile-cache@^2.0.3:
  version "2.3.0"
  resolved "https://registry.npm.taobao.org/v8-compile-cache/download/v8-compile-cache-2.3.0.tgz"
  integrity sha1-LeGWGMZtwkfc+2+ZM4A12CRaLO4=

v8-to-istanbul@^7.0.0:
  version "7.1.2"
  resolved "https://registry.nlark.com/v8-to-istanbul/download/v8-to-istanbul-7.1.2.tgz"
  integrity sha1-MImNGn+gyE0iWiwUNPuVjykIg8E=
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.1"
    convert-source-map "^1.6.0"
    source-map "^0.7.3"

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "https://registry.npm.taobao.org/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vary@^1, vary@~1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/vary/download/vary-1.1.2.tgz"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

verror@1.10.0:
  version "1.10.0"
  resolved "https://registry.npm.taobao.org/verror/download/verror-1.10.0.tgz"
  integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

w3c-hr-time@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/w3c-hr-time/download/w3c-hr-time-1.0.2.tgz"
  integrity sha1-ConN9cwVgi35w2BUNnaWPgzDCM0=
  dependencies:
    browser-process-hrtime "^1.0.0"

w3c-xmlserializer@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/w3c-xmlserializer/download/w3c-xmlserializer-2.0.0.tgz"
  integrity sha1-PnEEoFt1FGzGD1ZDgLf2g6zxAgo=
  dependencies:
    xml-name-validator "^3.0.0"

walker@^1.0.7, walker@~1.0.5:
  version "1.0.7"
  resolved "https://registry.npm.taobao.org/walker/download/walker-1.0.7.tgz"
  integrity sha1-L3+bj9ENZ3JisYqITijRlhjgKPs=
  dependencies:
    makeerror "1.0.x"

watchpack@^2.0.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/watchpack/download/watchpack-2.2.0.tgz"
  integrity sha1-R9ePVBX+VQ7NdA+Z/iiCMjpYsc4=
  dependencies:
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.1.2"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/wcwidth/download/wcwidth-1.0.1.tgz"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

webidl-conversions@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npm.taobao.org/webidl-conversions/download/webidl-conversions-5.0.0.tgz"
  integrity sha1-rlnIoAsSFUOirMZcBDT1ew/BGv8=

webidl-conversions@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npm.taobao.org/webidl-conversions/download/webidl-conversions-6.1.0.tgz"
  integrity sha1-kRG01+qArNQPUnDWZmIa+ni2lRQ=

webpack-node-externals@2.5.2:
  version "2.5.2"
  resolved "https://registry.nlark.com/webpack-node-externals/download/webpack-node-externals-2.5.2.tgz"
  integrity sha1-F44BeiT+xgFbyeZyx3lYpq+shh0=

webpack-sources@^2.1.1:
  version "2.3.1"
  resolved "https://registry.nlark.com/webpack-sources/download/webpack-sources-2.3.1.tgz?cache=0&sync_timestamp=1627898333297&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fwebpack-sources%2Fdownload%2Fwebpack-sources-2.3.1.tgz"
  integrity sha1-Vw3grxY5Sf4nIjPCzv4bVvdFEf0=
  dependencies:
    source-list-map "^2.0.1"
    source-map "^0.6.1"

webpack@5.28.0:
  version "5.28.0"
  resolved "https://registry.nlark.com/webpack/download/webpack-5.28.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fwebpack%2Fdownload%2Fwebpack-5.28.0.tgz"
  integrity sha1-Dei81wYYaybaCdTR6MvT5AJafC8=
  dependencies:
    "@types/eslint-scope" "^3.7.0"
    "@types/estree" "^0.0.46"
    "@webassemblyjs/ast" "1.11.0"
    "@webassemblyjs/wasm-edit" "1.11.0"
    "@webassemblyjs/wasm-parser" "1.11.0"
    acorn "^8.0.4"
    browserslist "^4.14.5"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^5.7.0"
    es-module-lexer "^0.4.0"
    eslint-scope "^5.1.1"
    events "^3.2.0"
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.2.4"
    json-parse-better-errors "^1.0.2"
    loader-runner "^4.2.0"
    mime-types "^2.1.27"
    neo-async "^2.6.2"
    schema-utils "^3.0.0"
    tapable "^2.1.1"
    terser-webpack-plugin "^5.1.1"
    watchpack "^2.0.0"
    webpack-sources "^2.1.1"

whatwg-encoding@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npm.taobao.org/whatwg-encoding/download/whatwg-encoding-1.0.5.tgz"
  integrity sha1-WrrPd3wyFmpR0IXWtPPn0nET3bA=
  dependencies:
    iconv-lite "0.4.24"

whatwg-mimetype@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npm.taobao.org/whatwg-mimetype/download/whatwg-mimetype-2.3.0.tgz"
  integrity sha1-PUseAxLSB5h5+Cav8Y2+7KWWD78=

whatwg-url@^8.0.0, whatwg-url@^8.5.0:
  version "8.7.0"
  resolved "https://registry.nlark.com/whatwg-url/download/whatwg-url-8.7.0.tgz"
  integrity sha1-ZWp45RD/jzk3vAvL6fXArDWUG3c=
  dependencies:
    lodash "^4.7.0"
    tr46 "^2.1.0"
    webidl-conversions "^6.1.0"

which-module@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/which-module/download/which-module-2.0.0.tgz"
  integrity sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=

which@1, which@^1.2.9:
  version "1.3.1"
  resolved "https://registry.nlark.com/which/download/which-1.3.1.tgz"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

which@^2.0.1, which@^2.0.2:
  version "2.0.2"
  resolved "https://registry.nlark.com/which/download/which-2.0.2.tgz"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

wide-align@^1.1.0:
  version "1.1.3"
  resolved "https://registry.npm.taobao.org/wide-align/download/wide-align-1.1.3.tgz"
  integrity sha1-rgdOa9wMFKQx6ATmJFScYzsABFc=
  dependencies:
    string-width "^1.0.2 || 2"

windows-release@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/windows-release/download/windows-release-4.0.0.tgz"
  integrity sha1-RyXscCF9G/bgLHdyQTspzd6ew3c=
  dependencies:
    execa "^4.0.2"

wmf@~1.0.1:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/wmf/-/wmf-1.0.2.tgz"
  integrity sha512-/p9K7bEh0Dj6WbXg4JG0xvLQmIadrner1bi45VMJTfnbVHsc7yIajZyoSoK60/dtVBs12Fm6WkUI5/3WAVsNMw==

word-wrap@^1.2.3, word-wrap@~1.2.3:
  version "1.2.3"
  resolved "https://registry.nlark.com/word-wrap/download/word-wrap-1.2.3.tgz"
  integrity sha1-YQY29rH3A4kb00dxzLF/uTtHB5w=

word@~0.3.0:
  version "0.3.0"
  resolved "https://registry.npmmirror.com/word/-/word-0.3.0.tgz"
  integrity sha512-OELeY0Q61OXpdUfTp+oweA/vtLVg5VDOXh+3he3PNzLGG/y0oylSOC1xRVj0+l4vQ3tj/bB1HVHv1ocXkQceFA==

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "https://registry.npm.taobao.org/wrap-ansi/download/wrap-ansi-6.2.0.tgz?cache=0&sync_timestamp=1618558913931&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-6.2.0.tgz"
  integrity sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npm.taobao.org/wrap-ansi/download/wrap-ansi-7.0.0.tgz?cache=0&sync_timestamp=1618558913931&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-7.0.0.tgz"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.nlark.com/wrappy/download/wrappy-1.0.2.tgz"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^3.0.0:
  version "3.0.3"
  resolved "https://registry.npm.taobao.org/write-file-atomic/download/write-file-atomic-3.0.3.tgz"
  integrity sha1-Vr1cWlxwSBzRnFcb05q5ZaXeVug=
  dependencies:
    imurmurhash "^0.1.4"
    is-typedarray "^1.0.0"
    signal-exit "^3.0.2"
    typedarray-to-buffer "^3.1.5"

ws@^7.4.6, ws@~7.4.2:
  version "7.4.6"
  resolved "https://registry.nlark.com/ws/download/ws-7.4.6.tgz?cache=0&sync_timestamp=1627496096076&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fws%2Fdownload%2Fws-7.4.6.tgz"
  integrity sha1-VlTKjs3u5HwzqaS/bSjivimAN3w=

xlsx@^0.18.5:
  version "0.18.5"
  resolved "https://registry.npmmirror.com/xlsx/-/xlsx-0.18.5.tgz"
  integrity sha512-dmg3LCjBPHZnQp5/F/+nnTa+miPJxUXB6vtk42YjBBKayDNagxGEeIdWApkYPOf3Z3pm3k62Knjzp7lMeTEtFQ==
  dependencies:
    adler-32 "~1.3.0"
    cfb "~1.2.1"
    codepage "~1.15.0"
    crc-32 "~1.2.1"
    ssf "~0.11.2"
    wmf "~1.0.1"
    word "~0.3.0"

xml-name-validator@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/xml-name-validator/download/xml-name-validator-3.0.0.tgz"
  integrity sha1-auc+Bt5NjG5H+fsYH3jWSK1FfGo=

xml2js@^0.4.23:
  version "0.4.23"
  resolved "https://registry.nlark.com/xml2js/download/xml2js-0.4.23.tgz"
  integrity sha1-oMaVFnUkIesqx1juTUzPWIQ+rGY=
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xmlbuilder@~11.0.0:
  version "11.0.1"
  resolved "https://registry.npm.taobao.org/xmlbuilder/download/xmlbuilder-11.0.1.tgz"
  integrity sha1-vpuuHIoEbnazESdyY0fQrXACvrM=

xmlchars@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/xmlchars/download/xmlchars-2.2.0.tgz"
  integrity sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs=

xmlhttprequest-ssl@~1.6.2:
  version "1.6.3"
  resolved "https://registry.nlark.com/xmlhttprequest-ssl/download/xmlhttprequest-ssl-1.6.3.tgz"
  integrity sha1-A7cThzsBZZ36LBxdBWBlsn3cLeY=

xtend@^4.0.0:
  version "4.0.2"
  resolved "https://registry.nlark.com/xtend/download/xtend-4.0.2.tgz"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

y18n@^4.0.0:
  version "4.0.3"
  resolved "https://registry.npm.taobao.org/y18n/download/y18n-4.0.3.tgz?cache=0&sync_timestamp=1617822642544&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fy18n%2Fdownload%2Fy18n-4.0.3.tgz"
  integrity sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8=

y18n@^5.0.5:
  version "5.0.8"
  resolved "https://registry.npm.taobao.org/y18n/download/y18n-5.0.8.tgz?cache=0&sync_timestamp=1617822642544&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fy18n%2Fdownload%2Fy18n-5.0.8.tgz"
  integrity sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=

yallist@^3.0.0, yallist@^3.0.3:
  version "3.1.1"
  resolved "https://registry.nlark.com/yallist/download/yallist-3.1.1.tgz"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/yallist/download/yallist-4.0.0.tgz"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=

yaml@^1.7.2:
  version "1.10.2"
  resolved "https://registry.nlark.com/yaml/download/yaml-1.10.2.tgz"
  integrity sha1-IwHF/78StGfejaIzOkWeKeeSDks=

yargonaut@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npm.taobao.org/yargonaut/download/yargonaut-1.1.4.tgz"
  integrity sha1-xk9WQyx0ZScSIfU/XMUXiQw9bgw=
  dependencies:
    chalk "^1.1.1"
    figlet "^1.1.1"
    parent-require "^1.0.0"

yargs-parser@20.x, yargs-parser@^20.2.2:
  version "20.2.9"
  resolved "https://registry.nlark.com/yargs-parser/download/yargs-parser-20.2.9.tgz"
  integrity sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=

yargs-parser@^18.1.2:
  version "18.1.3"
  resolved "https://registry.nlark.com/yargs-parser/download/yargs-parser-18.1.3.tgz"
  integrity sha1-vmjEl1xrKr9GkjawyHA2L6sJp7A=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs@^15.3.1, yargs@^15.4.1:
  version "15.4.1"
  resolved "https://registry.nlark.com/yargs/download/yargs-15.4.1.tgz"
  integrity sha1-DYehbeAa7p2L7Cv7909nhRcw9Pg=
  dependencies:
    cliui "^6.0.0"
    decamelize "^1.2.0"
    find-up "^4.1.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^4.2.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^18.1.2"

yargs@^16.0.0:
  version "16.2.0"
  resolved "https://registry.nlark.com/yargs/download/yargs-16.2.0.tgz"
  integrity sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=
  dependencies:
    cliui "^7.0.2"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.0"
    y18n "^5.0.5"
    yargs-parser "^20.2.2"

yargs@^17.0.1:
  version "17.1.0"
  resolved "https://registry.nlark.com/yargs/download/yargs-17.1.0.tgz"
  integrity sha1-DNmCegVyyaF5U2HE0VMOU62haM8=
  dependencies:
    cliui "^7.0.2"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.0"
    y18n "^5.0.5"
    yargs-parser "^20.2.2"

yeast@0.1.2:
  version "0.1.2"
  resolved "https://registry.nlark.com/yeast/download/yeast-0.1.2.tgz"
  integrity sha1-AI4G2AlDIMNy28L47XagymyKxBk=

yn@3.1.1:
  version "3.1.1"
  resolved "https://registry.npm.taobao.org/yn/download/yn-3.1.1.tgz"
  integrity sha1-HodAGgnXZ8HV6rJqbkwYUYLS61A=

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npm.taobao.org/yocto-queue/download/yocto-queue-0.1.0.tgz?cache=0&sync_timestamp=1606290282107&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fyocto-queue%2Fdownload%2Fyocto-queue-0.1.0.tgz"
  integrity sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=

yup@^0.29.1:
  version "0.29.3"
  resolved "https://registry.npmmirror.com/yup/-/yup-0.29.3.tgz"
  integrity sha512-RNUGiZ/sQ37CkhzKFoedkeMfJM0vNQyaz+wRZJzxdKE7VfDeVKH8bb4rr7XhRLbHJz5hSjoDNwMEIaKhuMZ8gQ==
  dependencies:
    "@babel/runtime" "^7.10.5"
    fn-name "~3.0.0"
    lodash "^4.17.15"
    lodash-es "^4.17.11"
    property-expr "^2.0.2"
    synchronous-promise "^2.0.13"
    toposort "^2.0.2"

zen-observable-ts@^1.0.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/zen-observable-ts/download/zen-observable-ts-1.1.0.tgz"
  integrity sha1-LRqp15uHBY6bdWmLknkcGDhVH4M=
  dependencies:
    "@types/zen-observable" "0.8.3"
    zen-observable "0.8.15"

zen-observable@0.8.15:
  version "0.8.15"
  resolved "https://registry.npm.taobao.org/zen-observable/download/zen-observable-0.8.15.tgz"
  integrity sha1-lkFcUS2OP/2SCv04iWBOMLnqrBU=
