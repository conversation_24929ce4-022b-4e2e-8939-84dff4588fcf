<template>
  <div class="site-inspection-template">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <el-button type="primary" @click="handleCreateTemplate"
            >创建模版</el-button
          >
        </div>
      </template>

      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="模版名称">
          <el-input
            v-model="searchForm.templateName"
            placeholder="请输入模版名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="适用类型">
          <el-select
            v-model="searchForm.siteType"
            placeholder="请选择类型"
            clearable
          >
            <el-option label="水泵站" value="pump_station" />
            <el-option label="处理厂" value="treatment_plant" />
            <el-option label="配水站" value="distribution_station" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="templateData" stripe>
        <el-table-column prop="templateName" label="模版名称" />
        <el-table-column prop="siteType" label="适用类型" width="120">
          <template #default="{ row }">
            {{ getSiteTypeText(row.siteType) }}
          </template>
        </el-table-column>
        <el-table-column prop="itemCount" label="检查项数量" width="120" />
        <el-table-column prop="estimatedDuration" label="预计耗时" width="120">
          <template #default="{ row }">
            {{ row.estimatedDuration }}分钟
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === "active" ? "启用" : "禁用" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="150" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)"
              >查看</el-button
            >
            <el-button type="success" size="small" @click="handleEdit(row)"
              >编辑</el-button
            >
            <el-button type="danger" size="small" @click="handleDelete(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";

const searchForm = reactive({
  templateName: "",
  siteType: "",
});

const templateData = ref([
  {
    id: 1,
    templateName: "水泵站标准巡检模版",
    siteType: "pump_station",
    itemCount: 20,
    estimatedDuration: 90,
    status: "active",
    createTime: "2024-01-01",
  },
  {
    id: 2,
    templateName: "污水处理厂巡检模版",
    siteType: "treatment_plant",
    itemCount: 35,
    estimatedDuration: 120,
    status: "active",
    createTime: "2024-01-02",
  },
]);

const getSiteTypeText = (type: string) => {
  const texts: Record<string, string> = {
    pump_station: "水泵站",
    treatment_plant: "处理厂",
    distribution_station: "配水站",
    monitoring_station: "监测站",
  };
  return texts[type] || "其他";
};

const handleCreateTemplate = () => console.log("创建模版");
const handleSearch = () => console.log("搜索模版", searchForm);
const handleReset = () =>
  Object.assign(searchForm, { templateName: "", siteType: "" });
const handleView = (row: any) => console.log("查看模版", row);
const handleEdit = (row: any) => console.log("编辑模版", row);
const handleDelete = (row: any) => console.log("删除模版", row);

onMounted(() => {
  // 初始化
});
</script>

<style scoped>
.site-inspection-template {
  padding: 20px;
}

.page-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}
</style>
