# CLAUDE.md

始终使用中文和我交流

## 项目概述

水利站后端监控系统，采用双数据库架构（MongoDB + MySQL），提供TCP设备数据接收、物理开关档位变化检测、数据降采样和设备控制功能。所有设备采用本地自主控制模式。

## 开发命令

### 运行系统
```bash
python main.py
```
启动TCP服务器（8889）、API服务器（8500）和数据处理服务。

### 仿真测试
```bash
python simulation_test.py                    # 50个点位，每5秒发送数据
python simulation_test.py --duration 300    # 限时5分钟
python simulation_test.py --test-single     # 单次测试
```

### 代码检查
```bash
npm run lint       # 代码检查
npm run typecheck  # 类型检查
```

## 核心架构

### 数据库
- **MongoDB**（tmpfs内存）：5秒级设备数据，重启后不持久化
- **MySQL**：配置数据、降采样数据，持久化存储

### 主要模块
```python
main.py                    # 系统入口点
generic_tcp_server.py      # TCP服务器（端口8889）
api_server.py             # REST API（端口8500）
device_status_logger.py   # 设备状态变化日志
realtime_data_logger.py   # 实时数据日志  
data_downsampling_service.py  # 数据降采样服务
```

### 日志系统
```
logs/
├── device_status/device_status_YYYYMMDD.log  # 物理开关档位变化
└── realtime_data/realtime_data_YYYYMMDD.log  # 完整设备数据
```

## 关键特性

### 物理开关三档位
```json
{
  "0": "停止模式（强制关闭）",
  "1": "手动模式（强制启动）", 
  "2": "自动模式（按规则运行）"
}
```

### 设备映射
```python
DO21 → water_pump1  # 水泵1
DO22 → water_pump2  # 水泵2
DO23 → air_pump1    # 气泵1  
DO24 → air_pump2    # 气泵2
```

### 数据降采样
```
5秒级数据(MongoDB) → 分钟级数据(MySQL) → 小时级数据(MySQL)
每分钟05秒执行    → 每小时05分执行    → 保留7天分钟数据
```

## API接口

### 设备控制
```python
POST /control/{sn}
{
  "do_name": "DO21",
  "value": 1  # 0=断开, 1=闭合
}
```

### 系统信息
```python
GET /docs           # API文档
GET /openapi.json   # OpenAPI规范
```

## 重要说明

- ✅ 设备本地自主控制，服务器不进行远程状态维持
- ✅ 物理开关档位变化单独发送消息，记录到文件系统
- ✅ 支持三档位状态：停止/手动/自动
- ✅ 仿真测试每5秒发送数据，包含状态变化模拟
- ✅ MongoDB内存存储，MySQL持久化配置
- ✅ 数据自动降采样和清理，避免磁盘空间问题

## 监控要点

- MongoDB内存使用情况
- 日志文件磁盘占用
- 数据降采样服务状态
- 设备连接和数据接收频率