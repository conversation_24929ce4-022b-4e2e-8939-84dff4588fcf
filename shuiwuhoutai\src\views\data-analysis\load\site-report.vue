<template>
  <div class="site-load-report">
    <el-card shadow="hover" class="page-header">
      <template #header>
        <div class="card-header">
          <div class="header-actions">
            <el-button type="primary" size="small" @click="generateSiteReport"
              >生成报表</el-button
            >
            <el-button type="success" size="small" @click="compareMultiSites"
              >站点对比</el-button
            >
          </div>
        </div>
      </template>

      <!-- 报表配置表单 -->
      <el-form :inline="true" :model="reportForm" class="report-form">
        <el-form-item label="选择站点">
          <el-select
            v-model="reportForm.siteIds"
            placeholder="请选择站点"
            multiple
            clearable
          >
            <el-option label="浦南一号泵站" value="pn001" />
            <el-option label="浦南二号泵站" value="pn002" />
            <el-option label="史北一号泵站" value="sb001" />
            <el-option label="史北二号泵站" value="sb002" />
            <el-option label="东部一号泵站" value="eb001" />
          </el-select>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="报表类型">
          <el-select
            v-model="reportForm.reportType"
            placeholder="请选择报表类型"
          >
            <el-option label="负荷日报" value="daily" />
            <el-option label="负荷周报" value="weekly" />
            <el-option label="负荷月报" value="monthly" />
            <el-option label="自定义报表" value="custom" />
          </el-select>
        </el-form-item>

        <el-form-item label="报表内容">
          <el-select
            v-model="reportForm.content"
            placeholder="请选择内容"
            multiple
          >
            <el-option label="负荷曲线" value="curve" />
            <el-option label="峰谷分析" value="peak_valley" />
            <el-option label="负荷率分析" value="load_factor" />
            <el-option label="设备利用率" value="utilization" />
            <el-option label="对比分析" value="comparison" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 站点负荷概览 -->
    <div class="sites-overview">
      <el-row :gutter="20">
        <el-col :span="6" v-for="(site, index) in sitesOverview" :key="index">
          <el-card shadow="hover" :class="['site-overview-card', site.status]">
            <div class="site-header">
              <div class="site-name">{{ site.siteName }}</div>
              <div class="site-status">
                <el-tag :type="getSiteStatusType(site.status)" size="small">
                  {{ getSiteStatusLabel(site.status) }}
                </el-tag>
              </div>
            </div>
            <div class="site-metrics">
              <div class="metric-item">
                <span class="metric-label">当前负荷:</span>
                <span class="metric-value">{{ site.currentLoad }} kW</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">峰值负荷:</span>
                <span class="metric-value">{{ site.peakLoad }} kW</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">负荷率:</span>
                <span class="metric-value">{{ site.loadFactor }}%</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">利用率:</span>
                <span class="metric-value">{{ site.utilization }}%</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 站点负荷分析图表 -->
    <div class="site-charts">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>站点负荷对比分析</span>
                <div>
                  <el-select
                    v-model="chartMetric"
                    size="small"
                    style="margin-right: 10px"
                  >
                    <el-option label="负荷曲线" value="load_curve" />
                    <el-option label="负荷率" value="load_factor" />
                    <el-option label="利用率" value="utilization" />
                  </el-select>
                  <el-button-group size="small">
                    <el-button
                      :type="chartPeriod === '24h' ? 'primary' : ''"
                      @click="chartPeriod = '24h'"
                      >24小时</el-button
                    >
                    <el-button
                      :type="chartPeriod === '7d' ? 'primary' : ''"
                      @click="chartPeriod = '7d'"
                      >7天</el-button
                    >
                    <el-button
                      :type="chartPeriod === '30d' ? 'primary' : ''"
                      @click="chartPeriod = '30d'"
                      >30天</el-button
                    >
                  </el-button-group>
                </div>
              </div>
            </template>
            <div id="siteLoadComparisonChart" style="height: 400px"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>站点负荷分布</span>
              </div>
            </template>
            <div id="siteLoadDistributionChart" style="height: 350px"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>站点性能雷达图</span>
              </div>
            </template>
            <div id="sitePerformanceRadarChart" style="height: 350px"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 站点负荷统计表格 -->
    <el-card shadow="hover" class="table-card">
      <template #header>
        <div class="card-header">
          <span>站点负荷统计数据</span>
          <div class="table-actions">
            <el-button size="small" @click="exportSiteReport"
              >导出报表</el-button
            >
            <el-button size="small" type="warning" @click="setLoadTarget"
              >设置目标</el-button
            >
            <el-button size="small" type="primary" @click="optimizeSiteLoad"
              >优化建议</el-button
            >
          </div>
        </div>
      </template>

      <el-table :data="siteLoadData" stripe style="width: 100%" border>
        <el-table-column type="selection" width="55" />
        <el-table-column
          prop="siteName"
          label="站点名称"
          width="120"
          fixed="left"
        />
        <el-table-column prop="area" label="区域" width="80" />
        <el-table-column prop="siteType" label="类型" width="100" />
        <el-table-column
          prop="ratedCapacity"
          label="额定容量(kW)"
          width="120"
        />
        <el-table-column prop="currentLoad" label="当前负荷(kW)" width="120" />
        <el-table-column prop="peakLoad" label="峰值负荷(kW)" width="120" />
        <el-table-column prop="avgLoad" label="平均负荷(kW)" width="120" />
        <el-table-column prop="minLoad" label="最小负荷(kW)" width="120" />
        <el-table-column prop="loadFactor" label="负荷率(%)" width="100">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.loadFactor"
              :color="getLoadFactorColor(scope.row.loadFactor)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="utilization" label="设备利用率(%)" width="120" />
        <el-table-column
          prop="operatingHours"
          label="运行时长(h)"
          width="120"
        />
        <el-table-column prop="peakTime" label="峰值时间" width="100" />
        <el-table-column prop="valleyTime" label="谷值时间" width="100" />
        <el-table-column prop="fluctuation" label="波动率(%)" width="100" />
        <el-table-column prop="efficiency" label="效率(%)" width="100">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.efficiency"
              :color="getEfficiencyColor(scope.row.efficiency)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="alertCount" label="预警次数" width="100" />
        <el-table-column prop="performanceLevel" label="性能等级" width="100">
          <template #default="scope">
            <el-tag :type="getPerformanceLevelType(scope.row.performanceLevel)">
              {{ scope.row.performanceLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="200">
          <template #default="scope">
            <el-button size="small" @click="viewSiteDetail(scope.row)"
              >详情</el-button
            >
            <el-button
              size="small"
              type="warning"
              @click="adjustSiteLoad(scope.row)"
              >调节</el-button
            >
            <el-button
              size="small"
              type="primary"
              @click="generateSiteDetailReport(scope.row)"
              >报告</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 负荷分析结论 -->
    <el-card shadow="hover" class="conclusion-card">
      <template #header>
        <div class="card-header">
          <span>负荷分析结论</span>
          <span class="report-time">报表时间: {{ reportTime }}</span>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col :span="8">
          <div class="conclusion-section">
            <h4>🔍 关键发现</h4>
            <ul class="finding-list">
              <li v-for="(finding, index) in keyFindings" :key="index">
                {{ finding }}
              </li>
            </ul>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="conclusion-section">
            <h4>⚠️ 关注点</h4>
            <ul class="attention-list">
              <li v-for="(attention, index) in attentionPoints" :key="index">
                {{ attention }}
              </li>
            </ul>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="conclusion-section">
            <h4>📋 优化建议</h4>
            <ul class="recommendation-list">
              <li
                v-for="(recommendation, index) in recommendations"
                :key="index"
              >
                {{ recommendation }}
              </li>
            </ul>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";

// 报表配置表单
const reportForm = reactive({
  siteIds: ["pn001", "sb001"],
  reportType: "daily",
  content: ["curve", "peak_valley", "load_factor"],
});

// 时间范围
const dateRange = ref([]);

// 图表配置
const chartMetric = ref("load_curve");
const chartPeriod = ref("24h");

// 报表时间
const reportTime = ref("2024-01-20 14:30:25");

// 站点概览数据
const sitesOverview = ref([
  {
    siteName: "浦南一号泵站",
    status: "normal",
    currentLoad: 856.2,
    peakLoad: 980.5,
    loadFactor: 82.3,
    utilization: 87.5,
  },
  {
    siteName: "史北二号泵站",
    status: "warning",
    currentLoad: 1420.8,
    peakLoad: 1580.5,
    loadFactor: 76.8,
    utilization: 92.1,
  },
  {
    siteName: "东部一号泵站",
    status: "normal",
    currentLoad: 620.5,
    peakLoad: 720.3,
    loadFactor: 85.7,
    utilization: 78.9,
  },
  {
    siteName: "浦南二号泵站",
    status: "good",
    currentLoad: 540.2,
    peakLoad: 680.8,
    loadFactor: 88.2,
    utilization: 81.6,
  },
]);

// 站点负荷详细数据
const siteLoadData = ref([
  {
    siteName: "浦南一号泵站",
    area: "浦南",
    siteType: "取水泵站",
    ratedCapacity: 1200,
    currentLoad: 856.2,
    peakLoad: 980.5,
    avgLoad: 720.8,
    minLoad: 420.3,
    loadFactor: 82,
    utilization: 87.5,
    operatingHours: 18.5,
    peakTime: "14:30",
    valleyTime: "03:15",
    fluctuation: 12.5,
    efficiency: 89,
    alertCount: 2,
    performanceLevel: "优秀",
  },
  {
    siteName: "史北二号泵站",
    area: "史北",
    siteType: "污水泵站",
    ratedCapacity: 1800,
    currentLoad: 1420.8,
    peakLoad: 1580.5,
    avgLoad: 1120.6,
    minLoad: 680.2,
    loadFactor: 76,
    utilization: 92.1,
    operatingHours: 22.3,
    peakTime: "15:45",
    valleyTime: "02:30",
    fluctuation: 18.3,
    efficiency: 82,
    alertCount: 5,
    performanceLevel: "良好",
  },
]);

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 28,
});

// 关键发现
const keyFindings = ref([
  "史北二号泵站负荷率达92.1%，接近满负荷运行",
  "浦南区域站点负荷分布较为均衡",
  "峰谷负荷差异显著，存在优化空间",
  "东部站点设备利用率有待提升",
]);

// 关注点
const attentionPoints = ref([
  "史北二号泵站预警次数较多，需重点关注",
  "部分站点负荷波动率超过15%",
  "夜间低负荷时段设备空转现象",
  "负荷预测准确性有待提高",
]);

// 优化建议
const recommendations = ref([
  "实施智能调度，优化负荷分配",
  "加强设备维护，提升运行效率",
  "建立负荷预警机制，及时调整",
  "推广变频技术，降低能耗",
]);

// 获取站点状态类型
const getSiteStatusType = (status) => {
  const typeMap = {
    good: "success",
    normal: "info",
    warning: "warning",
    danger: "danger",
  };
  return typeMap[status] || "info";
};

// 获取站点状态标签
const getSiteStatusLabel = (status) => {
  const labelMap = {
    good: "优秀",
    normal: "正常",
    warning: "预警",
    danger: "告警",
  };
  return labelMap[status] || "正常";
};

// 获取负荷率颜色
const getLoadFactorColor = (factor) => {
  if (factor >= 85) return "#67c23a";
  if (factor >= 70) return "#e6a23c";
  return "#f56c6c";
};

// 获取效率颜色
const getEfficiencyColor = (efficiency) => {
  if (efficiency >= 85) return "#67c23a";
  if (efficiency >= 70) return "#e6a23c";
  return "#f56c6c";
};

// 获取性能等级类型
const getPerformanceLevelType = (level) => {
  const typeMap = {
    优秀: "success",
    良好: "info",
    一般: "warning",
    较差: "danger",
  };
  return typeMap[level] || "info";
};

// 查询处理
const handleQuery = () => {
  ElMessage.success("查询成功");
};

// 重置表单
const resetForm = () => {
  reportForm.siteIds = ["pn001", "sb001"];
  reportForm.reportType = "daily";
  reportForm.content = ["curve", "peak_valley", "load_factor"];
  dateRange.value = [];
  ElMessage.info("已重置查询条件");
};

// 生成站点报表
const generateSiteReport = () => {
  ElMessage.success("站点报表生成中，请稍候...");
};

// 站点对比
const compareMultiSites = () => {
  ElMessage.info("站点对比分析功能开发中");
};

// 查看站点详情
const viewSiteDetail = (row) => {
  ElMessage.info(`查看 ${row.siteName} 的详细信息`);
};

// 调节站点负荷
const adjustSiteLoad = (row) => {
  ElMessage.success(`正在为 ${row.siteName} 调节负荷`);
};

// 生成站点详细报告
const generateSiteDetailReport = (row) => {
  ElMessage.success(`正在为 ${row.siteName} 生成详细报告`);
};

// 导出站点报表
const exportSiteReport = () => {
  ElMessage.success("站点报表导出成功");
};

// 设置负荷目标
const setLoadTarget = () => {
  ElMessage.info("负荷目标设置功能开发中");
};

// 优化站点负荷
const optimizeSiteLoad = () => {
  ElMessage.success("站点负荷优化建议生成中");
};

// 分页处理
const handleSizeChange = (size) => {
  pagination.pageSize = size;
};

const handleCurrentChange = (page) => {
  pagination.currentPage = page;
};

// 组件挂载
onMounted(() => {
  initCharts();
});

// 初始化图表
const initCharts = () => {
  console.log("初始化站点负荷报表图表");
};
</script>

<style scoped>
.site-load-report {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.report-form {
  margin-bottom: 0;
}

.sites-overview {
  margin-bottom: 20px;
}

.site-overview-card {
  cursor: pointer;
  transition: all 0.3s;
  border-left: 4px solid;
}

.site-overview-card.good {
  border-left-color: #67c23a;
}

.site-overview-card.normal {
  border-left-color: #409eff;
}

.site-overview-card.warning {
  border-left-color: #e6a23c;
}

.site-overview-card.danger {
  border-left-color: #f56c6c;
}

.site-overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.site-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.site-name {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.site-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-size: 14px;
  color: #606266;
}

.metric-value {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.site-charts {
  margin-bottom: 20px;
}

.chart-card {
  min-height: 450px;
}

.table-card {
  margin-bottom: 20px;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.conclusion-card {
  margin-bottom: 20px;
}

.report-time {
  font-size: 14px;
  color: #666;
  background: #f5f5f5;
  padding: 4px 12px;
  border-radius: 4px;
}

.conclusion-section h4 {
  color: #303133;
  margin-bottom: 15px;
  font-size: 16px;
}

.finding-list,
.attention-list,
.recommendation-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.finding-list li,
.attention-list li,
.recommendation-list li {
  margin-bottom: 10px;
  font-size: 14px;
  line-height: 1.5;
  padding-left: 15px;
  position: relative;
}

.finding-list li::before {
  content: "•";
  color: #409eff;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.attention-list li::before {
  content: "⚠";
  color: #e6a23c;
  position: absolute;
  left: 0;
}

.recommendation-list li::before {
  content: "✓";
  color: #67c23a;
  font-weight: bold;
  position: absolute;
  left: 0;
}
</style>
