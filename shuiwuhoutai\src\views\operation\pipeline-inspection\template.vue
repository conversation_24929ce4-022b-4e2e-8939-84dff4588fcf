<template>
  <div class="pipeline-inspection-template">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <el-button type="primary" @click="handleCreateTemplate"
            >创建模版</el-button
          >
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="模版名称">
          <el-input
            v-model="searchForm.templateName"
            placeholder="请输入模版名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="适用类型">
          <el-select
            v-model="searchForm.pipelineType"
            placeholder="请选择类型"
            clearable
          >
            <el-option label="供水管网" value="water_supply" />
            <el-option label="排水管网" value="drainage" />
            <el-option label="燃气管网" value="gas" />
          </el-select>
        </el-form-item>
        <el-form-item label="模版状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 模版列表 -->
      <el-table :data="templateData" stripe>
        <el-table-column prop="templateName" label="模版名称" />
        <el-table-column prop="pipelineType" label="适用类型" width="120">
          <template #default="{ row }">
            {{ getTypeText(row.pipelineType) }}
          </template>
        </el-table-column>
        <el-table-column prop="itemCount" label="检查项数量" width="120" />
        <el-table-column prop="estimatedDuration" label="预计耗时" width="120">
          <template #default="{ row }">
            {{ row.estimatedDuration }}分钟
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === "active" ? "启用" : "禁用" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="150" />
        <el-table-column prop="creator" label="创建人" width="100" />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)"
              >查看</el-button
            >
            <el-button type="success" size="small" @click="handleEdit(row)"
              >编辑</el-button
            >
            <el-button type="warning" size="small" @click="handleCopy(row)"
              >复制</el-button
            >
            <el-button type="danger" size="small" @click="handleDelete(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 模版详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="模版详情" width="800px">
      <div v-if="currentTemplate" class="template-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="模版名称">{{
            currentTemplate.templateName
          }}</el-descriptions-item>
          <el-descriptions-item label="适用类型">{{
            getTypeText(currentTemplate.pipelineType)
          }}</el-descriptions-item>
          <el-descriptions-item label="预计耗时"
            >{{ currentTemplate.estimatedDuration }}分钟</el-descriptions-item
          >
          <el-descriptions-item label="状态">
            <el-tag
              :type="currentTemplate.status === 'active' ? 'success' : 'danger'"
            >
              {{ currentTemplate.status === "active" ? "启用" : "禁用" }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建人">{{
            currentTemplate.creator
          }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{
            currentTemplate.createTime
          }}</el-descriptions-item>
        </el-descriptions>

        <div class="detail-section">
          <h4>检查项目</h4>
          <el-table :data="currentTemplate.checkItems" size="small">
            <el-table-column prop="category" label="检查类别" width="120" />
            <el-table-column prop="item" label="检查项目" />
            <el-table-column prop="method" label="检查方法" />
            <el-table-column prop="standard" label="检查标准" />
            <el-table-column prop="required" label="是否必填" width="100">
              <template #default="{ row }">
                <el-tag :type="row.required ? 'success' : 'info'">
                  {{ row.required ? "必填" : "选填" }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";

// 搜索表单
const searchForm = reactive({
  templateName: "",
  pipelineType: "",
  status: "",
});

// 模版数据
const templateData = ref([
  {
    id: 1,
    templateName: "供水管网标准巡检模版",
    pipelineType: "water_supply",
    itemCount: 15,
    estimatedDuration: 60,
    status: "active",
    createTime: "2024-01-01",
    creator: "管理员",
    checkItems: [
      {
        category: "外观检查",
        item: "管道表面状况",
        method: "目视检查",
        standard: "无裂缝、无腐蚀、无变形",
        required: true,
      },
      {
        category: "压力检查",
        item: "管道压力测试",
        method: "压力表检测",
        standard: "压力在正常范围内",
        required: true,
      },
    ],
  },
  {
    id: 2,
    templateName: "排水管网维护模版",
    pipelineType: "drainage",
    itemCount: 12,
    estimatedDuration: 45,
    status: "active",
    createTime: "2024-01-02",
    creator: "张三",
    checkItems: [],
  },
]);

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0,
});

// 详情对话框
const detailDialogVisible = ref(false);
const currentTemplate = ref<any>(null);

// 获取类型文本
const getTypeText = (type: string) => {
  const texts: Record<string, string> = {
    water_supply: "供水管网",
    drainage: "排水管网",
    gas: "燃气管网",
  };
  return texts[type] || "其他";
};

// 创建模版
const handleCreateTemplate = () => {
  console.log("创建巡检模版");
};

// 搜索
const handleSearch = () => {
  console.log("搜索模版", searchForm);
  loadTemplateData();
};

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    templateName: "",
    pipelineType: "",
    status: "",
  });
  loadTemplateData();
};

// 查看模版
const handleView = (row: any) => {
  currentTemplate.value = row;
  detailDialogVisible.value = true;
};

// 编辑模版
const handleEdit = (row: any) => {
  console.log("编辑模版", row);
};

// 复制模版
const handleCopy = (row: any) => {
  console.log("复制模版", row);
};

// 删除模版
const handleDelete = (row: any) => {
  console.log("删除模版", row);
};

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size;
  loadTemplateData();
};

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page;
  loadTemplateData();
};

// 加载模版数据
const loadTemplateData = () => {
  console.log("加载模版数据");
  pagination.total = templateData.value.length;
};

onMounted(() => {
  loadTemplateData();
});
</script>

<style scoped>
.pipeline-inspection-template {
  padding: 20px;
}

.page-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}

.template-detail {
  max-height: 500px;
  overflow-y: auto;
}

.detail-section {
  margin-top: 20px;
}

.detail-section h4 {
  margin-bottom: 10px;
  color: #333;
  border-left: 4px solid #409eff;
  padding-left: 10px;
}
</style>
