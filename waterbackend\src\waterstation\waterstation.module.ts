import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MongooseModule } from '@nestjs/mongoose';
import { DeviceDataMinute } from './entities/device-data-minute.entity';
import { DeviceDataHour } from './entities/device-data-hour.entity';
import { DataProcessingLog } from './entities/data-processing-log.entity';
import { DeviceData5s, DeviceData5sSchema } from './schemas/device-data-5s.schema';
import { DeviceDataMinuteService } from './services/device-data-minute.service';
import { DeviceDataHourService } from './services/device-data-hour.service';
import { DeviceData5sService } from './services/device-data-5s.service';
import { StationRuntimeService } from './services/station-runtime.service';
import { DeviceDataMinuteController } from './controllers/device-data-minute.controller';
import { DeviceDataHourController } from './controllers/device-data-hour.controller';
import { DeviceData5sController } from './controllers/device-data-5s.controller';
import { StationRuntimeController } from './controllers/station-runtime.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      DeviceDataMinute,
      DeviceDataHour,
      DataProcessingLog
    ]),
    MongooseModule.forFeature([
      { name: DeviceData5s.name, schema: DeviceData5sSchema }
    ])
  ],
  controllers: [
    DeviceDataMinuteController,
    DeviceDataHourController,
    DeviceData5sController,
    StationRuntimeController
  ],
  providers: [
    DeviceDataMinuteService,
    DeviceDataHourService,
    DeviceData5sService,
    StationRuntimeService
  ],
  exports: [
    DeviceDataMinuteService,
    DeviceDataHourService,
    DeviceData5sService,
    StationRuntimeService
  ]
})
export class WaterstationModule {}