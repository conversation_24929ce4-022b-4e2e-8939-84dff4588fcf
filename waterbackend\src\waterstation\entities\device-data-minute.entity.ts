import { ApiProperty } from '@nestjs/swagger';
import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

@Entity('device_data_minute')
export class DeviceDataMinute {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number;

  @ApiProperty({ description: '设备序列号', example: 'WS_001' })
  @Column('varchar', { length: 50, name: 'device_sn' })
  deviceSn: string;

  @ApiProperty({ description: '分钟时间戳', example: '2025-08-28T13:45:00.000Z' })
  @Column({ type: 'datetime', precision: 3 })
  timestamp: Date;

  @ApiProperty({ description: '平均值数据', example: '{"float1": 25.5, "float2": 30.2}' })
  @Column('json', { name: 'avg_data' })
  avgData: Record<string, any>;

  @ApiProperty({ description: '最大值数据', example: '{"float1": 26.0, "float2": 31.0}' })
  @Column('json', { name: 'max_data' })
  maxData: Record<string, any>;

  @ApiProperty({ description: '最小值数据', example: '{"float1": 25.0, "float2": 29.5}' })
  @Column('json', { name: 'min_data' })
  minData: Record<string, any>;

  @ApiProperty({ description: '原始样本数量', example: 12 })
  @Column('int', { name: 'sample_count' })
  sampleCount: number;

  @ApiProperty({ description: '记录创建时间' })
  @Column({ 
    type: 'datetime', 
    precision: 3, 
    name: 'created_at',
    default: () => 'CURRENT_TIMESTAMP(3)' 
  })
  createdAt: Date;
}