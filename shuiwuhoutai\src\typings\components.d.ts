/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module "vue" {
  export interface GlobalComponents {
    AppLink: (typeof import("./../components/AppLink/index.vue"))["default"];
    AppMain: (typeof import("./../layout/components/AppMain/index.vue"))["default"];
    BarChart: (typeof import("./../views/dashboard/components/BarChart.vue"))["default"];
    Breadcrumb: (typeof import("./../components/Breadcrumb/index.vue"))["default"];
    DeptTree: (typeof import("./../views/system/user/components/dept-tree.vue"))["default"];
    Dictionary: (typeof import("./../components/Dictionary/index.vue"))["default"];
    DictItem: (typeof import("./../views/system/dict/components/dict-item.vue"))["default"];
    ElBreadcrumb: (typeof import("element-plus/es"))["ElBreadcrumb"];
    ElBreadcrumbItem: (typeof import("element-plus/es"))["ElBreadcrumbItem"];
    ElButton: (typeof import("element-plus/es"))["ElButton"];
    ElCard: (typeof import("element-plus/es"))["ElCard"];
    ElCheckbox: (typeof import("element-plus/es"))["ElCheckbox"];
    ElCheckboxGroup: (typeof import("element-plus/es"))["ElCheckboxGroup"];
    ElCol: (typeof import("element-plus/es"))["ElCol"];
    ElColorPicker: (typeof import("element-plus/es"))["ElColorPicker"];
    ElConfigProvider: (typeof import("element-plus/es"))["ElConfigProvider"];
    ElDatePicker: (typeof import("element-plus/es"))["ElDatePicker"];
    ElDialog: (typeof import("element-plus/es"))["ElDialog"];
    ElDivider: (typeof import("element-plus/es"))["ElDivider"];
    ElDrawer: (typeof import("element-plus/es"))["ElDrawer"];
    ElDropdown: (typeof import("element-plus/es"))["ElDropdown"];
    ElDropdownItem: (typeof import("element-plus/es"))["ElDropdownItem"];
    ElDropdownMenu: (typeof import("element-plus/es"))["ElDropdownMenu"];
    ElForm: (typeof import("element-plus/es"))["ElForm"];
    ElFormItem: (typeof import("element-plus/es"))["ElFormItem"];
    ElIcon: (typeof import("element-plus/es"))["ElIcon"];
    ElImage: (typeof import("element-plus/es"))["ElImage"];
    ElInput: (typeof import("element-plus/es"))["ElInput"];
    ElInputNumber: (typeof import("element-plus/es"))["ElInputNumber"];
    ElLink: (typeof import("element-plus/es"))["ElLink"];
    ElMenu: (typeof import("element-plus/es"))["ElMenu"];
    ElMenuItem: (typeof import("element-plus/es"))["ElMenuItem"];
    ElOption: (typeof import("element-plus/es"))["ElOption"];
    ElPagination: (typeof import("element-plus/es"))["ElPagination"];
    ElPopover: (typeof import("element-plus/es"))["ElPopover"];
    ElRadio: (typeof import("element-plus/es"))["ElRadio"];
    ElRadioGroup: (typeof import("element-plus/es"))["ElRadioGroup"];
    ElRow: (typeof import("element-plus/es"))["ElRow"];
    ElScrollbar: (typeof import("element-plus/es"))["ElScrollbar"];
    ElSelect: (typeof import("element-plus/es"))["ElSelect"];
    ElStatistic: (typeof import("element-plus/es"))["ElStatistic"];
    ElSubMenu: (typeof import("element-plus/es"))["ElSubMenu"];
    ElSwitch: (typeof import("element-plus/es"))["ElSwitch"];
    ElTable: (typeof import("element-plus/es"))["ElTable"];
    ElTableColumn: (typeof import("element-plus/es"))["ElTableColumn"];
    ElTag: (typeof import("element-plus/es"))["ElTag"];
    ElTooltip: (typeof import("element-plus/es"))["ElTooltip"];
    ElTree: (typeof import("element-plus/es"))["ElTree"];
    ElTreeSelect: (typeof import("element-plus/es"))["ElTreeSelect"];
    ElUpload: (typeof import("element-plus/es"))["ElUpload"];
    ElWatermark: (typeof import("element-plus/es"))["ElWatermark"];
    FunnelChart: (typeof import("./../views/dashboard/components/FunnelChart.vue"))["default"];
    GithubCorner: (typeof import("./../components/GithubCorner/index.vue"))["default"];
    Hamburger: (typeof import("./../components/Hamburger/index.vue"))["default"];
    IconSelect: (typeof import("./../components/IconSelect/index.vue"))["default"];
    IEpArrowDown: (typeof import("~icons/ep/arrow-down"))["default"];
    IEpArrowUp: (typeof import("~icons/ep/arrow-up"))["default"];
    IEpClose: (typeof import("~icons/ep/close"))["default"];
    IEpDelete: (typeof import("~icons/ep/delete"))["default"];
    IEpDownload: (typeof import("~icons/ep/download"))["default"];
    IEpEdit: (typeof import("~icons/ep/edit"))["default"];
    IEpPlus: (typeof import("~icons/ep/plus"))["default"];
    IEpRefresh: (typeof import("~icons/ep/refresh"))["default"];
    IEpRefreshLeft: (typeof import("~icons/ep/refresh-left"))["default"];
    IEpSearch: (typeof import("~icons/ep/search"))["default"];
    IEpSetting: (typeof import("~icons/ep/setting"))["default"];
    IEpTop: (typeof import("~icons/ep/top"))["default"];
    IEpUploadFilled: (typeof import("~icons/ep/upload-filled"))["default"];
    LangSelect: (typeof import("./../components/LangSelect/index.vue"))["default"];
    LayoutSelect: (typeof import("./../layout/components/Settings/components/LayoutSelect.vue"))["default"];
    MultiUpload: (typeof import("./../components/Upload/MultiUpload.vue"))["default"];
    NavBar: (typeof import("./../layout/components/NavBar/index.vue"))["default"];
    NavbarLeft: (typeof import("./../layout/components/NavBar/components/NavbarLeft.vue"))["default"];
    NavbarRight: (typeof import("./../layout/components/NavBar/components/NavbarRight.vue"))["default"];
    PageContent: (typeof import("./../components/PageContent/index.vue"))["default"];
    PageModal: (typeof import("./../components/PageModal/index.vue"))["default"];
    PageSearch: (typeof import("./../components/PageSearch/index.vue"))["default"];
    Pagination: (typeof import("./../components/Pagination/index.vue"))["default"];
    PieChart: (typeof import("./../views/dashboard/components/PieChart.vue"))["default"];
    RadarChart: (typeof import("./../views/dashboard/components/RadarChart.vue"))["default"];
    RouterLink: (typeof import("vue-router"))["RouterLink"];
    RouterView: (typeof import("vue-router"))["RouterView"];
    Settings: (typeof import("./../layout/components/Settings/index.vue"))["default"];
    Sidebar: (typeof import("./../layout/components/Sidebar/index.vue"))["default"];
    SidebarLogo: (typeof import("./../layout/components/Sidebar/components/SidebarLogo.vue"))["default"];
    SidebarMenu: (typeof import("./../layout/components/Sidebar/components/SidebarMenu.vue"))["default"];
    SidebarMenuItem: (typeof import("./../layout/components/Sidebar/components/SidebarMenuItem.vue"))["default"];
    SidebarMenuItemTitle: (typeof import("./../layout/components/Sidebar/components/SidebarMenuItemTitle.vue"))["default"];
    SidebarMixTopMenu: (typeof import("./../layout/components/Sidebar/components/SidebarMixTopMenu.vue"))["default"];
    SingleUpload: (typeof import("./../components/Upload/SingleUpload.vue"))["default"];
    SizeSelect: (typeof import("./../components/SizeSelect/index.vue"))["default"];
    SvgIcon: (typeof import("./../components/SvgIcon/index.vue"))["default"];
    TableSelect: (typeof import("./../components/TableSelect/index.vue"))["default"];
    TagsView: (typeof import("./../layout/components/TagsView/index.vue"))["default"];
    ThemeColorPicker: (typeof import("./../layout/components/Settings/components/ThemeColorPicker.vue"))["default"];
    WangEditor: (typeof import("./../components/WangEditor/index.vue"))["default"];
  }
  export interface ComponentCustomProperties {
    vLoading: (typeof import("element-plus/es"))["ElLoadingDirective"];
  }
}
