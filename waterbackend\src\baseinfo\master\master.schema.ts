import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type MasterDocument = Master & Document;

@Schema()
export class Master extends Document {
  @Prop()
  title: string;
  @Prop()
  userop: number;
  @Prop()
  masterop: number;

  @Prop()
  loginlogop: number;
  @Prop()
  partymemberop: number;
  @Prop()
  partygroupop: number;
  @Prop()
  partyactionop: number;
  @Prop()
  partyactionclassifyop: number;
  @Prop()
  volunteerop: number;
  @Prop()
  volunteergroupop: number;
  @Prop()
  volunteeractionop: number;
  @Prop()
  volunteeractionclassifyop: number;
  @Prop()
  articleop: number;
  @Prop()
  peopleop: number;
  @Prop()
  buildingop: number;
  @Prop()
  spotop: number;
  @Prop()
  personnelop: number;
  @Prop()
  waringpersonnelop: number;
  @Prop()
  localepersonnelop: number;
  @Prop()
  personnellogop: number;
  @Prop()
  cameraop: number;
  @Prop()
  modelinfoop: number;
  @Prop()
  modeltypeop: number;
}

export const MasterSchema = SchemaFactory.createForClass(Master);
