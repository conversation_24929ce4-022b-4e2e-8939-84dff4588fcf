import { UserService } from '../baseinfo/user/user.service';
import { JwtService } from '@nestjs/jwt';
import { Repository } from 'typeorm';
import { User } from '../baseinfo/user/entities/user.entity';
export declare class AuthService {
    private readonly userService;
    private readonly jwtService;
    private readonly userRepository;
    constructor(userService: UserService, jwtService: JwtService, userRepository: Repository<User>);
    validateUser(username: string, pass: string): Promise<any>;
    login(user: any, ip: string): Promise<{
        code: number;
        data: {
            accessToken: string;
            tokenType: string;
            refreshToken: string;
            expires: number;
        };
        msg: string;
    }>;
    logout(user: any): Promise<boolean>;
}
