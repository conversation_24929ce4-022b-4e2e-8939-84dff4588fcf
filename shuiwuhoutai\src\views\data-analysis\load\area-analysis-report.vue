<template>
  <div class="area-load-analysis">
    <el-card shadow="hover" class="page-header">
      <template #header>
        <div class="card-header">
          <div class="header-actions">
            <el-button
              type="primary"
              size="small"
              @click="generateAreaLoadReport"
              >生成区域报表</el-button
            >
            <el-button type="success" size="small" @click="compareAreaLoad"
              >区域对比</el-button
            >
          </div>
        </div>
      </template>

      <!-- 查询配置表单 -->
      <el-form :inline="true" :model="queryForm" class="query-form">
        <el-form-item label="选择区域">
          <el-select
            v-model="queryForm.areas"
            placeholder="请选择区域"
            multiple
            clearable
          >
            <el-option label="浦南区域" value="punan" />
            <el-option label="史北区域" value="shibei" />
            <el-option label="东部区域" value="east" />
            <el-option label="西部区域" value="west" />
          </el-select>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="分析类型">
          <el-select
            v-model="queryForm.analysisType"
            placeholder="请选择分析类型"
          >
            <el-option label="负荷总量分析" value="total_load" />
            <el-option label="负荷密度分析" value="load_density" />
            <el-option label="峰谷负荷分析" value="peak_valley" />
            <el-option label="负荷均衡性分析" value="balance" />
          </el-select>
        </el-form-item>

        <el-form-item label="统计粒度">
          <el-select v-model="queryForm.granularity" placeholder="请选择粒度">
            <el-option label="小时" value="hour" />
            <el-option label="日" value="day" />
            <el-option label="周" value="week" />
            <el-option label="月" value="month" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 区域负荷概览 -->
    <div class="area-load-overview">
      <el-row :gutter="20">
        <el-col
          :span="6"
          v-for="(area, index) in areaLoadOverview"
          :key="index"
        >
          <el-card shadow="hover" :class="['area-load-card', area.code]">
            <div class="area-header">
              <div class="area-name">{{ area.name }}</div>
              <div class="area-level">
                <el-tag :type="getLoadLevelType(area.loadLevel)" size="small">
                  {{ area.loadLevel }}
                </el-tag>
              </div>
            </div>
            <div class="area-load-metrics">
              <div class="metric-row">
                <span class="metric-label">当前负荷:</span>
                <span class="metric-value">{{ area.currentLoad }} MW</span>
              </div>
              <div class="metric-row">
                <span class="metric-label">峰值负荷:</span>
                <span class="metric-value">{{ area.peakLoad }} MW</span>
              </div>
              <div class="metric-row">
                <span class="metric-label">负荷率:</span>
                <span class="metric-value">{{ area.loadFactor }}%</span>
              </div>
              <div class="metric-row">
                <span class="metric-label">站点数量:</span>
                <span class="metric-value">{{ area.stationCount }}</span>
              </div>
              <div class="metric-row">
                <span class="metric-label">同比变化:</span>
                <span
                  :class="[
                    'metric-value',
                    area.yearOnYearChange >= 0 ? 'increase' : 'decrease',
                  ]"
                >
                  {{ area.yearOnYearChange >= 0 ? "+" : ""
                  }}{{ area.yearOnYearChange }}%
                </span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 区域负荷对比分析图表 -->
    <div class="area-load-charts">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>区域负荷趋势对比</span>
                <div>
                  <el-select
                    v-model="trendMetric"
                    size="small"
                    style="margin-right: 10px"
                  >
                    <el-option label="总负荷" value="total_load" />
                    <el-option label="峰值负荷" value="peak_load" />
                    <el-option label="负荷率" value="load_factor" />
                    <el-option label="负荷密度" value="load_density" />
                  </el-select>
                  <el-button-group size="small">
                    <el-button
                      :type="trendPeriod === '24h' ? 'primary' : ''"
                      @click="trendPeriod = '24h'"
                      >24小时</el-button
                    >
                    <el-button
                      :type="trendPeriod === '7d' ? 'primary' : ''"
                      @click="trendPeriod = '7d'"
                      >7天</el-button
                    >
                    <el-button
                      :type="trendPeriod === '30d' ? 'primary' : ''"
                      @click="trendPeriod = '30d'"
                      >30天</el-button
                    >
                  </el-button-group>
                </div>
              </div>
            </template>
            <div id="areaLoadTrendChart" style="height: 400px"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="8">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>区域负荷占比</span>
              </div>
            </template>
            <div id="areaLoadProportionChart" style="height: 350px"></div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>区域负荷密度分布</span>
              </div>
            </template>
            <div id="areaLoadDensityChart" style="height: 350px"></div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>区域负荷均衡度</span>
              </div>
            </template>
            <div id="areaLoadBalanceChart" style="height: 350px"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 区域负荷排行榜 -->
    <el-row :gutter="20" style="margin-bottom: 20px">
      <el-col :span="8">
        <el-card shadow="hover" class="ranking-card">
          <template #header>
            <div class="card-header">
              <span>负荷总量排行</span>
              <el-tag type="info" size="small">MW</el-tag>
            </div>
          </template>
          <div class="ranking-list">
            <div
              v-for="(item, index) in totalLoadRanking"
              :key="index"
              class="ranking-item"
            >
              <div class="ranking-number" :class="'rank-' + (index + 1)">
                {{ index + 1 }}
              </div>
              <div class="ranking-content">
                <div class="ranking-name">{{ item.areaName }}</div>
                <div class="ranking-value">{{ item.totalLoad }} MW</div>
              </div>
              <div class="ranking-trend">
                <el-tag
                  :type="item.trend === 'up' ? 'danger' : 'success'"
                  size="small"
                >
                  {{ item.trend === "up" ? "↑" : "↓" }} {{ item.change }}%
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover" class="ranking-card">
          <template #header>
            <div class="card-header">
              <span>负荷率排行</span>
              <el-tag type="success" size="small">%</el-tag>
            </div>
          </template>
          <div class="ranking-list">
            <div
              v-for="(item, index) in loadFactorRanking"
              :key="index"
              class="ranking-item"
            >
              <div class="ranking-number" :class="'rank-' + (index + 1)">
                {{ index + 1 }}
              </div>
              <div class="ranking-content">
                <div class="ranking-name">{{ item.areaName }}</div>
                <div class="ranking-value">{{ item.loadFactor }}%</div>
              </div>
              <div class="ranking-badge">
                <el-tag type="success" size="small">优秀</el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover" class="ranking-card">
          <template #header>
            <div class="card-header">
              <span>负荷增长率排行</span>
              <el-tag type="warning" size="small">同比</el-tag>
            </div>
          </template>
          <div class="ranking-list">
            <div
              v-for="(item, index) in growthRateRanking"
              :key="index"
              class="ranking-item"
            >
              <div class="ranking-number" :class="'rank-' + (index + 1)">
                {{ index + 1 }}
              </div>
              <div class="ranking-content">
                <div class="ranking-name">{{ item.areaName }}</div>
                <div class="ranking-value">{{ item.growthRate }}%</div>
              </div>
              <div class="ranking-badge">
                <el-tag
                  :type="item.growthRate > 5 ? 'danger' : 'warning'"
                  size="small"
                >
                  {{ item.growthRate > 5 ? "快速" : "稳定" }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 区域负荷详细分析表格 -->
    <el-card shadow="hover" class="table-card">
      <template #header>
        <div class="card-header">
          <span>区域负荷分析详情</span>
          <div class="table-actions">
            <el-button size="small" @click="exportAreaLoadReport"
              >导出报表</el-button
            >
            <el-button size="small" type="warning" @click="setAreaLoadTarget"
              >设置目标</el-button
            >
            <el-button size="small" type="primary" @click="optimizeAreaLoad"
              >优化配置</el-button
            >
          </div>
        </div>
      </template>

      <el-table :data="areaLoadDetailData" stripe style="width: 100%" border>
        <el-table-column type="selection" width="55" />
        <el-table-column
          prop="areaName"
          label="区域名称"
          width="100"
          fixed="left"
        />
        <el-table-column prop="stationCount" label="站点数量" width="80" />
        <el-table-column prop="totalCapacity" label="总容量(MW)" width="110" />
        <el-table-column prop="currentLoad" label="当前负荷(MW)" width="120" />
        <el-table-column prop="peakLoad" label="峰值负荷(MW)" width="120" />
        <el-table-column prop="avgLoad" label="平均负荷(MW)" width="120" />
        <el-table-column prop="minLoad" label="最小负荷(MW)" width="120" />
        <el-table-column prop="loadFactor" label="负荷率(%)" width="100">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.loadFactor"
              :color="getLoadFactorColor(scope.row.loadFactor)"
            />
          </template>
        </el-table-column>
        <el-table-column
          prop="loadDensity"
          label="负荷密度(MW/km²)"
          width="140"
        />
        <el-table-column
          prop="peakValleyRatio"
          label="峰谷差率(%)"
          width="110"
        />
        <el-table-column prop="balanceIndex" label="均衡指数" width="100" />
        <el-table-column
          prop="utilizationRate"
          label="设备利用率(%)"
          width="120"
        />
        <el-table-column prop="growthRate" label="同比增长(%)" width="110">
          <template #default="scope">
            <span :class="scope.row.growthRate >= 0 ? 'increase' : 'decrease'">
              {{ scope.row.growthRate >= 0 ? "+" : ""
              }}{{ scope.row.growthRate }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="loadLevel" label="负荷等级" width="100">
          <template #default="scope">
            <el-tag :type="getLoadLevelType(scope.row.loadLevel)">
              {{ scope.row.loadLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="alertStatus" label="预警状态" width="100">
          <template #default="scope">
            <el-tag :type="getAlertStatusType(scope.row.alertStatus)">
              {{ scope.row.alertStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="200">
          <template #default="scope">
            <el-button size="small" @click="viewAreaLoadDetail(scope.row)"
              >详情</el-button
            >
            <el-button
              size="small"
              type="warning"
              @click="adjustAreaLoad(scope.row)"
              >调节</el-button
            >
            <el-button
              size="small"
              type="primary"
              @click="generateAreaDetailReport(scope.row)"
              >报告</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[5, 10, 20, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 区域负荷分析结论 -->
    <el-card shadow="hover" class="analysis-conclusion-card">
      <template #header>
        <div class="card-header">
          <span>区域负荷分析结论</span>
          <span class="analysis-time">分析时间: {{ analysisTime }}</span>
        </div>
      </template>

      <el-row :gutter="30">
        <el-col :span="8">
          <div class="conclusion-section">
            <h4>📊 负荷特征分析</h4>
            <ul class="analysis-list">
              <li v-for="(feature, index) in loadFeatures" :key="index">
                {{ feature }}
              </li>
            </ul>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="conclusion-section">
            <h4>⚠️ 存在问题</h4>
            <ul class="problem-list">
              <li v-for="(problem, index) in existingProblems" :key="index">
                {{ problem }}
              </li>
            </ul>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="conclusion-section">
            <h4>🎯 优化建议</h4>
            <ul class="optimization-list">
              <li
                v-for="(suggestion, index) in optimizationSuggestions"
                :key="index"
              >
                {{ suggestion }}
              </li>
            </ul>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";

// 查询表单数据
const queryForm = reactive({
  areas: ["punan", "shibei"],
  analysisType: "total_load",
  granularity: "day",
});

// 时间范围
const dateRange = ref([]);

// 图表配置
const trendMetric = ref("total_load");
const trendPeriod = ref("24h");

// 分析时间
const analysisTime = ref("2024-01-20 14:30:25");

// 区域负荷概览数据
const areaLoadOverview = ref([
  {
    code: "punan",
    name: "浦南区域",
    loadLevel: "正常",
    currentLoad: 15.68,
    peakLoad: 18.92,
    loadFactor: 82.8,
    stationCount: 28,
    yearOnYearChange: -2.5,
  },
  {
    code: "shibei",
    name: "史北区域",
    loadLevel: "较高",
    currentLoad: 22.45,
    peakLoad: 25.8,
    loadFactor: 87.1,
    stationCount: 17,
    yearOnYearChange: 5.8,
  },
  {
    code: "east",
    name: "东部区域",
    loadLevel: "正常",
    currentLoad: 12.36,
    peakLoad: 14.75,
    loadFactor: 83.7,
    stationCount: 22,
    yearOnYearChange: 1.2,
  },
  {
    code: "west",
    name: "西部区域",
    loadLevel: "正常",
    currentLoad: 18.92,
    peakLoad: 21.5,
    loadFactor: 88.0,
    stationCount: 19,
    yearOnYearChange: -0.8,
  },
]);

// 排行榜数据
const totalLoadRanking = ref([
  { areaName: "史北区域", totalLoad: 22.45, trend: "up", change: 5.8 },
  { areaName: "西部区域", totalLoad: 18.92, trend: "down", change: 0.8 },
  { areaName: "浦南区域", totalLoad: 15.68, trend: "down", change: 2.5 },
  { areaName: "东部区域", totalLoad: 12.36, trend: "up", change: 1.2 },
]);

const loadFactorRanking = ref([
  { areaName: "西部区域", loadFactor: 88.0 },
  { areaName: "史北区域", loadFactor: 87.1 },
  { areaName: "东部区域", loadFactor: 83.7 },
  { areaName: "浦南区域", loadFactor: 82.8 },
]);

const growthRateRanking = ref([
  { areaName: "史北区域", growthRate: 5.8 },
  { areaName: "东部区域", growthRate: 1.2 },
  { areaName: "西部区域", growthRate: -0.8 },
  { areaName: "浦南区域", growthRate: -2.5 },
]);

// 区域负荷详细数据
const areaLoadDetailData = ref([
  {
    areaName: "浦南区域",
    stationCount: 28,
    totalCapacity: 25.6,
    currentLoad: 15.68,
    peakLoad: 18.92,
    avgLoad: 14.25,
    minLoad: 8.36,
    loadFactor: 83,
    loadDensity: 2.35,
    peakValleyRatio: 56.2,
    balanceIndex: 0.78,
    utilizationRate: 87.5,
    growthRate: -2.5,
    loadLevel: "正常",
    alertStatus: "正常",
  },
  {
    areaName: "史北区域",
    stationCount: 17,
    totalCapacity: 32.8,
    currentLoad: 22.45,
    peakLoad: 25.8,
    avgLoad: 19.86,
    minLoad: 12.58,
    loadFactor: 87,
    loadDensity: 3.12,
    peakValleyRatio: 51.2,
    balanceIndex: 0.82,
    utilizationRate: 92.1,
    growthRate: 5.8,
    loadLevel: "较高",
    alertStatus: "预警",
  },
]);

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 4,
});

// 负荷特征分析
const loadFeatures = ref([
  "史北区域负荷密度最高，达3.12MW/km²",
  "西部区域负荷率最优，达88.0%",
  "各区域峰谷差率保持在50%-60%之间",
  "整体负荷增长趋势平稳，年增长率约1.4%",
]);

// 存在问题
const existingProblems = ref([
  "史北区域负荷增长过快，同比增长5.8%",
  "浦南区域负荷下降，可能存在设备闲置",
  "部分区域峰谷差较大，调峰压力明显",
  "区域间负荷分布不均，资源配置待优化",
]);

// 优化建议
const optimizationSuggestions = ref([
  "加强史北区域负荷监控，防止过载风险",
  "优化浦南区域设备配置，提高利用率",
  "推进区域间负荷调配，实现资源共享",
  "建立负荷预测模型，提前制定调度策略",
]);

// 获取负荷等级类型
const getLoadLevelType = (level) => {
  const typeMap = {
    正常: "success",
    较高: "warning",
    过高: "danger",
    较低: "info",
  };
  return typeMap[level] || "info";
};

// 获取负荷率颜色
const getLoadFactorColor = (factor) => {
  if (factor >= 85) return "#67c23a";
  if (factor >= 70) return "#e6a23c";
  return "#f56c6c";
};

// 获取预警状态类型
const getAlertStatusType = (status) => {
  const typeMap = {
    正常: "success",
    预警: "warning",
    告警: "danger",
  };
  return typeMap[status] || "info";
};

// 查询处理
const handleQuery = () => {
  ElMessage.success("查询成功");
};

// 重置查询
const resetQuery = () => {
  queryForm.areas = ["punan", "shibei"];
  queryForm.analysisType = "total_load";
  queryForm.granularity = "day";
  dateRange.value = [];
  ElMessage.info("已重置查询条件");
};

// 生成区域负荷报表
const generateAreaLoadReport = () => {
  ElMessage.success("区域负荷报表生成中，请稍候...");
};

// 区域负荷对比
const compareAreaLoad = () => {
  ElMessage.info("区域负荷对比分析功能开发中");
};

// 查看区域负荷详情
const viewAreaLoadDetail = (row) => {
  ElMessage.info(`查看 ${row.areaName} 的负荷详情`);
};

// 调节区域负荷
const adjustAreaLoad = (row) => {
  ElMessage.success(`正在为 ${row.areaName} 调节负荷`);
};

// 生成区域详细报告
const generateAreaDetailReport = (row) => {
  ElMessage.success(`正在为 ${row.areaName} 生成详细报告`);
};

// 导出区域负荷报表
const exportAreaLoadReport = () => {
  ElMessage.success("区域负荷报表导出成功");
};

// 设置区域负荷目标
const setAreaLoadTarget = () => {
  ElMessage.info("区域负荷目标设置功能开发中");
};

// 优化区域负荷
const optimizeAreaLoad = () => {
  ElMessage.success("区域负荷优化配置生成中");
};

// 分页处理
const handleSizeChange = (size) => {
  pagination.pageSize = size;
};

const handleCurrentChange = (page) => {
  pagination.currentPage = page;
};

// 组件挂载
onMounted(() => {
  initCharts();
});

// 初始化图表
const initCharts = () => {
  console.log("初始化区域负荷分析图表");
};
</script>

<style scoped>
.area-load-analysis {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.query-form {
  margin-bottom: 0;
}

.area-load-overview {
  margin-bottom: 20px;
}

.area-load-card {
  cursor: pointer;
  transition: all 0.3s;
  border-left: 4px solid;
}

.area-load-card.punan {
  border-left-color: #67c23a;
}

.area-load-card.shibei {
  border-left-color: #e6a23c;
}

.area-load-card.east {
  border-left-color: #409eff;
}

.area-load-card.west {
  border-left-color: #f56c6c;
}

.area-load-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.area-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.area-name {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.area-load-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-size: 14px;
  color: #606266;
}

.metric-value {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.metric-value.increase {
  color: #f56c6c;
}

.metric-value.decrease {
  color: #67c23a;
}

.area-load-charts {
  margin-bottom: 20px;
}

.chart-card {
  min-height: 450px;
}

.ranking-card {
  height: 400px;
  margin-bottom: 20px;
}

.ranking-list {
  padding: 10px 0;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-number {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  border-radius: 50%;
  margin-right: 15px;
}

.rank-1 {
  background-color: #ffd700;
  color: #fff;
}

.rank-2 {
  background-color: #c0c0c0;
  color: #fff;
}

.rank-3 {
  background-color: #cd7f32;
  color: #fff;
}

.rank-4 {
  background-color: #f5f5f5;
  color: #666;
}

.ranking-content {
  flex: 1;
}

.ranking-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.ranking-value {
  font-size: 14px;
  color: #666;
}

.ranking-trend,
.ranking-badge {
  margin-left: 10px;
}

.table-card {
  margin-bottom: 20px;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.increase {
  color: #f56c6c;
}

.decrease {
  color: #67c23a;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.analysis-conclusion-card {
  margin-bottom: 20px;
}

.analysis-time {
  font-size: 14px;
  color: #666;
  background: #f5f5f5;
  padding: 4px 12px;
  border-radius: 4px;
}

.conclusion-section h4 {
  color: #303133;
  margin-bottom: 15px;
  font-size: 16px;
}

.analysis-list,
.problem-list,
.optimization-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.analysis-list li,
.problem-list li,
.optimization-list li {
  margin-bottom: 12px;
  font-size: 14px;
  line-height: 1.5;
  padding-left: 15px;
  position: relative;
}

.analysis-list li::before {
  content: "📊";
  position: absolute;
  left: 0;
}

.problem-list li::before {
  content: "⚠️";
  position: absolute;
  left: 0;
}

.optimization-list li::before {
  content: "🎯";
  position: absolute;
  left: 0;
}
</style>
