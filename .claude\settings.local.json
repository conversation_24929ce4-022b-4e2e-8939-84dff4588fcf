{"permissions": {"allow": ["<PERSON><PERSON>(docker-compose:*)", "Bash(docker logs:*)", "Bash(rm:*)", "<PERSON><PERSON>(docker exec:*)", "Bash(pip install:*)", "<PERSON><PERSON>(python test:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(timeout:*)", "Bash(del sql_app.db)", "Bash(move simulation_test.py simulation_test_backup.py)", "Bash(move simulation_test_updated.py simulation_test.py)", "<PERSON><PERSON>(mv:*)", "Bash(find:*)", "<PERSON><PERSON>(tasklist:*)", "<PERSON><PERSON>(taskkill:*)", "Bash(cmd /c:*)", "<PERSON><PERSON>(powershell:*)", "<PERSON><PERSON>(del:*)", "<PERSON>sh(New-Item -ItemType Directory -Force -Path \"$env:USERPROFILE\\.docker\")", "Bash(copy:*)", "Bash(ping:*)", "Bash(docker volume rm:*)", "Bash(docker volume:*)", "Bash(if exist \"data\\rabbitmq_data\" rmdir /s /q \"data\\rabbitmq_data\")", "<PERSON><PERSON>(dir:*)", "Bash(npm install:*)", "Bash(npm run start:dev:*)"], "deny": [], "defaultMode": "acceptEdits", "additionalDirectories": ["f:\\水利站"]}}