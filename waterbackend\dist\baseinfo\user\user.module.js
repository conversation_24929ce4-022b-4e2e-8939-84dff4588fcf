"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserModule = void 0;
const common_1 = require("@nestjs/common");
const user_service_1 = require("./user.service");
const user_controller_1 = require("./user.controller");
const user_entity_1 = require("./entities/user.entity");
const typeorm_1 = require("@nestjs/typeorm");
const jwt_1 = require("@nestjs/jwt");
const jwt_strategy_1 = require("../../auth/jwt.strategy");
const user_seed_1 = require("./user.seed");
let UserModule = class UserModule {
    constructor(userSeedService) {
        this.userSeedService = userSeedService;
    }
    async onModuleInit() {
        await this.userSeedService.seed();
    }
};
UserModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([user_entity_1.User]),
            jwt_1.JwtModule.register({
                secret: process.env.JWT_SECRET || 'your-secret-key',
                signOptions: { expiresIn: '24h' },
            }),
        ],
        controllers: [user_controller_1.UserController],
        providers: [
            jwt_strategy_1.JwtStrategy,
            user_service_1.UserService,
            user_seed_1.UserSeedService,
        ],
        exports: [user_service_1.UserService],
    }),
    __metadata("design:paramtypes", [user_seed_1.UserSeedService])
], UserModule);
exports.UserModule = UserModule;
//# sourceMappingURL=user.module.js.map