<template>
  <div class="alarm-code-statistics">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-button type="primary" @click="refreshData">刷新</el-button>
        </div>
      </template>

      <!-- 告警代码统计图表 -->
      <div id="codeChart" style="height: 300px; margin-bottom: 20px"></div>

      <!-- 告警代码统计表格 -->
      <el-table :data="codeStatistics" style="width: 100%" v-loading="loading">
        <el-table-column prop="alarmCode" label="告警代码" width="120" />
        <el-table-column prop="alarmName" label="告警名称" />
        <el-table-column prop="count" label="出现次数" width="100" />
        <el-table-column prop="percentage" label="占比" width="100">
          <template #default="scope">
            <span>{{ scope.row.percentage }}%</span>
          </template>
        </el-table-column>
        <el-table-column prop="avgDuration" label="平均持续时间" width="120" />
        <el-table-column
          prop="lastOccurrence"
          label="最近发生时间"
          width="180"
        />
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(scope.row)"
              >查看详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";

// 告警代码统计数据
const codeStatistics = ref([
  {
    alarmCode: "E001",
    alarmName: "压力超限",
    count: 45,
    percentage: 28.8,
    avgDuration: "2.5小时",
    lastOccurrence: "2024-01-15 14:30:00",
  },
  {
    alarmCode: "E002",
    alarmName: "流量异常",
    count: 32,
    percentage: 20.5,
    avgDuration: "1.8小时",
    lastOccurrence: "2024-01-15 13:15:00",
  },
  {
    alarmCode: "E003",
    alarmName: "水位过高",
    count: 28,
    percentage: 17.9,
    avgDuration: "3.2小时",
    lastOccurrence: "2024-01-15 12:45:00",
  },
  {
    alarmCode: "E004",
    alarmName: "设备故障",
    count: 25,
    percentage: 16.0,
    avgDuration: "4.5小时",
    lastOccurrence: "2024-01-15 11:20:00",
  },
  {
    alarmCode: "E005",
    alarmName: "通信中断",
    count: 26,
    percentage: 16.8,
    avgDuration: "0.5小时",
    lastOccurrence: "2024-01-15 10:10:00",
  },
]);

const loading = ref(false);

// 刷新数据
const refreshData = () => {
  loadData();
};

// 查看详情
const handleViewDetail = (row: any) => {
  console.log("查看告警代码详情:", row);
};

// 加载数据
const loadData = () => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
    initChart();
  }, 1000);
};

// 初始化图表
const initChart = () => {
  // 这里可以使用ECharts等图表库来绘制告警代码统计饼图
  console.log("初始化告警代码统计图表");
};

onMounted(() => {
  loadData();
});
</script>

<style scoped>
.alarm-code-statistics {
  padding: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
