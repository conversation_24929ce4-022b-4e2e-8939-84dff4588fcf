import { Injectable } from '@nestjs/common';
import { CreateLoginlogDto } from './dto/create-loginlog.dto';
import { UpdateLoginlogDto } from './dto/update-loginlog.dto';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { Loginlog, LoginlogDocument } from './loginlog.schema';

@Injectable()
export class LoginlogService {
  constructor(
    @InjectModel('Loginlog') private loginlogModel: Model<LoginlogDocument>,
  ) {}
  async create(createLoginlogDto: CreateLoginlogDto): Promise<Loginlog> {
    const createdLoginlog = new this.loginlogModel(createLoginlogDto);
    return createdLoginlog.save();
  }

  findAll(): Promise<Loginlog[]> {
    return this.loginlogModel.find({}, ['title', '_id']).exec();
  }
}
