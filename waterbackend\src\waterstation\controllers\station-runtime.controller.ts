import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiQuery } from '@nestjs/swagger';
import { StationRuntimeService, StationRuntimeInfo } from '../services/station-runtime.service';

@ApiTags('水利站运行时间统计')
@Controller('station-runtime')
export class StationRuntimeController {
  constructor(
    private readonly stationRuntimeService: StationRuntimeService
  ) {}

  @Get('today')
  @ApiOperation({ 
    summary: '获取当天站点运行时间', 
    description: '计算当天各站点的运行时间，通过实际数据点数量计算运行时长' 
  })
  @ApiQuery({ name: 'deviceSn', required: false, description: '设备序列号，不传则返回所有设备' })
  async getTodayRuntime(
    @Query('deviceSn') deviceSn?: string
  ): Promise<StationRuntimeInfo[]> {
    return this.stationRuntimeService.calculateStationRuntime(deviceSn);
  }

  @Get('date')
  @ApiOperation({ 
    summary: '获取指定日期的站点运行时间',
    description: '计算指定日期各站点的运行时间统计' 
  })
  @ApiQuery({ name: 'date', required: true, description: '日期，格式: YYYY-MM-DD', example: '2025-08-28' })
  @ApiQuery({ name: 'deviceSn', required: false, description: '设备序列号，不传则返回所有设备' })
  async getRuntimeByDate(
    @Query('date') date: string,
    @Query('deviceSn') deviceSn?: string
  ): Promise<StationRuntimeInfo[]> {
    return this.stationRuntimeService.calculateStationRuntime(deviceSn, date);
  }

  @Get('range')
  @ApiOperation({ 
    summary: '获取时间段内的站点运行时间',
    description: '计算指定时间段内各站点每天的运行时间统计' 
  })
  @ApiQuery({ name: 'startDate', required: true, description: '开始日期，格式: YYYY-MM-DD' })
  @ApiQuery({ name: 'endDate', required: true, description: '结束日期，格式: YYYY-MM-DD' })
  @ApiQuery({ name: 'deviceSn', required: false, description: '设备序列号，不传则返回所有设备' })
  async getRuntimeByRange(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('deviceSn') deviceSn?: string
  ): Promise<StationRuntimeInfo[]> {
    return this.stationRuntimeService.getStationRuntimeBetweenDates(
      startDate, 
      endDate, 
      deviceSn
    );
  }

  @Get('summary')
  @ApiOperation({ 
    summary: '获取站点运行状态摘要',
    description: '获取当天所有站点的运行状态概览，包括平均运行时间和运行率' 
  })
  @ApiQuery({ name: 'deviceSn', required: false, description: '设备序列号，不传则返回所有设备摘要' })
  async getStationStatusSummary(@Query('deviceSn') deviceSn?: string) {
    return this.stationRuntimeService.getStationStatusSummary(deviceSn);
  }
}