<template>
  <div class="coordinates-audit-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="header-actions">
            <el-button
              type="success"
              @click="handleBatchApprove"
              :disabled="selectedRows.length === 0"
            >
              <el-icon><Check /></el-icon>
              批量通过
            </el-button>
            <el-button
              type="danger"
              @click="handleBatchReject"
              :disabled="selectedRows.length === 0"
            >
              <el-icon><Close /></el-icon>
              批量拒绝
            </el-button>
            <el-button @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form">
        <el-form :model="searchForm" inline>
          <el-form-item label="设施名称">
            <el-input
              v-model="searchForm.facilityName"
              placeholder="请输入设施名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="申请人">
            <el-input
              v-model="searchForm.applicant"
              placeholder="请输入申请人"
              clearable
              style="width: 150px"
            />
          </el-form-item>
          <el-form-item label="审核状态">
            <el-select
              v-model="searchForm.auditStatus"
              placeholder="请选择状态"
              clearable
              style="width: 120px"
            >
              <el-option label="待审核" value="pending" />
              <el-option label="已通过" value="approved" />
              <el-option label="已拒绝" value="rejected" />
            </el-select>
          </el-form-item>
          <el-form-item label="申请时间">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 240px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 审核统计 -->
      <div class="audit-statistics">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card total">
              <div class="stat-icon">
                <el-icon><DataBoard /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.total }}</div>
                <div class="stat-label">总申请数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card pending">
              <div class="stat-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.pending }}</div>
                <div class="stat-label">待审核</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card approved">
              <div class="stat-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.approved }}</div>
                <div class="stat-label">已通过</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card rejected">
              <div class="stat-icon">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.rejected }}</div>
                <div class="stat-label">已拒绝</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table
          ref="tableRef"
          :data="tableData"
          style="width: 100%"
          stripe
          border
          v-loading="tableLoading"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="facilityCode" label="设施编码" width="120" />
          <el-table-column prop="facilityName" label="设施名称" width="150" />
          <el-table-column prop="facilityType" label="设施类型" width="120">
            <template #default="{ row }">
              <el-tag :type="getTypeTagType(row.facilityType)" size="small">
                {{ getTypeLabel(row.facilityType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="原坐标" width="180">
            <template #default="{ row }">
              <div class="coordinate-display">
                <span v-if="row.originalLongitude && row.originalLatitude">
                  {{ row.originalLongitude }}, {{ row.originalLatitude }}
                </span>
                <span v-else class="no-data">无</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="申请坐标" width="180">
            <template #default="{ row }">
              <div class="coordinate-display new">
                {{ row.newLongitude }}, {{ row.newLatitude }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="distance" label="偏移距离" width="100">
            <template #default="{ row }">
              <span v-if="row.distance">{{ row.distance }} 米</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="applicant" label="申请人" width="100" />
          <el-table-column prop="applyTime" label="申请时间" width="150" />
          <el-table-column prop="auditStatus" label="审核状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.auditStatus)" size="small">
                {{ getStatusLabel(row.auditStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="auditor" label="审核人" width="100" />
          <el-table-column prop="auditTime" label="审核时间" width="150" />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                size="small"
                type="primary"
                text
                @click="handleViewDetail(row)"
              >
                <el-icon><View /></el-icon>
                详情
              </el-button>
              <template v-if="row.auditStatus === 'pending'">
                <el-button
                  size="small"
                  type="success"
                  text
                  @click="handleApprove(row)"
                >
                  <el-icon><Check /></el-icon>
                  通过
                </el-button>
                <el-button
                  size="small"
                  type="danger"
                  text
                  @click="handleReject(row)"
                >
                  <el-icon><Close /></el-icon>
                  拒绝
                </el-button>
              </template>
              <template v-else>
                <el-button
                  size="small"
                  type="info"
                  text
                  @click="handleViewAudit(row)"
                >
                  <el-icon><Document /></el-icon>
                  审核记录
                </el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="坐标修改详情" width="800px">
      <div v-if="detailData" class="detail-content">
        <!-- 基本信息 -->
        <el-descriptions title="设施信息" :column="2" border>
          <el-descriptions-item label="设施编码">{{
            detailData.facilityCode
          }}</el-descriptions-item>
          <el-descriptions-item label="设施名称">{{
            detailData.facilityName
          }}</el-descriptions-item>
          <el-descriptions-item label="设施类型">{{
            getTypeLabel(detailData.facilityType)
          }}</el-descriptions-item>
          <el-descriptions-item label="所属区域">{{
            detailData.areaName
          }}</el-descriptions-item>
          <el-descriptions-item label="安装位置">{{
            detailData.location
          }}</el-descriptions-item>
          <el-descriptions-item label="负责人">{{
            detailData.manager
          }}</el-descriptions-item>
        </el-descriptions>

        <!-- 坐标变更信息 -->
        <el-descriptions
          title="坐标变更"
          :column="2"
          border
          style="margin-top: 20px"
        >
          <el-descriptions-item label="原经度">{{
            detailData.originalLongitude || "无"
          }}</el-descriptions-item>
          <el-descriptions-item label="原纬度">{{
            detailData.originalLatitude || "无"
          }}</el-descriptions-item>
          <el-descriptions-item label="新经度">{{
            detailData.newLongitude
          }}</el-descriptions-item>
          <el-descriptions-item label="新纬度">{{
            detailData.newLatitude
          }}</el-descriptions-item>
          <el-descriptions-item label="偏移距离">{{
            detailData.distance ? detailData.distance + " 米" : "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="变更原因" :span="2">{{
            detailData.changeReason || "-"
          }}</el-descriptions-item>
        </el-descriptions>

        <!-- 申请信息 -->
        <el-descriptions
          title="申请信息"
          :column="2"
          border
          style="margin-top: 20px"
        >
          <el-descriptions-item label="申请人">{{
            detailData.applicant
          }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{
            detailData.applyTime
          }}</el-descriptions-item>
          <el-descriptions-item label="审核状态">
            <el-tag :type="getStatusTagType(detailData.auditStatus)">
              {{ getStatusLabel(detailData.auditStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="审核人">{{
            detailData.auditor || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="审核时间">{{
            detailData.auditTime || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="审核备注" :span="2">{{
            detailData.auditRemark || "-"
          }}</el-descriptions-item>
        </el-descriptions>

        <!-- 地图展示 -->
        <div class="map-section" style="margin-top: 20px">
          <h4>位置对比</h4>
          <div class="map-placeholder">
            <el-icon size="60"><Location /></el-icon>
            <p>地图展示区域</p>
            <p>
              原坐标: {{ detailData.originalLongitude || "无" }},
              {{ detailData.originalLatitude || "无" }}
            </p>
            <p>
              新坐标: {{ detailData.newLongitude }},
              {{ detailData.newLatitude }}
            </p>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog
      v-model="auditDialogVisible"
      :title="auditTitle"
      width="500px"
      @close="handleAuditDialogClose"
    >
      <el-form
        ref="auditFormRef"
        :model="auditForm"
        :rules="auditRules"
        label-width="100px"
      >
        <el-form-item label="审核结果" prop="result">
          <el-radio-group v-model="auditForm.result">
            <el-radio label="approved">通过</el-radio>
            <el-radio label="rejected">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注" prop="remark">
          <el-input
            v-model="auditForm.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入审核备注"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="auditDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleAuditSubmit"
            :loading="auditLoading"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Check,
  Close,
  Refresh,
  Search,
  View,
  Document,
  Location,
  DataBoard,
  Clock,
  CircleCheck,
  CircleClose,
} from "@element-plus/icons-vue";

// 搜索表单
const searchForm = reactive({
  facilityName: "",
  applicant: "",
  auditStatus: "",
  dateRange: null as any,
});

// 表格数据
const tableLoading = ref(false);
const tableRef = ref();
const selectedRows = ref<any[]>([]);

const tableData = ref([
  {
    id: "1",
    facilityCode: "SB001",
    facilityName: "1号水泵站",
    facilityType: "pump",
    areaName: "南京市",
    location: "玄武湖南岸",
    originalLongitude: 118.79,
    originalLatitude: 32.06,
    newLongitude: 118.7969,
    newLatitude: 32.0689,
    distance: 850,
    applicant: "张三",
    applyTime: "2024-08-20 09:30:00",
    auditStatus: "pending",
    auditor: "",
    auditTime: "",
    changeReason: "实地测量发现原坐标偏差较大",
    manager: "李四",
  },
  {
    id: "2",
    facilityCode: "JC002",
    facilityName: "水质监测点A",
    facilityType: "monitor",
    areaName: "苏州市",
    location: "金鸡湖东岸",
    originalLongitude: null,
    originalLatitude: null,
    newLongitude: 120.7021,
    newLatitude: 31.319,
    distance: null,
    applicant: "王五",
    applyTime: "2024-08-19 14:20:00",
    auditStatus: "approved",
    auditor: "管理员",
    auditTime: "2024-08-19 15:30:00",
    changeReason: "新增设施，首次录入坐标",
    auditRemark: "坐标准确，同意录入",
    manager: "赵六",
  },
  {
    id: "3",
    facilityCode: "ZM003",
    facilityName: "防洪闸门",
    facilityType: "gate",
    areaName: "南京市",
    location: "秦淮河段",
    originalLongitude: 118.78,
    originalLatitude: 32.03,
    newLongitude: 118.7834,
    newLatitude: 32.0378,
    distance: 920,
    applicant: "孙七",
    applyTime: "2024-08-18 11:15:00",
    auditStatus: "rejected",
    auditor: "管理员",
    auditTime: "2024-08-18 16:45:00",
    changeReason: "GPS定位更新",
    auditRemark: "坐标偏差过大，需要重新测量确认",
    manager: "周八",
  },
]);

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 3,
});

// 统计数据
const statistics = computed(() => {
  const total = tableData.value.length;
  const pending = tableData.value.filter(
    (item) => item.auditStatus === "pending"
  ).length;
  const approved = tableData.value.filter(
    (item) => item.auditStatus === "approved"
  ).length;
  const rejected = tableData.value.filter(
    (item) => item.auditStatus === "rejected"
  ).length;

  return { total, pending, approved, rejected };
});

// 详情对话框
const detailDialogVisible = ref(false);
const detailData = ref<any>(null);

// 审核对话框
const auditDialogVisible = ref(false);
const auditTitle = ref("");
const auditLoading = ref(false);
const auditFormRef = ref();
const currentAuditRows = ref<any[]>([]);

const auditForm = reactive({
  result: "approved",
  remark: "",
});

const auditRules = {
  result: [{ required: true, message: "请选择审核结果", trigger: "change" }],
  remark: [{ required: true, message: "请输入审核备注", trigger: "blur" }],
};

// 方法
const getTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    pump: "水泵设施",
    monitor: "监测设施",
    pipeline: "管道设施",
    gate: "闸门设施",
    other: "其他设施",
  };
  return typeMap[type] || "未知";
};

const getTypeTagType = (
  type: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const typeTagMap: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    pump: "primary",
    monitor: "success",
    pipeline: "warning",
    gate: "info",
    other: "danger",
  };
  return typeTagMap[type] || "info";
};

const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: "待审核",
    approved: "已通过",
    rejected: "已拒绝",
  };
  return statusMap[status] || "未知";
};

const getStatusTagType = (
  status: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const statusTagMap: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    pending: "warning",
    approved: "success",
    rejected: "danger",
  };
  return statusTagMap[status] || "info";
};

const handleSearch = () => {
  tableLoading.value = true;
  // TODO: 实现搜索逻辑
  setTimeout(() => {
    tableLoading.value = false;
    ElMessage.success("搜索完成");
  }, 1000);
};

const handleReset = () => {
  searchForm.facilityName = "";
  searchForm.applicant = "";
  searchForm.auditStatus = "";
  searchForm.dateRange = null;
  ElMessage.success("搜索条件已重置");
};

const refreshData = () => {
  tableLoading.value = true;
  // TODO: 实现数据刷新
  setTimeout(() => {
    tableLoading.value = false;
    ElMessage.success("数据已刷新");
  }, 1000);
};

const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection;
};

const handleViewDetail = (row: any) => {
  detailData.value = row;
  detailDialogVisible.value = true;
};

const handleViewAudit = (row: any) => {
  ElMessage.info("查看审核记录功能待实现");
};

const handleApprove = (row: any) => {
  auditTitle.value = "审核通过";
  auditForm.result = "approved";
  auditForm.remark = "";
  currentAuditRows.value = [row];
  auditDialogVisible.value = true;
};

const handleReject = (row: any) => {
  auditTitle.value = "审核拒绝";
  auditForm.result = "rejected";
  auditForm.remark = "";
  currentAuditRows.value = [row];
  auditDialogVisible.value = true;
};

const handleBatchApprove = () => {
  const pendingRows = selectedRows.value.filter(
    (row) => row.auditStatus === "pending"
  );
  if (pendingRows.length === 0) {
    ElMessage.warning("请选择待审核的记录");
    return;
  }

  auditTitle.value = `批量审核通过 (${pendingRows.length}条)`;
  auditForm.result = "approved";
  auditForm.remark = "";
  currentAuditRows.value = pendingRows;
  auditDialogVisible.value = true;
};

const handleBatchReject = () => {
  const pendingRows = selectedRows.value.filter(
    (row) => row.auditStatus === "pending"
  );
  if (pendingRows.length === 0) {
    ElMessage.warning("请选择待审核的记录");
    return;
  }

  auditTitle.value = `批量审核拒绝 (${pendingRows.length}条)`;
  auditForm.result = "rejected";
  auditForm.remark = "";
  currentAuditRows.value = pendingRows;
  auditDialogVisible.value = true;
};

const handleAuditSubmit = async () => {
  if (!auditFormRef.value) return;

  try {
    await auditFormRef.value.validate();
    auditLoading.value = true;

    // TODO: 实现审核逻辑
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 更新数据
    currentAuditRows.value.forEach((row) => {
      row.auditStatus = auditForm.result;
      row.auditor = "当前用户";
      row.auditTime = new Date().toLocaleString();
      row.auditRemark = auditForm.remark;
    });

    ElMessage.success("审核完成");
    auditDialogVisible.value = false;

    // 清空选择
    tableRef.value?.clearSelection();
    selectedRows.value = [];
  } catch (error) {
    console.error("表单验证失败:", error);
  } finally {
    auditLoading.value = false;
  }
};

const handleAuditDialogClose = () => {
  auditForm.result = "approved";
  auditForm.remark = "";
  currentAuditRows.value = [];
  auditFormRef.value?.clearValidate();
};

const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  refreshData();
};

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page;
  refreshData();
};

onMounted(() => {
  refreshData();
});
</script>

<style scoped>
.coordinates-audit-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.audit-statistics {
  margin: 20px 0;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid;
}

.stat-card.total {
  border-left-color: #409eff;
}

.stat-card.pending {
  border-left-color: #e6a23c;
}

.stat-card.approved {
  border-left-color: #67c23a;
}

.stat-card.rejected {
  border-left-color: #f56c6c;
}

.stat-icon {
  font-size: 36px;
  margin-right: 16px;
  color: #409eff;
}

.pending .stat-icon {
  color: #e6a23c;
}

.approved .stat-icon {
  color: #67c23a;
}

.rejected .stat-icon {
  color: #f56c6c;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.table-container {
  margin-top: 20px;
}

.coordinate-display {
  font-family: monospace;
  font-size: 12px;
}

.coordinate-display.new {
  color: #409eff;
  font-weight: bold;
}

.no-data {
  color: #c0c4cc;
  font-style: italic;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.map-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.map-placeholder {
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 2px dashed #ddd;
  border-radius: 6px;
  color: #999;
}

.map-placeholder .el-icon {
  color: #c0c4cc;
}

.dialog-footer {
  text-align: right;
}
</style>
