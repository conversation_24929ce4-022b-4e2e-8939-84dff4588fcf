<template>
  <div class="alarm-reason-analysis">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-button type="primary" @click="refreshData">刷新</el-button>
        </div>
      </template>

      <!-- 原因分类统计 -->
      <el-row :gutter="20" style="margin-bottom: 20px">
        <el-col :span="8">
          <el-card class="reason-card">
            <div class="reason-content">
              <div class="reason-number">{{ equipmentFailures }}</div>
              <div class="reason-label">设备故障</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="reason-card">
            <div class="reason-content">
              <div class="reason-number">{{ environmentFactors }}</div>
              <div class="reason-label">环境因素</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="reason-card">
            <div class="reason-content">
              <div class="reason-number">{{ humanFactors }}</div>
              <div class="reason-label">人为因素</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 告警原因分析图表 -->
      <div id="reasonChart" style="height: 400px; margin-bottom: 20px"></div>

      <!-- 告警原因分析表格 -->
      <el-table :data="reasonAnalysis" style="width: 100%" v-loading="loading">
        <el-table-column prop="category" label="原因分类" width="120" />
        <el-table-column prop="reason" label="具体原因" />
        <el-table-column prop="count" label="发生次数" width="100" />
        <el-table-column prop="trend" label="趋势" width="100">
          <template #default="scope">
            <el-tag :type="getTrendType(scope.row.trend)">
              {{ getTrendText(scope.row.trend) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="avgImpact" label="平均影响时长" width="120" />
        <el-table-column
          prop="preventionSuggestion"
          label="预防建议"
          show-overflow-tooltip
        />
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(scope.row)"
              >查看详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";

// 原因分类统计数据
const equipmentFailures = ref(45);
const environmentFactors = ref(32);
const humanFactors = ref(18);

// 告警原因分析数据
const reasonAnalysis = ref([
  {
    category: "设备故障",
    reason: "泵机械磨损",
    count: 15,
    trend: "up",
    avgImpact: "4.2小时",
    preventionSuggestion: "定期维护保养，更换磨损部件",
  },
  {
    category: "设备故障",
    reason: "传感器故障",
    count: 12,
    trend: "down",
    avgImpact: "2.1小时",
    preventionSuggestion: "加强传感器校准和清洁",
  },
  {
    category: "环境因素",
    reason: "恶劣天气影响",
    count: 20,
    trend: "stable",
    avgImpact: "1.5小时",
    preventionSuggestion: "增强设备防护措施",
  },
  {
    category: "环境因素",
    reason: "外部污染",
    count: 12,
    trend: "up",
    avgImpact: "3.0小时",
    preventionSuggestion: "加强水源监测和保护",
  },
  {
    category: "人为因素",
    reason: "操作不当",
    count: 10,
    trend: "down",
    avgImpact: "2.8小时",
    preventionSuggestion: "加强操作培训和规范管理",
  },
  {
    category: "人为因素",
    reason: "维护不及时",
    count: 8,
    trend: "stable",
    avgImpact: "5.5小时",
    preventionSuggestion: "建立完善的维护计划和提醒机制",
  },
]);

const loading = ref(false);

// 获取趋势类型
const getTrendType = (
  trend: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const typeMap: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    up: "danger",
    down: "success",
    stable: "info",
  };
  return typeMap[trend] || "info";
};

// 获取趋势文本
const getTrendText = (trend: string) => {
  const textMap: Record<string, string> = {
    up: "上升",
    down: "下降",
    stable: "稳定",
  };
  return textMap[trend] || "稳定";
};

// 刷新数据
const refreshData = () => {
  loadData();
};

// 查看详情
const handleViewDetail = (row: any) => {
  console.log("查看告警原因详情:", row);
};

// 加载数据
const loadData = () => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
    initChart();
  }, 1000);
};

// 初始化图表
const initChart = () => {
  // 这里可以使用ECharts等图表库来绘制告警原因分析图表
  console.log("初始化告警原因分析图表");
};

onMounted(() => {
  loadData();
});
</script>

<style scoped>
.alarm-reason-analysis {
  padding: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.reason-card {
  text-align: center;
}
.reason-content {
  padding: 20px;
}
.reason-number {
  font-size: 28px;
  font-weight: bold;
  color: #409eff;
}
.reason-label {
  margin-top: 10px;
  color: #666;
}
</style>
