import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { DeviceData5s, DeviceData5sDocument } from '../schemas/device-data-5s.schema';

@Injectable()
export class DeviceData5sService {
  constructor(
    @InjectModel(DeviceData5s.name)
    private readonly deviceData5sModel: Model<DeviceData5sDocument>,
  ) {}

  async findAll(): Promise<DeviceData5s[]> {
    return this.deviceData5sModel.find()
      .sort({ timestamp: -1 })
      .limit(1000)
      .exec();
  }

  async findByDeviceSn(deviceSn: string): Promise<DeviceData5s[]> {
    return this.deviceData5sModel.find({ device_sn: deviceSn })
      .sort({ timestamp: -1 })
      .limit(1000)
      .exec();
  }

  async findByTimeRange(
    startTime: Date, 
    endTime: Date, 
    deviceSn?: string
  ): Promise<DeviceData5s[]> {
    const query: any = {
      timestamp: { $gte: startTime, $lte: endTime }
    };
    
    if (deviceSn) {
      query.device_sn = deviceSn;
    }

    return this.deviceData5sModel.find(query)
      .sort({ timestamp: 1 })
      .exec();
  }

  async getLatestData(deviceSn?: string): Promise<DeviceData5s[]> {
    const query: any = {};
    if (deviceSn) {
      query.device_sn = deviceSn;
    }

    return this.deviceData5sModel.find(query)
      .sort({ timestamp: -1 })
      .limit(100)
      .exec();
  }

  async getRealTimeData(deviceSn?: string): Promise<DeviceData5s[]> {
    // 获取最近5分钟的数据
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    const query: any = {
      timestamp: { $gte: fiveMinutesAgo }
    };
    
    if (deviceSn) {
      query.device_sn = deviceSn;
    }

    return this.deviceData5sModel.find(query)
      .sort({ timestamp: -1 })
      .exec();
  }

  async getDataStatistics(deviceSn?: string) {
    const matchStage: any = {};
    if (deviceSn) {
      matchStage.device_sn = deviceSn;
    }

    const pipeline = [
      { $match: matchStage },
      {
        $group: {
          _id: null,
          totalRecords: { $sum: 1 },
          latestTimestamp: { $max: '$timestamp' },
          earliestTimestamp: { $min: '$timestamp' },
          uniqueDevices: { $addToSet: '$device_sn' }
        }
      },
      {
        $project: {
          _id: 0,
          totalRecords: 1,
          latestTimestamp: 1,
          earliestTimestamp: 1,
          deviceCount: { $size: '$uniqueDevices' }
        }
      }
    ];

    const result = await this.deviceData5sModel.aggregate(pipeline);
    return result[0] || {
      totalRecords: 0,
      deviceCount: 0,
      latestTimestamp: null,
      earliestTimestamp: null
    };
  }

  async getDeviceList(): Promise<string[]> {
    return this.deviceData5sModel.distinct('device_sn');
  }
}