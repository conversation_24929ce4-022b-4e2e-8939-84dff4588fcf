import { ApiProperty } from '@nestjs/swagger';

export class CreateMasterDto {
  @ApiProperty({ description: '权限组名称', example: '超级管理员' })
  title: string;
  @ApiProperty({ description: '用户管理权限', example: 1 })
  userop: number;
  @ApiProperty({ description: '权限组管理权限', example: 1 })
  masterop: number;

  @ApiProperty({ description: '登录日志权限', example: 1 })
  loginlogop: number;
  @ApiProperty({ description: '党员管理权限', example: 1 })
  partymemberop: number;
  @ApiProperty({ description: '管理权限', example: 1 })
  partygroupop: number;
  @ApiProperty({ description: '管理权限', example: 1 })
  partyactionop: number;
  @ApiProperty({ description: '管理权限', example: 1 })
  partyactionclassifyop: number;
  @ApiProperty({ description: '管理权限', example: 1 })
  volunteerop: number;
  @ApiProperty({ description: '管理权限', example: 1 })
  volunteergroupop: number;
  @ApiProperty({ description: '管理权限', example: 1 })
  volunteeractionop: number;
  @ApiProperty({ description: '管理权限', example: 1 })
  volunteeractionclassifyop: number;
  @ApiProperty({ description: '管理权限', example: 1 })
  articleop: number;
  @ApiProperty({ description: '管理权限', example: 1 })
  peopleop: number;
  @ApiProperty({ description: '管理权限', example: 1 })
  buildingop: number;
  @ApiProperty({ description: '管理权限', example: 1 })
  spotop: number;
  @ApiProperty({ description: '管理权限', example: 1 })
  personnelop: number;
  @ApiProperty({ description: '管理权限', example: 1 })
  waringpersonnelop: number;
  @ApiProperty({ description: '管理权限', example: 1 })
  localepersonnelop: number;
  @ApiProperty({ description: '管理权限', example: 1 })
  personnellogop: number;
  @ApiProperty({ description: '管理权限', example: 1 })
  cameraop: number;
  @ApiProperty({ description: '管理权限', example: 1 })
  modelinfoop: number;
  @ApiProperty({ description: '管理权限', example: 1 })
  modeltypeop: number;
}
