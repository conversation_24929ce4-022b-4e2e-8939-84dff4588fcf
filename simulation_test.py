#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
水利站仿真测试脚本
模拟1000个点位，每个点位每5秒发送一次数据到后端TCP服务器
包含数据破损、空字段、丢失字段、不存在SN等异常情况的测试
真实场景仿真：1000个监测点位的数据采集和传输
"""

import json
import socket
import time
import threading
import random
import datetime
import os  # 添加os模块导入
from typing import Dict, Any, Optional
import argparse
import signal
import sys
import copy
import struct  # 添加struct模块用于socket选项设置


class WaterStationSimulator:
    """水利站点位仿真器"""

    def __init__(
        self,
        host: str = "127.0.0.1",
        port: int = 8889,
        num_stations: int = 1000,
        duration: int = 1,
    ):
        self.host = host
        self.port = port
        self.num_stations = num_stations
        self.duration = duration  # 测试持续时间（秒），0表示无限循环
        self.running = True
        self.threads = []
        self.station_stats = {}
        self.start_time = None  # 记录开始时间
        
        # 连接池：为每个站点预分配socket连接
        self.connection_pool = {}
        self.pool_lock = threading.Lock()

        # 1000个不同的设备序列号（包含有效和无效的SN）
        self.valid_sns = []

        # 生成M100系列设备SN（900个有效SN）
        for i in range(1, 901):
            self.valid_sns.append(f"M100{i:03d}")

        # 生成M300系列设备SN（95个有效SN）
        for i in range(1, 96):
            self.valid_sns.append(f"02800125071500{i:03d}")

        # 总共995个有效SN

        # 无效SN（假设这些不在数据库中，应该被丢弃）
        self.invalid_sns = []
        # 生成5个无效SN
        for i in range(1, 6):
            self.invalid_sns.append(f"INVALID{i:03d}")

        # 设备状态缓存，用于模拟状态变化
        self.device_status_cache = {}

        # 注册信号处理器，支持优雅退出
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """信号处理器，处理Ctrl+C等退出信号"""
        print(f"\n[仿真器] 接收到退出信号 {signum}，正在停止仿真...")
        self.stop_simulation()

    def get_station_sn(self, station_id: int) -> str:
        """获取站点设备序列号 - 使用测试专用SN避免冲突"""
        # 使用TEST前缀确保与生产环境SN不冲突
        return f"TEST{station_id:04d}"

    def simulate_data_corruption(
        self, data: Dict[str, Any], corruption_type: str
    ) -> Optional[Dict[str, Any]]:
        """模拟数据破损情况"""
        if corruption_type == "empty_fields":
            # 模拟空字段
            corrupted_data = copy.deepcopy(data)
            corrupted_data["wenshidu"]["temperature"] = None
            corrupted_data["float_switches"]["float1"] = ""
            corrupted_data["diannengbiao"]["voltages"]["Ua"] = None
            return corrupted_data

        elif corruption_type == "missing_fields":
            # 模拟丢失字段
            corrupted_data = copy.deepcopy(data)
            del corrupted_data["wenshidu"]  # 删除温湿度整个字段
            del corrupted_data["device_info"]["sn"]  # 删除设备SN
            if "DO21_status" in corrupted_data:
                del corrupted_data["DO21_status"]  # 删除DO状态
            return corrupted_data

        elif corruption_type == "invalid_json":
            # 模拟JSON格式破损
            return None  # 返回None表示需要发送无效JSON

        elif corruption_type == "invalid_types":
            # 模拟数据类型错误
            corrupted_data = copy.deepcopy(data)
            corrupted_data["timestamp"] = "invalid_timestamp"  # 字符串而非数字
            corrupted_data["wenshidu"]["temperature"] = "not_a_number"
            corrupted_data["DO21_status"] = "invalid_status"
            return corrupted_data

        return data

    def generate_realistic_data(self, station_id: int) -> Dict[str, Any]:
        """生成真实的设备数据"""
        current_time = int(time.time() * 1000)  # 毫秒级时间戳
        sn = self.get_station_sn(station_id)

        # 模拟不同站点的基础数据差异
        base_temp = 20 + (station_id % 10) * 2  # 不同站点基础温度不同
        base_voltage = 220 + (station_id % 5) * 2  # 不同站点基础电压略有差异

        # 添加时间变化和随机波动
        time_factor = time.time() % 86400  # 一天的秒数
        temp_variation = 10 * (0.5 - abs((time_factor / 43200) - 1))  # 温度日变化

        data = {
            "timestamp": current_time,
            "dianliucaiji2": {
                "curr2_ch1": round(random.uniform(0.01, 0.02), 3),
                "curr2_ch2": round(random.uniform(0.7, 0.9), 3),
                "curr2_ch3": round(random.uniform(0.008, 0.015), 3),
                "curr2_ch4": round(random.uniform(0.4, 0.6), 3),
            },
            "wenshidu": {
                "humidity": round(base_temp + random.uniform(-5, 5) + 20, 1),
                "temperature": round(
                    base_temp + temp_variation + random.uniform(-2, 2), 1
                ),
            },
            "diannengbiao": {
                "voltages": {
                    "Ua": round(base_voltage + random.uniform(-5, 5), 1),
                    "Ub": round(base_voltage + random.uniform(-5, 5), 1),
                    "Uc": round(base_voltage + random.uniform(-5, 5), 1),
                },
                "currents": {
                    "Ia": round(random.uniform(0.8, 1.0), 3),
                    "Ib": round(random.uniform(0.7, 0.9), 3),
                    "Ic": round(random.uniform(1.2, 1.5), 3),
                },
                "active_power": {
                    "total": round(random.uniform(-400, -300), 1),
                    "phaseA": round(random.uniform(-120, -90), 1),
                    "phaseB": round(random.uniform(-120, -90), 1),
                    "phaseC": round(random.uniform(-160, -120), 1),
                },
                "active_energy": round(random.uniform(0, 100), 1),
            },
            "float_switches": {"float1": random.choice([0, 1])},  # 浮球开关状态
            "water_pump1": {
                "mode_status": self.simulate_pump_status_change(
                    station_id, "water_pump1"
                )
            },
            "water_pump2": {
                "mode_status": self.simulate_pump_status_change(
                    station_id, "water_pump2"
                )
            },
            "air_pump1": {
                "mode_status": self.simulate_pump_status_change(station_id, "air_pump1")
            },
            "air_pump2": {
                "mode_status": self.simulate_pump_status_change(station_id, "air_pump2")
            },
            "device_info": {
                "sn": sn,
                "imei": f"86573507294{station_id:04d}",  # 模拟IMEI
                "timestamp": current_time - random.randint(1, 10),  # 设备时间戳略有差异
            },
            "DO21_status": self.simulate_switch_status_change(
                station_id, "DO21_status"
            ),
            "DO22_status": self.simulate_switch_status_change(
                station_id, "DO22_status"
            ),
            "DO23_status": self.simulate_switch_status_change(
                station_id, "DO23_status"
            ),
            "DO24_status": self.simulate_switch_status_change(
                station_id, "DO24_status"
            ),
            "sn": sn,
            "topic": "/UploadTopic",
        }

        # 随机添加数据破损（2%概率，降低从10%）
        if random.random() < 0.02:
            corruption_types = [
                "empty_fields",
                "missing_fields",
                "invalid_json",
                "invalid_types",
            ]
            corruption_type = random.choice(corruption_types)

            corrupted_data = self.simulate_data_corruption(data, corruption_type)
            if corrupted_data is not None:
                data = corrupted_data
                # For invalid_json, we return the data with a special marker
                # The send_data_to_server method will handle it specially
                if corruption_type == "invalid_json":
                    data["_corruption_type"] = corruption_type
                return data
            else:
                # When simulate_data_corruption returns None (for invalid_json case)
                # we need to handle it properly
                if corruption_type == "invalid_json":
                    data["_corruption_type"] = corruption_type
                    return data
                # For other cases where corrupted_data is None, we should not return None
                # This shouldn't happen with current implementation, but let's be safe
                return data  # Return original data if corruption function returned None unexpectedly

        # Always return data, even if unmodified
        return data

    def simulate_switch_status_change(self, station_id: int, switch_name: str) -> int:
        """
        模拟开关状态变化，保持一定的状态稳定性
        :param station_id: 站点ID
        :param switch_name: 开关名称
        :return: 开关状态 (0 or 1)
        """
        cache_key = f"{station_id}_{switch_name}"

        # 获取当前状态
        current_status = self.device_status_cache.get(cache_key, random.choice([0, 1]))

        # 2%的概率发生状态变化（提高从原来的0.02）
        if random.random() < 0.02:
            new_status = 1 if current_status == 0 else 0
            self.device_status_cache[cache_key] = new_status

            # 发送单独的状态变化消息
            self.send_status_change_message(station_id, switch_name, new_status)

            return new_status
        else:
            self.device_status_cache[cache_key] = current_status
            return current_status

    def simulate_pump_status_change(self, station_id: int, pump_name: str) -> int:
        """
        模拟水泵/气泵三档位状态变化：0=停止，1=手动，2=自动
        :param station_id: 站点ID
        :param pump_name: 水泵名称
        :return: 水泵模式状态 (0=停止, 1=手动, 2=自动)
        """
        cache_key = f"{station_id}_{pump_name}"

        # 获取当前状态
        current_status = self.device_status_cache.get(
            cache_key, random.choice([0, 1, 2])
        )

        # 2%的概率发生模式变化（提高从原来的0.01）
        if random.random() < 0.02:
            # 三档位之间的随机变化
            available_modes = [0, 1, 2]
            available_modes.remove(current_status)  # 移除当前状态
            new_status = random.choice(available_modes)

            self.device_status_cache[cache_key] = new_status

            # 发送单独的状态变化消息
            self.send_pump_mode_change_message(station_id, pump_name, new_status)

            return new_status
        else:
            self.device_status_cache[cache_key] = current_status
            return current_status

    def send_status_change_message(
        self, station_id: int, field_name: str, new_value: int
    ):
        """
        发送DO状态变化消息到服务器
        :param station_id: 站点ID
        :param field_name: 字段名称
        :param new_value: 新状态值
        """
        try:
            sn = self.get_station_sn(station_id)
            current_time = int(time.time() * 1000)

            # 创建DO状态变化消息
            status_change_data = {
                "sn": sn,
                "timestamp": current_time,
                "message_type": "status_change",
                "change_source": "physical_switch",
            }

            # 只支持DO状态变化
            if field_name.startswith("DO"):
                status_change_data[field_name] = new_value

            # 发送到服务器
            success, response = self.send_data_to_server(status_change_data, station_id)

            if success:
                change_type = "开启" if new_value == 1 else "关闭"
                print(
                    f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [状态变化] 站点{station_id:02d} {field_name} {change_type} (SN: {sn})"
                )
            else:
                print(
                    f"[状态变化] 站点{station_id:02d} {field_name} 状态变化消息发送失败: {response}"
                )

        except Exception as e:
            print(f"[状态变化] 站点{station_id:02d} 发送状态变化消息失败: {e}")

    def send_pump_mode_change_message(
        self, station_id: int, pump_name: str, mode_value: int
    ):
        """
        发送水泵模式变化消息到服务器（三档位）
        :param station_id: 站点ID
        :param pump_name: 水泵名称
        :param mode_value: 模式值 (0=停止, 1=手动, 2=自动)
        """
        try:
            sn = self.get_station_sn(station_id)
            current_time = int(time.time() * 1000)

            # 创建三档位模式变化消息
            mode_change_data = {
                "sn": sn,
                "timestamp": current_time,
                "message_type": "mode_change",
                "change_source": "physical_switch_3_position",
                pump_name: {"mode_status": mode_value},  # 0=停止, 1=手动, 2=自动
            }

            # 发送到服务器
            success, response = self.send_data_to_server(mode_change_data, station_id)

            if success:
                mode_names = {
                    0: "停止模式(强制关闭)",
                    1: "手动模式(强制启动)",
                    2: "自动模式(按规则运行)",
                }
                mode_name = mode_names.get(mode_value, f"未知模式({mode_value})")
                print(
                    f"[{datetime.datetime.now().strftime('%H:%M:%S')}] [模式变化] 站点{station_id:02d} {pump_name} 档位调至{mode_name} (SN: {sn})"
                )
            else:
                print(
                    f"[模式变化] 站点{station_id:02d} {pump_name} 模式变化消息发送失败: {response}"
                )

        except Exception as e:
            print(f"[模式变化] 站点{station_id:02d} 发送模式变化消息失败: {e}")

    def get_connection(self, station_id: int):
        """获取或创建站点的socket连接"""
        with self.pool_lock:
            if station_id not in self.connection_pool:
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(5)
                    
                    # 设置socket选项
                    sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                    if os.name == 'nt':
                        try:
                            sock.setsockopt(socket.SOL_SOCKET, socket.SO_EXCLUSIVEADDRUSE, 1)
                        except:
                            pass
                    
                    try:
                        sock.setsockopt(socket.SOL_SOCKET, socket.SO_LINGER, 
                                      struct.pack('ii', 1, 0))
                    except:
                        pass
                    
                    sock.connect((self.host, self.port))
                    self.connection_pool[station_id] = sock
                    print(f"[连接池] 站点{station_id:03d} 建立新连接 (SN: {self.get_station_sn(station_id)})")
                    
                except Exception as e:
                    print(f"[连接池] 站点{station_id:03d} 建立连接失败: {e}")
                    return None
            else:
                # 连接已存在，验证连接是否还活着
                sock = self.connection_pool[station_id]
                try:
                    # 使用SO_ERROR检查socket状态
                    error = sock.getsockopt(socket.SOL_SOCKET, socket.SO_ERROR)
                    if error != 0:
                        print(f"[连接池] 站点{station_id:03d} 连接状态异常(error={error})，重新建立")
                        self.close_connection(station_id)
                        return self.get_connection(station_id)  # 递归重建
                except:
                    print(f"[连接池] 站点{station_id:03d} 连接状态检查失败，重新建立")
                    self.close_connection(station_id)
                    return self.get_connection(station_id)  # 递归重建
            
            return self.connection_pool.get(station_id)
    
    def close_connection(self, station_id: int):
        """关闭站点的socket连接"""
        with self.pool_lock:
            if station_id in self.connection_pool:
                try:
                    self.connection_pool[station_id].close()
                    print(f"[连接池] 站点{station_id:03d} 连接已关闭")
                except:
                    pass
                del self.connection_pool[station_id]
    
    def close_all_connections(self):
        """关闭所有socket连接"""
        with self.pool_lock:
            for station_id, sock in self.connection_pool.items():
                try:
                    sock.close()
                except:
                    pass
            self.connection_pool.clear()
            print("[连接池] 所有连接已关闭")

    def send_data_to_server(
        self, data: Dict[str, Any], station_id: int
    ) -> tuple[bool, str]:
        """发送数据到TCP服务器，使用连接池（长连接模式）"""
        sock = self.get_connection(station_id)
        if not sock:
            return False, "无法建立连接"
        
        try:
            # 检查是否为invalid_json类型的破损数据
            if data.get("_corruption_type") == "invalid_json":
                # 发送破损的JSON
                broken_json = json.dumps(data, ensure_ascii=False)[:-5] + "broken"
                sock.send(broken_json.encode("utf-8"))
            else:
                # 将数据转换为JSON字符串并发送
                json_data = json.dumps(data, ensure_ascii=False)
                sock.send(json_data.encode("utf-8"))

            # 等待服务器响应（可选）
            response_msg = "No response"
            try:
                response = sock.recv(1024)
                response_msg = response.decode("utf-8", errors="ignore")
            except socket.timeout:
                response_msg = "Response timeout"

            return True, response_msg

        except socket.error as e:
            error_msg = str(e)
            
            # 针对10053错误（连接被服务器关闭）特殊处理
            if "10053" in error_msg:
                print(f"[站点{station_id:03d}] 连接被服务器关闭: {error_msg}")
            else:
                print(f"[站点{station_id:03d}] 连接错误: {error_msg}")
            
            # 连接出错，关闭并移除，下次会重新建立
            self.close_connection(station_id)
            return False, error_msg
        except Exception as e:
            return False, str(e)

    def station_worker(self, station_id: int):
        """单个站点的工作线程"""
        sn = self.get_station_sn(station_id)
        # 所有测试SN都标记为有效，避免判断逻辑问题
        is_valid_sn = True
        sn_type = "测试SN"

        print(f"[站点{station_id:02d}] 启动数据发送线程，设备SN: {sn} ({sn_type})")

        # 初始化统计数据
        self.station_stats[station_id] = {
            "total_sent": 0,
            "total_failed": 0,
            "corruption_count": 0,
            "sn": sn,
            "is_valid_sn": is_valid_sn,
            "last_sent_time": None,
            "last_response": None,
        }

        # 为每个站点设置不同的启动延迟，避免同时连接（1000个站点需要更长延迟）
        startup_delay = station_id * 0.01  # 每个站点延迟0.01秒启动，1000个站点总启动时间10秒
        time.sleep(startup_delay)

        send_count = 0
        while self.running:
            # 检查是否超过时长限制
            if self.duration > 0 and self.start_time:
                elapsed = time.time() - self.start_time
                if elapsed >= self.duration:
                    break

            try:
                # 生成并发送数据
                data = self.generate_realistic_data(station_id)
                success, response = self.send_data_to_server(data, station_id)

                # 更新统计
                if success:
                    self.station_stats[station_id]["total_sent"] += 1
                    self.station_stats[station_id][
                        "last_sent_time"
                    ] = datetime.datetime.now()
                    self.station_stats[station_id]["last_response"] = response
                    send_count += 1

                    # 统计数据破损情况
                    if "_corruption_type" in data:
                        self.station_stats[station_id]["corruption_count"] += 1

                    if send_count % 60 == 0:  # 每发送60次打印一次状态（每5分钟打印一次，60次×5秒=300秒）
                        temp = data.get("wenshidu", {}).get("temperature", "N/A")
                        humidity = data.get("wenshidu", {}).get("humidity", "N/A")
                        voltage = (
                            data.get("diannengbiao", {})
                            .get("voltages", {})
                            .get("Ua", "N/A")
                        )
                        corruption_info = (
                            f" [破损:{data['_corruption_type']}]"
                            if "_corruption_type" in data
                            else ""
                        )
                        print(
                            f"[站点{station_id:02d}] 已发送 {send_count} 次 | SN:{sn}({sn_type}) | 温度:{temp}°C | 湿度:{humidity}% | 电压:{voltage}V{corruption_info}"
                        )
                else:
                    self.station_stats[station_id]["total_failed"] += 1
                    print(f"[站点{station_id:02d}] 发送失败: {response}")

                # 等待5秒后发送下一次数据，但考虑时长限制
                if self.running:
                    if self.duration > 0 and self.start_time:
                        elapsed = time.time() - self.start_time
                        remaining_time = self.duration - elapsed
                        sleep_time = min(5.0, remaining_time)
                        if sleep_time > 0:
                            time.sleep(sleep_time)
                    else:
                        # 添加更大的随机延迟，避免所有站点同时发送
                        base_sleep = 5.0
                        random_offset = random.uniform(-0.5, 0.5)  # ±500ms随机偏移
                        sleep_time = max(4.0, base_sleep + random_offset)
                        time.sleep(sleep_time)

            except Exception as e:
                print(f"[站点{station_id:02d}] 工作线程异常: {e}")
                time.sleep(5)

    def print_statistics(self):
        """打印统计信息的线程"""
        while self.running:
            time.sleep(30)  # 每30秒打印一次统计信息
            if not self.running:
                break

            print("\n" + "=" * 100)
            print(
                f"仿真统计报告 - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
            print("=" * 100)

            total_sent = 0
            total_failed = 0
            total_corruption = 0
            active_stations = 0
            valid_sn_stations = 0
            invalid_sn_stations = 0

            for station_id, stats in self.station_stats.items():
                total_sent += stats["total_sent"]
                total_failed += stats["total_failed"]
                total_corruption += stats["corruption_count"]

                if stats["is_valid_sn"]:
                    valid_sn_stations += 1
                else:
                    invalid_sn_stations += 1

                if (
                    stats["last_sent_time"]
                    and (datetime.datetime.now() - stats["last_sent_time"]).seconds < 60
                ):
                    active_stations += 1

            print(f"活跃站点数: {active_stations}/{len(self.station_stats)}")
            print(
                f"有效SN站点: {valid_sn_stations} | 无效SN站点: {invalid_sn_stations}"
            )
            print(f"总发送次数: {total_sent}")
            print(f"总失败次数: {total_failed}")
            print(f"数据破损次数: {total_corruption}")
            if total_sent + total_failed > 0:
                success_rate = (total_sent / (total_sent + total_failed)) * 100
                corruption_rate = (
                    (total_corruption / total_sent) * 100 if total_sent > 0 else 0
                )
                print(f"成功率: {success_rate:.1f}%")
                print(f"数据破损率: {corruption_rate:.1f}%")

            # 显示最近活跃的站点
            recent_active = []
            for station_id, stats in self.station_stats.items():
                if (
                    stats["last_sent_time"]
                    and (datetime.datetime.now() - stats["last_sent_time"]).seconds < 10
                ):
                    sn_type = "有效" if stats["is_valid_sn"] else "无效"
                    recent_active.append(f"站点{station_id:02d}({sn_type})")

            if recent_active:
                print(f"最近10秒内活跃站点: {', '.join(recent_active)}")

            print("=" * 100 + "\n")

    def start_simulation(self):
        """启动仿真"""
        print(f"水利站仿真测试启动")
        print(f"目标服务器: {self.host}:{self.port}")
        print(f"模拟站点数: {self.num_stations}")
        print(f"发送频率: 每5秒一次")
        print(f"数据破损率: 2%（包含空字段、缺失字段、JSON破损、数据类型错误）")
        print(f"状态变化率: 2%（DO开关状态变化）")
        print(f"模式变化率: 2%（水泵/气泵三档位模式变化）")
        print(f"有效SN数量: {len(self.valid_sns)}")
        print(f"无效SN数量: {len(self.invalid_sns)}")
        if self.duration > 0:
            print(f"测试时长: {self.duration}秒 ({self.duration/60:.1f}分钟)")
        else:
            print("测试时长: 无限循环")
        print("按 Ctrl+C 停止仿真\n")

        # 记录开始时间
        self.start_time = time.time()

        # 启动统计线程
        stats_thread = threading.Thread(target=self.print_statistics, daemon=True)
        stats_thread.start()
        self.threads.append(stats_thread)

        # 启动各站点的工作线程
        for station_id in range(1, self.num_stations + 1):
            thread = threading.Thread(
                target=self.station_worker, args=(station_id,), daemon=True
            )
            thread.start()
            self.threads.append(thread)

        try:
            # 主线程保持运行
            while self.running:
                # 检查是否超过时长限制
                if self.duration > 0 and self.start_time:
                    elapsed = time.time() - self.start_time
                    if elapsed >= self.duration:
                        print(
                            f"\n[仿真器] 测试时长 {self.duration} 秒已到，停止测试..."
                        )
                        self.stop_simulation()
                        break
                time.sleep(1)
        except KeyboardInterrupt:
            self.stop_simulation()

    def stop_simulation(self):
        """停止仿真"""
        print("\n[仿真器] 正在停止所有站点...")
        self.running = False

        # 关闭所有连接池中的连接
        self.close_all_connections()

        # 等待所有线程结束
        for thread in self.threads:
            if thread.is_alive():
                thread.join(timeout=2)

        # 打印最终统计
        print("\n" + "=" * 100)
        print("最终统计报告")
        print("=" * 100)

        total_sent = sum(stats["total_sent"] for stats in self.station_stats.values())
        total_failed = sum(
            stats["total_failed"] for stats in self.station_stats.values()
        )
        total_corruption = sum(
            stats["corruption_count"] for stats in self.station_stats.values()
        )

        print(f"总站点数: {len(self.station_stats)}")
        print(f"总发送次数: {total_sent}")
        print(f"总失败次数: {total_failed}")
        print(f"总破损次数: {total_corruption}")
        if total_sent + total_failed > 0:
            success_rate = (total_sent / (total_sent + total_failed)) * 100
            corruption_rate = (
                (total_corruption / total_sent) * 100 if total_sent > 0 else 0
            )
            print(f"总成功率: {success_rate:.1f}%")
            print(f"总破损率: {corruption_rate:.1f}%")

        print("\n各站点详细统计:")
        for station_id, stats in sorted(self.station_stats.items()):
            sn = stats["sn"]
            sn_type = "有效SN" if stats["is_valid_sn"] else "无效SN"
            corruption_count = stats["corruption_count"]
            print(
                f"  站点{station_id:02d} (SN:{sn}, {sn_type}) - 成功:{stats['total_sent']} 失败:{stats['total_failed']} 破损:{corruption_count}"
            )

        print("=" * 100)
        print("仿真测试结束")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="水利站仿真测试脚本")
    parser.add_argument(
        "--host", default="127.0.0.1", help="后端服务器IP地址 (默认: 127.0.0.1)"
    )
    parser.add_argument(
        "--port", type=int, default=8889, help="后端TCP端口 (默认: 8889)"
    )
    parser.add_argument(
        "--stations", type=int, default=1000, help="模拟站点数量 (默认: 1000)"
    )
    parser.add_argument(
        "--test-single", action="store_true", help="单次测试模式，发送一次数据后退出"
    )
    parser.add_argument(
        "--duration", type=int, default=0, help="测试持续时间（秒），0表示无限循环"
    )
    parser.add_argument(
        "--test-corruption", action="store_true", help="专门测试数据破损处理"
    )
    parser.add_argument(
        "--test-invalid-sn", action="store_true", help="专门测试无效SN处理"
    )

    args = parser.parse_args()

    # 创建仿真器
    simulator = WaterStationSimulator(
        host=args.host,
        port=args.port,
        num_stations=args.stations,
        duration=args.duration,
    )

    if args.test_single:
        # 单次测试模式
        print("单次测试模式 - 每个站点发送一次数据")
        print(f"目标服务器: {args.host}:{args.port}")
        print(f"测试站点数: {args.stations}\n")

        success_count = 0
        for station_id in range(1, args.stations + 1):
            data = simulator.generate_realistic_data(station_id)
            sn = data["sn"]
            temp = data.get("wenshidu", {}).get("temperature", "N/A")
            corruption_info = (
                f" [破损:{data['_corruption_type']}]"
                if "_corruption_type" in data
                else ""
            )

            success, response = simulator.send_data_to_server(data, station_id)
            if success:
                success_count += 1
                print(
                    f"[OK] 站点{station_id:02d} (SN:{sn}) 数据发送成功 | 温度: {temp}°C{corruption_info}"
                )
            else:
                print(f"[FAIL] 站点{station_id:02d} (SN:{sn}) 数据发送失败: {response}")

            time.sleep(0.1)  # 避免并发过高

        success_rate = (success_count / args.stations) * 100
        print(
            f"\n测试完成 - 成功率: {success_rate:.1f}% ({success_count}/{args.stations})"
        )

    elif args.test_corruption:
        # 专门测试数据破损
        print("数据破损专项测试")
        print("测试各种数据破损情况的处理能力...\n")

        corruption_types = [
            "empty_fields",
            "missing_fields",
            "invalid_json",
            "invalid_types",
        ]
        for i, corruption_type in enumerate(corruption_types, 1):
            print(f"测试 {i}: {corruption_type}")
            data = simulator.generate_realistic_data(1)
            corrupted_data = simulator.simulate_data_corruption(data, corruption_type)

            if corrupted_data:
                corrupted_data["_corruption_type"] = corruption_type
                success, response = simulator.send_data_to_server(corrupted_data, 1)
                print(f"结果: {'成功' if success else '失败'} - {response}")
            else:
                # invalid_json 情况
                data["_corruption_type"] = corruption_type
                success, response = simulator.send_data_to_server(data, 1)
                print(f"结果: {'成功' if success else '失败'} - {response}")

            time.sleep(1)

    elif args.test_invalid_sn:
        # 专门测试无效SN
        print("无效SN专项测试")
        print("测试数据库中不存在的SN处理能力...\n")

        # 发送无效SN数据
        for i, invalid_sn in enumerate(simulator.invalid_sns, 1):
            print(f"测试 {i}: 发送无效SN {invalid_sn}")

            # 生成数据并替换SN
            data = simulator.generate_realistic_data(1)
            data["sn"] = invalid_sn
            data["device_info"]["sn"] = invalid_sn

            success, response = simulator.send_data_to_server(data, 1)
            print(f"结果: {'成功' if success else '失败'} - {response}")
            print("期望: 服务器应该接收数据但不存储到数据库\n")

            time.sleep(1)

    else:
        # 持续仿真模式
        simulator.start_simulation()


if __name__ == "__main__":
    main()
