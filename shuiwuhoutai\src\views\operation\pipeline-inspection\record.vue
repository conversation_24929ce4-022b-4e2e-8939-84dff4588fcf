<template>
  <div class="pipeline-inspection-record">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <el-button type="primary" @click="handleCreateRecord"
            >新增记录</el-button
          >
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="记录编号">
          <el-input
            v-model="searchForm.recordNo"
            placeholder="请输入记录编号"
            clearable
          />
        </el-form-item>
        <el-form-item label="管网名称">
          <el-input
            v-model="searchForm.pipelineName"
            placeholder="请输入管网名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="巡检员">
          <el-select
            v-model="searchForm.inspector"
            placeholder="请选择巡检员"
            clearable
          >
            <el-option label="张三" value="张三" />
            <el-option label="李四" value="李四" />
            <el-option label="王五" value="王五" />
          </el-select>
        </el-form-item>
        <el-form-item label="巡检结果">
          <el-select
            v-model="searchForm.result"
            placeholder="请选择结果"
            clearable
          >
            <el-option label="正常" value="normal" />
            <el-option label="异常" value="abnormal" />
            <el-option label="需维修" value="repair" />
          </el-select>
        </el-form-item>
        <el-form-item label="巡检时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>

      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon normal">
                <el-icon size="24"><Check /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ stats.normal }}</div>
                <div class="stat-label">正常记录</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon abnormal">
                <el-icon size="24"><Warning /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ stats.abnormal }}</div>
                <div class="stat-label">异常记录</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon repair">
                <el-icon size="24"><Tools /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ stats.repair }}</div>
                <div class="stat-label">需维修</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon total">
                <el-icon size="24"><Document /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ stats.total }}</div>
                <div class="stat-label">总记录数</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 巡检记录表格 -->
      <el-table :data="recordData" stripe>
        <el-table-column type="selection" width="55" />
        <el-table-column prop="recordNo" label="记录编号" width="120" />
        <el-table-column prop="pipelineName" label="管网名称" />
        <el-table-column prop="location" label="位置" width="120" />
        <el-table-column prop="inspector" label="巡检员" width="100" />
        <el-table-column prop="inspectionTime" label="巡检时间" width="150" />
        <el-table-column prop="result" label="巡检结果" width="100">
          <template #default="{ row }">
            <el-tag :type="getResultType(row.result)">
              {{ getResultText(row.result) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="issueCount" label="问题数量" width="100">
          <template #default="{ row }">
            <el-badge
              :value="row.issueCount"
              :type="row.issueCount > 0 ? 'danger' : 'primary'"
            >
              <span>{{ row.issueCount }}</span>
            </el-badge>
          </template>
        </el-table-column>
        <el-table-column prop="weather" label="天气情况" width="100" />
        <el-table-column prop="duration" label="巡检时长" width="100">
          <template #default="{ row }"> {{ row.duration }}分钟 </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)"
              >查看</el-button
            >
            <el-button type="success" size="small" @click="handleEdit(row)"
              >编辑</el-button
            >
            <el-button type="warning" size="small" @click="handleReport(row)"
              >生成报告</el-button
            >
            <el-button type="danger" size="small" @click="handleDelete(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 记录详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="巡检记录详情" width="800px">
      <div v-if="currentRecord" class="record-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="记录编号">{{
            currentRecord.recordNo
          }}</el-descriptions-item>
          <el-descriptions-item label="管网名称">{{
            currentRecord.pipelineName
          }}</el-descriptions-item>
          <el-descriptions-item label="位置">{{
            currentRecord.location
          }}</el-descriptions-item>
          <el-descriptions-item label="巡检员">{{
            currentRecord.inspector
          }}</el-descriptions-item>
          <el-descriptions-item label="巡检时间">{{
            currentRecord.inspectionTime
          }}</el-descriptions-item>
          <el-descriptions-item label="巡检时长"
            >{{ currentRecord.duration }}分钟</el-descriptions-item
          >
          <el-descriptions-item label="天气情况">{{
            currentRecord.weather
          }}</el-descriptions-item>
          <el-descriptions-item label="巡检结果">
            <el-tag :type="getResultType(currentRecord.result)">
              {{ getResultText(currentRecord.result) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <div class="detail-section">
          <h4>巡检内容</h4>
          <p>{{ currentRecord.description || "正常巡检，未发现异常情况" }}</p>
        </div>

        <div
          v-if="currentRecord.issues && currentRecord.issues.length > 0"
          class="detail-section"
        >
          <h4>发现问题</h4>
          <el-table :data="currentRecord.issues" size="small">
            <el-table-column prop="type" label="问题类型" />
            <el-table-column prop="description" label="问题描述" />
            <el-table-column prop="severity" label="严重程度">
              <template #default="{ row }">
                <el-tag :type="getSeverityType(row.severity)">
                  {{ row.severity }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div
          v-if="currentRecord.photos && currentRecord.photos.length > 0"
          class="detail-section"
        >
          <h4>巡检照片</h4>
          <div class="photo-grid">
            <img
              v-for="(photo, index) in currentRecord.photos"
              :key="index"
              :src="photo"
              alt="巡检照片"
              class="inspection-photo"
            />
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { Check, Warning, Tools, Document } from "@element-plus/icons-vue";

// 搜索表单
const searchForm = reactive({
  recordNo: "",
  pipelineName: "",
  inspector: "",
  result: "",
  dateRange: [],
});

// 统计数据
const stats = reactive({
  normal: 186,
  abnormal: 23,
  repair: 15,
  total: 224,
});

// 记录数据
const recordData = ref([
  {
    id: 1,
    recordNo: "PR202401001",
    pipelineName: "主干道供水管网A段",
    location: "市中心区域",
    inspector: "张三",
    inspectionTime: "2024-01-01 10:30:00",
    result: "normal",
    issueCount: 0,
    weather: "晴朗",
    duration: 45,
    description: "管网运行正常，压力稳定，无泄漏现象",
    issues: [],
    photos: [],
  },
  {
    id: 2,
    recordNo: "PR202401002",
    pipelineName: "工业区排水管网B段",
    location: "工业开发区",
    inspector: "李四",
    inspectionTime: "2024-01-01 14:20:00",
    result: "abnormal",
    issueCount: 2,
    weather: "多云",
    duration: 65,
    description: "发现管网异常，存在轻微渗漏",
    issues: [
      { type: "渗漏", description: "管道接头处轻微渗漏", severity: "轻微" },
      { type: "腐蚀", description: "管道表面轻微腐蚀", severity: "轻微" },
    ],
    photos: [],
  },
]);

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0,
});

// 详情对话框
const detailDialogVisible = ref(false);
const currentRecord = ref<any>(null);

// 获取结果类型
const getResultType = (
  result: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const types: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    normal: "success",
    abnormal: "warning",
    repair: "danger",
  };
  return types[result] || "info";
};

// 获取结果文本
const getResultText = (result: string) => {
  const texts: Record<string, string> = {
    normal: "正常",
    abnormal: "异常",
    repair: "需维修",
  };
  return texts[result] || "未知";
};

// 获取严重程度类型
const getSeverityType = (
  severity: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const types: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    轻微: "info",
    一般: "warning",
    严重: "danger",
  };
  return types[severity] || "info";
};

// 创建记录
const handleCreateRecord = () => {
  console.log("创建巡检记录");
};

// 搜索
const handleSearch = () => {
  console.log("搜索记录", searchForm);
  loadRecordData();
};

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    recordNo: "",
    pipelineName: "",
    inspector: "",
    result: "",
    dateRange: [],
  });
  loadRecordData();
};

// 导出
const handleExport = () => {
  console.log("导出巡检记录");
};

// 查看记录
const handleView = (row: any) => {
  currentRecord.value = row;
  detailDialogVisible.value = true;
};

// 编辑记录
const handleEdit = (row: any) => {
  console.log("编辑记录", row);
};

// 生成报告
const handleReport = (row: any) => {
  console.log("生成巡检报告", row);
};

// 删除记录
const handleDelete = (row: any) => {
  console.log("删除记录", row);
};

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size;
  loadRecordData();
};

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page;
  loadRecordData();
};

// 加载记录数据
const loadRecordData = () => {
  console.log("加载记录数据");
  pagination.total = recordData.value.length;
};

onMounted(() => {
  loadRecordData();
});
</script>

<style scoped>
.pipeline-inspection-record {
  padding: 20px;
}

.page-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stat-icon {
  margin-right: 15px;
  padding: 10px;
  border-radius: 50%;
}

.stat-icon.normal {
  background-color: #f0f9ff;
  color: #67c23a;
}

.stat-icon.abnormal {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.stat-icon.repair {
  background-color: #fef0f0;
  color: #f56c6c;
}

.stat-icon.total {
  background-color: #f5f7fa;
  color: #409eff;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}

.record-detail {
  max-height: 500px;
  overflow-y: auto;
}

.detail-section {
  margin-top: 20px;
}

.detail-section h4 {
  margin-bottom: 10px;
  color: #333;
  border-left: 4px solid #409eff;
  padding-left: 10px;
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 10px;
  margin-top: 10px;
}

.inspection-photo {
  width: 100%;
  height: 100px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
}
</style>
