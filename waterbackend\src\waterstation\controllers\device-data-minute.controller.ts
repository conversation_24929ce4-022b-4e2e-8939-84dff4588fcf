import { Controller, Get, Query, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiQuery, ApiParam } from '@nestjs/swagger';
import { DeviceDataMinuteService } from '../services/device-data-minute.service';
import { DeviceDataMinute } from '../entities/device-data-minute.entity';

@ApiTags('水利站分钟级数据')
@Controller('device-data-minute')
export class DeviceDataMinuteController {
  constructor(
    private readonly deviceDataMinuteService: DeviceDataMinuteService
  ) {}

  @Get()
  @ApiOperation({ summary: '获取所有分钟级设备数据' })
  @ApiQuery({ name: 'deviceSn', required: false, description: '设备序列号' })
  @ApiQuery({ name: 'startTime', required: false, description: '开始时间 ISO格式' })
  @ApiQuery({ name: 'endTime', required: false, description: '结束时间 ISO格式' })
  async findAll(
    @Query('deviceSn') deviceSn?: string,
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string
  ): Promise<DeviceDataMinute[]> {
    if (startTime && endTime) {
      return this.deviceDataMinuteService.findByTimeRange(
        new Date(startTime), 
        new Date(endTime), 
        deviceSn
      );
    }

    if (deviceSn) {
      return this.deviceDataMinuteService.findByDeviceSn(deviceSn);
    }

    return this.deviceDataMinuteService.findAll();
  }

  @Get('latest')
  @ApiOperation({ summary: '获取最新的分钟级数据' })
  @ApiQuery({ name: 'deviceSn', required: false, description: '设备序列号' })
  async getLatestData(
    @Query('deviceSn') deviceSn?: string
  ): Promise<DeviceDataMinute[]> {
    return this.deviceDataMinuteService.getLatestData(deviceSn);
  }

  @Get('statistics')
  @ApiOperation({ summary: '获取分钟级数据统计信息' })
  @ApiQuery({ name: 'deviceSn', required: false, description: '设备序列号' })
  async getStatistics(@Query('deviceSn') deviceSn?: string) {
    return this.deviceDataMinuteService.getDataStatistics(deviceSn);
  }

  @Get(':deviceSn')
  @ApiOperation({ summary: '根据设备序列号获取分钟级数据' })
  @ApiParam({ name: 'deviceSn', description: '设备序列号' })
  async findByDeviceSn(
    @Param('deviceSn') deviceSn: string
  ): Promise<DeviceDataMinute[]> {
    return this.deviceDataMinuteService.findByDeviceSn(deviceSn);
  }
}