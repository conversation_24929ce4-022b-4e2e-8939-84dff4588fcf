/// <reference types="multer" />
import { AuthService } from './auth/auth.service';
import { AppService } from './app.service';
export declare class AppController {
    private readonly authService;
    private readonly appService;
    constructor(authService: AuthService, appService: AppService);
    login(loginDto: any, req: any): Promise<{
        code: number;
        data: {
            accessToken: string;
            tokenType: string;
            refreshToken: string;
            expires: number;
        };
        msg: string;
    }>;
    logout(req: any): Promise<{
        code: number;
        data: any;
        msg: string;
    }>;
    getProfile(req: any): Promise<{
        code: number;
        data: any;
        msg: string;
    }>;
    getRoutesList(req: any): Promise<{
        code: number;
        data: {}[];
        msg: string;
    }>;
    uploadFile(file: Express.Multer.File): Promise<{
        code: number;
        data: {
            filePath: string;
        };
        msg: string;
    }>;
}
