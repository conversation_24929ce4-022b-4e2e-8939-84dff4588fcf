# 设备状态变化日志记录器
import json
import datetime
import os
from typing import Dict, Any, List, Optional
import threading

# 状态缓存用于检测变化
_device_status_cache = {}
_cache_lock = threading.Lock()

# 日志目录
LOGS_DIR = "./logs"
STATUS_LOGS_DIR = os.path.join(LOGS_DIR, "device_status")

# 确保日志目录存在
os.makedirs(STATUS_LOGS_DIR, exist_ok=True)

def log_device_status_changes(
    device_sn: str, 
    device_data: Dict[str, Any], 
    client_address: str,
    is_status_only_message: bool = False
) -> List[Dict[str, Any]]:
    """
    检测并记录设备状态变化
    
    :param device_sn: 设备序列号
    :param device_data: 设备数据
    :param client_address: 客户端地址
    :param is_status_only_message: 是否为单独的状态变化消息
    :return: 状态变化列表
    """
    changes = []
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 需要监控的状态字段
    status_fields = [
        'DO21_status', 'DO22_status', 'DO23_status', 'DO24_status'
    ]
    
    # 需要监控的模式字段（水泵、气泵的三档位状态）
    mode_fields = [
        'water_pump1', 'water_pump2', 'air_pump1', 'air_pump2'
    ]
    
    with _cache_lock:
        # 获取设备的历史状态
        old_status = _device_status_cache.get(device_sn, {})
        
        # 检测DO状态变化
        for field in status_fields:
            if field in device_data:
                new_value = device_data[field]
                old_value = old_status.get(field)
                
                if old_value is not None and old_value != new_value:
                    change_type = "开启" if new_value == 1 else "关闭"
                    change_record = {
                        'field': field,
                        'old_value': old_value,
                        'new_value': new_value,
                        'change_type': change_type,
                        'timestamp': current_time
                    }
                    changes.append(change_record)
                    print(f"[{current_time}] [状态变化检测] 设备{device_sn} {field} {old_value}->{new_value} ({change_type})")
        
        # 检测模式状态变化（三档位）
        for field in mode_fields:
            if field in device_data and isinstance(device_data[field], dict):
                mode_status = device_data[field].get('mode_status')
                if mode_status is not None:
                    old_mode = old_status.get(f"{field}_mode", old_status.get(field, {}).get('mode_status'))
                    
                    if old_mode is not None and old_mode != mode_status:
                        mode_names = {
                            0: "停止模式(强制关闭)",
                            1: "手动模式(强制启动)", 
                            2: "自动模式(按规则运行)"
                        }
                        old_mode_name = mode_names.get(old_mode, f"未知模式({old_mode})")
                        new_mode_name = mode_names.get(mode_status, f"未知模式({mode_status})")
                        
                        change_record = {
                            'field': field,
                            'sub_field': 'mode_status',
                            'old_value': old_mode,
                            'new_value': mode_status,
                            'old_mode_name': old_mode_name,
                            'new_mode_name': new_mode_name,
                            'change_type': 'mode_change',
                            'timestamp': current_time
                        }
                        changes.append(change_record)
                        print(f"[{current_time}] [模式变化检测] 设备{device_sn} {field} {old_mode_name}->{new_mode_name}")
        
        # 更新缓存
        new_status = old_status.copy()
        
        # 更新DO状态
        for field in status_fields:
            if field in device_data:
                new_status[field] = device_data[field]
        
        # 更新模式状态
        for field in mode_fields:
            if field in device_data and isinstance(device_data[field], dict):
                mode_status = device_data[field].get('mode_status')
                if mode_status is not None:
                    new_status[f"{field}_mode"] = mode_status
        
        _device_status_cache[device_sn] = new_status
    
    # 如果有状态变化，写入日志文件
    if changes:
        _write_status_change_to_file(device_sn, changes, client_address, is_status_only_message)
    
    return changes

def _write_status_change_to_file(
    device_sn: str,
    changes: List[Dict[str, Any]], 
    client_address: str,
    is_status_only_message: bool
):
    """将状态变化写入日志文件"""
    try:
        # 生成日志文件名（按日期）
        today = datetime.datetime.now().strftime("%Y%m%d")
        log_file_path = os.path.join(STATUS_LOGS_DIR, f"device_status_{today}.log")
        
        # 构造日志记录
        log_entry = {
            'timestamp': datetime.datetime.now().isoformat(),
            'device_sn': device_sn,
            'client_address': client_address,
            'change_count': len(changes),
            'changes': changes,
            'log_type': '设备物理开关档位变换' if is_status_only_message else '设备状态数据变化'
        }
        
        # 写入文件
        with open(log_file_path, 'a', encoding='utf-8') as f:
            f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
        
        print(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [状态日志] 设备{device_sn} {len(changes)}个状态变化已记录到 {log_file_path}")
    
    except Exception as e:
        print(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [状态日志错误] 写入状态日志失败: {e}")

def get_device_current_status(device_sn: str) -> Dict[str, Any]:
    """获取设备当前状态"""
    with _cache_lock:
        return _device_status_cache.get(device_sn, {}).copy()

def clear_device_status_cache(device_sn: Optional[str] = None):
    """清空设备状态缓存"""
    with _cache_lock:
        if device_sn:
            _device_status_cache.pop(device_sn, None)
        else:
            _device_status_cache.clear()