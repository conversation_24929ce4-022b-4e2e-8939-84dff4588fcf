# Python后端架构文档

## 概述

本文档详细描述了基于FastAPI的设备数据管理和控制系统的后端架构。该系统提供REST API接口，支持设备数据查询、实时控制、任务调度和Node-RED配置管理等功能。

## 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        Python后端系统                            │
├─────────────────────────────────────────────────────────────────┤
│  API层 (FastAPI)                                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   REST API      │  │   WebSocket     │  │   CORS中间件    │ │
│  │   接口服务       │  │   实时通信       │  │   跨域支持       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  业务逻辑层                                                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   设备控制       │  │   任务调度       │  │   数据处理       │ │
│  │   device_control │  │   scheduler     │  │   data_store    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  数据层                                                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   内存缓存       │  │   SQLite数据库   │  │   文件存储       │ │
│  │   实时数据       │  │   历史数据       │  │   配置文件       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  通信层                                                          │
│  ┌─────────────────┐  ┌─────────────────┐                      │
│  │   TCP客户端      │  │   HTTP服务器     │                      │
│  │   设备通信       │  │   API服务        │                      │
│  └─────────────────┘  └─────────────────┘                      │
└─────────────────────────────────────────────────────────────────┘
```

## 核心模块详解

### 1. API服务器 (api_server.py)

#### 1.1 FastAPI应用配置

```python
app = FastAPI(
    title="设备数据API",
    description="用于查询通过TCP服务器收集的最新设备数据和历史数据的API。新增了DO控制和任务调度功能。",
    version="1.2.0",
)
```

#### 1.2 CORS跨域配置

```python
origins = [
    "http://jscn.sz-hgy.com",  # 生产环境前端地址
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

#### 1.3 DO设备映射

```python
DO_TO_DEVICE_MAPPING = {
    "DO21": "water_pump1",
    "DO22": "water_pump2", 
    "DO23": "air_pump1",
    "DO24": "air_pump2",
}
```

### 2. 数据存储模块 (data_store.py)

#### 2.1 内存数据结构

```python
# 全局数据存储
DEVICE_DATA = {}  # 设备实时数据
CONNECTED_CLIENTS = {}  # 已连接客户端
DATA_LOCK = threading.Lock()  # 数据访问锁
CLIENTS_LOCK = threading.Lock()  # 客户端访问锁
```

#### 2.2 数据存储逻辑

```python
def store_device_data(sn: str, data: dict):
    """存储设备数据到内存和数据库"""
    with DATA_LOCK:
        # 更新内存数据
        DEVICE_DATA[sn] = {
            **data,
            'last_update': datetime.datetime.now(),
            'connection_status': 'connected'
        }
    
    # 异步存储到数据库
    asyncio.create_task(save_to_database(sn, data))
```

### 3. 设备控制模块 (device_control.py)

#### 3.1 DO控制命令生成

```python
def create_do_command(do_name: str, value: int) -> str:
    """创建DO控制命令"""
    if do_name not in ["DO21", "DO22", "DO23", "DO24", "DO01"]:
        raise ValueError(f"无效的DO名称: {do_name}")
    
    if value not in [0, 1]:
        raise ValueError(f"无效的DO值: {value}")
    
    return f"{do_name}={value}"
```

#### 3.2 命令发送逻辑

```python
async def send_command_to_device(sn: str, command: str) -> tuple[bool, str]:
    """发送控制命令到设备"""
    try:
        # 检查设备连接状态
        if sn not in CONNECTED_CLIENTS:
            return False, "设备未连接"
        
        # 获取TCP连接
        client_socket = CONNECTED_CLIENTS[sn]['socket']
        
        # 发送命令
        command_data = {
            "type": "control",
            "command": command,
            "timestamp": datetime.datetime.now().isoformat()
        }
        
        await client_socket.send(json.dumps(command_data))
        return True, "命令发送成功"
        
    except Exception as e:
        return False, f"命令发送失败: {str(e)}"
```

### 4. 任务调度模块 (scheduler_service.py)

#### 4.1 延时任务调度

```python
class DelayedTask:
    def __init__(self, sn: str, do_name: str, value: int, delay_minutes: int):
        self.sn = sn
        self.do_name = do_name
        self.value = value
        self.execute_time = datetime.datetime.now() + datetime.timedelta(minutes=delay_minutes)
        self.task_id = str(uuid.uuid4())
    
    async def execute(self):
        """执行延时任务"""
        command = create_do_command(self.do_name, self.value)
        success, message = await send_command_to_device(self.sn, command)
        
        return {
            "task_id": self.task_id,
            "success": success,
            "message": message,
            "executed_at": datetime.datetime.now().isoformat()
        }
```

#### 4.2 循环任务调度

```python
class CycleTask:
    def __init__(self, sn: str, do_name: str, on_minutes: int, off_minutes: int):
        self.sn = sn
        self.do_name = do_name
        self.on_minutes = on_minutes
        self.off_minutes = off_minutes
        self.task_id = str(uuid.uuid4())
        self.is_running = False
    
    async def start_cycle(self):
        """启动循环任务"""
        self.is_running = True
        
        while self.is_running:
            # 开启阶段
            await send_command_to_device(self.sn, create_do_command(self.do_name, 1))
            await asyncio.sleep(self.on_minutes * 60)
            
            # 关闭阶段
            if self.is_running:
                await send_command_to_device(self.sn, create_do_command(self.do_name, 0))
                await asyncio.sleep(self.off_minutes * 60)
```

### 5. 数据库模块 (database.py & models.py)

#### 5.1 数据库配置

```python
# SQLite数据库配置
SQLALCHEMY_DATABASE_URL = "sqlite:///./device_data.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False}
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()
```

#### 5.2 数据模型

```python
class DeviceData(Base):
    __tablename__ = "device_data"
    
    id = Column(Integer, primary_key=True, index=True)
    device_sn = Column(String, index=True)
    timestamp = Column(DateTime, default=datetime.datetime.utcnow)
    raw_data = Column(JSON)
    data_type = Column(String)  # 数据类型标识
    
    # 索引优化
    __table_args__ = (
        Index('idx_device_timestamp', 'device_sn', 'timestamp'),
        Index('idx_timestamp', 'timestamp'),
    )
```

## API接口详解

### 1. 实时数据接口

#### 1.1 获取所有设备数据

```python
@app.get("/data", tags=["实时数据"])
async def get_all_device_data():
    """获取所有已连接设备上报的最新数据"""
    with DATA_LOCK:
        return copy.deepcopy(DEVICE_DATA)
```

#### 1.2 获取指定设备数据

```python
@app.get("/data/{sn}", tags=["实时数据"])
async def get_device_data(sn: str):
    """获取指定设备的最新数据"""
    with DATA_LOCK:
        device_data = DEVICE_DATA.get(sn)
        
    if not device_data:
        raise HTTPException(
            status_code=404,
            detail=f"设备SN '{sn}' 未连接或尚未上报数据"
        )
    
    return copy.deepcopy(device_data)
```

#### 1.3 获取连接设备列表

```python
@app.get("/connected-devices", tags=["设备管理"])
async def get_connected_devices():
    """获取当前已连接的设备列表"""
    with CLIENTS_LOCK:
        devices = []
        for sn, client_info in CONNECTED_CLIENTS.items():
            devices.append({
                "sn": sn,
                "connected_at": client_info.get('connected_at'),
                "last_heartbeat": client_info.get('last_heartbeat'),
                "ip_address": client_info.get('ip_address')
            })
    
    return {"connected_devices": devices, "total_count": len(devices)}
```

### 2. 设备控制接口

#### 2.1 DO控制

```python
@app.post("/control/{sn}/do", tags=["设备控制"])
async def control_device_do(sn: str, request: DOControlRequest):
    """控制指定设备的DO输出"""
    # 检查自动模式状态
    _check_auto_status(sn, request.do_name)
    
    # 等待设备就绪
    wait_for_device_ready(sn)
    
    # 创建并发送控制命令
    command = create_do_command(request.do_name, request.value)
    success, message = send_command_to_device(sn, command)
    
    if not success:
        raise HTTPException(status_code=500, detail=f"控制命令执行失败: {message}")
    
    return {
        "success": True,
        "message": f"DO控制命令已发送: {request.do_name}={request.value}",
        "command": command,
        "timestamp": datetime.datetime.now().isoformat()
    }
```

#### 2.2 延时控制任务

```python
@app.post("/schedule/{sn}/do", tags=["任务调度"])
async def schedule_do_control(sn: str, request: ScheduleRequest):
    """安排延时DO控制任务"""
    # 检查自动模式状态
    _check_auto_status(sn, request.do_name)
    
    # 创建延时任务
    task = DelayedTask(sn, request.do_name, request.value, request.delay_minutes)
    
    # 添加到调度器
    scheduler_service.add_delayed_task(task)
    
    return {
        "success": True,
        "task_id": task.task_id,
        "message": f"延时任务已安排，将在{request.delay_minutes}分钟后执行",
        "execute_time": task.execute_time.isoformat()
    }
```

#### 2.3 循环控制任务

```python
@app.post("/schedule/{sn}/cycle", tags=["任务调度"])
async def schedule_cycle_control(sn: str, request: CycleScheduleRequest):
    """安排循环DO控制任务"""
    # 检查自动模式状态
    _check_auto_status(sn, request.do_name)
    
    # 创建循环任务
    task = CycleTask(sn, request.do_name, request.on_minutes, request.off_minutes)
    
    # 启动循环任务
    asyncio.create_task(task.start_cycle())
    scheduler_service.add_cycle_task(task)
    
    return {
        "success": True,
        "task_id": task.task_id,
        "message": f"循环任务已启动，开启{request.on_minutes}分钟，关闭{request.off_minutes}分钟"
    }
```

### 3. 历史数据接口

#### 3.1 分页查询历史数据

```python
@app.get("/history/{sn}", response_model=HistoryView, tags=["历史数据"])
async def get_device_history(
    sn: str,
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """分页获取指定设备的历史数据"""
    # 计算偏移量
    offset = (page - 1) * size
    
    # 查询总数
    total = crud.get_device_history_count(db, sn)
    
    # 查询数据
    items = crud.get_device_history(db, sn, skip=offset, limit=size)
    
    return HistoryView(total=total, items=items)
```

#### 3.2 时间范围查询

```python
@app.get("/history/{sn}/range", tags=["历史数据"])
async def get_device_history_by_time_range(
    sn: str,
    start_time: datetime.datetime,
    end_time: datetime.datetime,
    db: Session = Depends(get_db)
):
    """按时间范围获取设备历史数据"""
    if start_time >= end_time:
        raise HTTPException(
            status_code=400,
            detail="开始时间必须早于结束时间"
        )
    
    # 限制查询时间范围（最多30天）
    if (end_time - start_time).days > 30:
        raise HTTPException(
            status_code=400,
            detail="查询时间范围不能超过30天"
        )
    
    items = crud.get_device_history_by_time_range(db, sn, start_time, end_time)
    
    return {
        "device_sn": sn,
        "start_time": start_time.isoformat(),
        "end_time": end_time.isoformat(),
        "total_records": len(items),
        "data": items
    }
```

### 4. Node-RED配置管理接口

#### 4.1 获取Node-RED流程配置

```python
@app.get("/node-red-config/flows-only", tags=["Node-RED配置"])
async def get_node_red_flows_only():
    """仅返回Node-RED流程配置数据，不包含元数据"""
    try:
        with open(NODE_RED_FLOWS_FILE, 'r', encoding='utf-8') as f:
            flows_data = json.load(f)
        
        # 获取文件修改时间作为版本号
        file_stat = os.stat(NODE_RED_FLOWS_FILE)
        version = datetime.datetime.fromtimestamp(file_stat.st_mtime).strftime("%Y%m%d_%H%M%S")
        
        return {
            "success": True,
            "version": version,
            "flows": flows_data,
            "metadata": {
                "file_size": file_stat.st_size,
                "modified_time": datetime.datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                "flow_count": len(flows_data),
                "checksum": hash(str(flows_data)) % (10**8)
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"读取Node-RED配置失败: {str(e)}")
```

#### 4.2 更新Node-RED配置

```python
@app.post("/node-red-config/update", tags=["Node-RED配置"])
async def update_node_red_config(config: UpdateFlowsRequest):
    """更新Node-RED流程配置"""
    try:
        # 验证flows格式
        if not isinstance(config.flows, list):
            raise HTTPException(status_code=400, detail="flows必须是数组格式")
        
        # 备份当前配置
        if config.backup and os.path.exists(NODE_RED_FLOWS_FILE):
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"flows_backup_{timestamp}.json"
            backup_path = os.path.join(NODE_RED_CONFIG_DIR, backup_filename)
            
            shutil.copy2(NODE_RED_FLOWS_FILE, backup_path)
        
        # 保存新配置
        with open(NODE_RED_FLOWS_FILE, 'w', encoding='utf-8') as f:
            json.dump(config.flows, f, ensure_ascii=False, indent=2)
        
        return {
            "success": True,
            "message": "Node-RED配置更新成功",
            "backup_created": config.backup,
            "updated_at": datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新Node-RED配置失败: {str(e)}")
```

## 数据模型定义

### 1. 请求模型

```python
class DOControlRequest(BaseModel):
    do_name: str = Field(..., description="要控制的DO名称")
    value: int = Field(..., ge=0, le=1, description="目标状态")

class ScheduleRequest(BaseModel):
    sn: str = Field(..., description="设备序列号")
    do_name: str = Field(..., description="要控制的DO名称")
    value: int = Field(..., ge=0, le=1, description="目标状态")
    delay_minutes: int = Field(..., gt=0, description="延迟分钟数")

class CycleScheduleRequest(BaseModel):
    sn: str = Field(..., description="设备序列号")
    do_name: str = Field(..., description="要控制的DO名称")
    on_minutes: int = Field(..., gt=0, description="开启持续分钟数")
    off_minutes: int = Field(..., gt=0, description="关闭持续分钟数")
```

### 2. 响应模型

```python
class HistoryLog(BaseModel):
    id: int
    device_sn: str
    timestamp: datetime.datetime
    raw_data: Any
    
    class Config:
        from_attributes = True

class HistoryView(BaseModel):
    total: int
    items: List[HistoryLog]
```

## 安全和权限控制

### 1. 自动模式检查

```python
def _check_auto_status(sn: str, do_name: str):
    """检查设备是否处于自动模式"""
    device_key = DO_TO_DEVICE_MAPPING.get(do_name)
    if not device_key:
        raise HTTPException(status_code=400, detail=f"无效的DO名称: {do_name}")
    
    with DATA_LOCK:
        device_data = copy.deepcopy(DEVICE_DATA.get(sn))
    
    if not device_data:
        raise HTTPException(status_code=404, detail=f"设备SN '{sn}' 未连接")
    
    module_status = device_data.get(device_key)
    if not module_status or module_status.get("auto_status") != 1:
        raise HTTPException(
            status_code=403,
            detail=f"设备模块 '{device_key}' 未处于自动模式"
        )
```

### 2. 设备状态验证

```python
def validate_device_connection(sn: str) -> bool:
    """验证设备连接状态"""
    with CLIENTS_LOCK:
        if sn not in CONNECTED_CLIENTS:
            return False
        
        # 检查心跳时间
        last_heartbeat = CONNECTED_CLIENTS[sn].get('last_heartbeat')
        if last_heartbeat:
            time_diff = datetime.datetime.now() - last_heartbeat
            return time_diff.total_seconds() < 60  # 60秒内有心跳
    
    return False
```

## 性能优化

### 1. 数据库优化

```python
# 数据库索引优化
class DeviceData(Base):
    __table_args__ = (
        Index('idx_device_timestamp', 'device_sn', 'timestamp'),
        Index('idx_timestamp', 'timestamp'),
        Index('idx_device_sn', 'device_sn'),
    )

# 查询优化
def get_device_history_optimized(db: Session, sn: str, skip: int = 0, limit: int = 20):
    """优化的历史数据查询"""
    return db.query(DeviceData)\
        .filter(DeviceData.device_sn == sn)\
        .order_by(DeviceData.timestamp.desc())\
        .offset(skip)\
        .limit(limit)\
        .all()
```

### 2. 内存管理

```python
# 内存数据清理
async def cleanup_old_data():
    """定期清理过期的内存数据"""
    current_time = datetime.datetime.now()
    
    with DATA_LOCK:
        expired_devices = []
        for sn, data in DEVICE_DATA.items():
            last_update = data.get('last_update')
            if last_update and (current_time - last_update).total_seconds() > 300:  # 5分钟
                expired_devices.append(sn)
        
        for sn in expired_devices:
            del DEVICE_DATA[sn]
```

### 3. 异步处理

```python
# 异步任务处理
async def process_device_data_async(sn: str, data: dict):
    """异步处理设备数据"""
    # 数据验证
    validated_data = await validate_device_data(data)
    
    # 存储到内存
    store_device_data(sn, validated_data)
    
    # 异步存储到数据库
    asyncio.create_task(save_to_database_async(sn, validated_data))
    
    # 触发数据处理事件
    await trigger_data_processing_events(sn, validated_data)
```

## 监控和日志

### 1. 系统监控

```python
@app.get("/system/status", tags=["系统监控"])
async def get_system_status():
    """获取系统运行状态"""
    return {
        "server_time": datetime.datetime.now().isoformat(),
        "connected_devices": len(CONNECTED_CLIENTS),
        "active_tasks": scheduler_service.get_active_task_count(),
        "memory_usage": get_memory_usage(),
        "database_status": check_database_connection()
    }
```

### 2. 日志记录

```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('api_server.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# 使用日志
logger.info(f"设备 {sn} 连接成功")
logger.warning(f"设备 {sn} 控制命令执行失败: {error_message}")
logger.error(f"数据库操作异常: {str(e)}")
```

## 部署和配置

### 1. 环境配置

```python
# 环境变量配置
import os
from pathlib import Path

# 服务器配置
API_HOST = os.getenv("API_HOST", "0.0.0.0")
API_PORT = int(os.getenv("API_PORT", 8500))
TCP_PORT = int(os.getenv("TCP_PORT", 8889))

# 数据库配置
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./device_data.db")

# Node-RED配置
NODE_RED_CONFIG_DIR = Path(os.getenv("NODE_RED_CONFIG_DIR", "/data"))
NODE_RED_FLOWS_FILE = NODE_RED_CONFIG_DIR / "flows.json"
```

### 2. 启动脚本

```python
if __name__ == "__main__":
    import uvicorn
    
    # 启动API服务器
    uvicorn.run(
        "api_server:app",
        host=API_HOST,
        port=API_PORT,
        reload=False,
        access_log=True
    )
```

### 3. Docker部署

```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8500 8889

CMD ["python", "api_server.py"]
```

## 扩展和维护

### 1. 功能扩展

- **新设备类型支持**: 通过配置文件添加新的设备类型
- **协议扩展**: 支持Modbus、MQTT等多种通信协议
- **算法集成**: 集成机器学习算法进行预测性维护

### 2. 系统维护

- **数据备份**: 定期备份数据库和配置文件
- **性能监控**: 监控API响应时间和系统资源使用
- **安全更新**: 定期更新依赖包和安全补丁

本Python后端系统为工业设备管理提供了完整、高性能、可扩展的API服务，支持实时数据查询、设备控制、任务调度等核心功能。