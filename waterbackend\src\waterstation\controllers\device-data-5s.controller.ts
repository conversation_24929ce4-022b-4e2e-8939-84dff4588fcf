import { Controller, Get, Query, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiQuery, ApiParam } from '@nestjs/swagger';
import { DeviceData5sService } from '../services/device-data-5s.service';
import { DeviceData5s } from '../schemas/device-data-5s.schema';

@ApiTags('水利站实时数据(5秒级)')
@Controller('device-data-realtime')
export class DeviceData5sController {
  constructor(
    private readonly deviceData5sService: DeviceData5sService
  ) {}

  @Get()
  @ApiOperation({ summary: '获取实时设备数据' })
  @ApiQuery({ name: 'deviceSn', required: false, description: '设备序列号' })
  @ApiQuery({ name: 'startTime', required: false, description: '开始时间 ISO格式' })
  @ApiQuery({ name: 'endTime', required: false, description: '结束时间 ISO格式' })
  async findAll(
    @Query('deviceSn') deviceSn?: string,
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string
  ): Promise<DeviceData5s[]> {
    if (startTime && endTime) {
      return this.deviceData5sService.findByTimeRange(
        new Date(startTime), 
        new Date(endTime), 
        deviceSn
      );
    }

    if (deviceSn) {
      return this.deviceData5sService.findByDeviceSn(deviceSn);
    }

    return this.deviceData5sService.findAll();
  }

  @Get('latest')
  @ApiOperation({ summary: '获取最新的实时数据' })
  @ApiQuery({ name: 'deviceSn', required: false, description: '设备序列号' })
  async getLatestData(
    @Query('deviceSn') deviceSn?: string
  ): Promise<DeviceData5s[]> {
    return this.deviceData5sService.getLatestData(deviceSn);
  }

  @Get('realtime')
  @ApiOperation({ summary: '获取实时数据(最近5分钟)' })
  @ApiQuery({ name: 'deviceSn', required: false, description: '设备序列号' })
  async getRealTimeData(
    @Query('deviceSn') deviceSn?: string
  ): Promise<DeviceData5s[]> {
    return this.deviceData5sService.getRealTimeData(deviceSn);
  }

  @Get('statistics')
  @ApiOperation({ summary: '获取实时数据统计信息' })
  @ApiQuery({ name: 'deviceSn', required: false, description: '设备序列号' })
  async getStatistics(@Query('deviceSn') deviceSn?: string) {
    return this.deviceData5sService.getDataStatistics(deviceSn);
  }

  @Get('devices')
  @ApiOperation({ summary: '获取设备列表' })
  async getDeviceList(): Promise<string[]> {
    return this.deviceData5sService.getDeviceList();
  }

  @Get(':deviceSn')
  @ApiOperation({ summary: '根据设备序列号获取实时数据' })
  @ApiParam({ name: 'deviceSn', description: '设备序列号' })
  async findByDeviceSn(
    @Param('deviceSn') deviceSn: string
  ): Promise<DeviceData5s[]> {
    return this.deviceData5sService.findByDeviceSn(deviceSn);
  }
}