<?php
//获取子目录下的api.php文件
$dir = dirname(__FILE__);
print_r($dir);

$filelist= opendir($dir);
while (false !== ($file = readdir($filelist))) {
     if(is_dir($file)){
        //获取此目录下的api.php文件
        if( is_file($file."/api.php")){
            echo "\nfindfile:".$file."/api.php"."\n";
            fixfile($file."/api.php");
        }
        
     }
}


//处理文件
function  fixfile($filepath){
    $str = file_get_contents($filepath);
    //备份文件
    $bakfile = $filepath."20220409.bak";
    // file_put_contents($bakfile,$str);

    //删除 class api
    $str= preg_replace("/class api{/","",$str);

       // 删除 public $db;
       $str= preg_replace("/public.*/","",$str);
    //删除 function  __construct(){ }
    $str=preg_replace("/function __construct\(\)\{([\s\S]+?)\}/",'${1}',$str);
    
    // 把全部 $this->db 替换为  $GLOBALS['db'] 
    $str=str_replace("\$this->db",'$GLOBALS[\'db\']',$str);
   //替换 	eval('$this->'.$str.'();'); 为  if( function_exists($str  )){call_user_func(  $str,"" ); }
       $str=str_replace('eval(\'$this->\'.$str.\'();\');','if( function_exists($str  )){call_user_func(  $str,"" ); }',$str);


 //替换 $this-> 为 空
    $str=str_replace("\$this->",'',$str);
   
        //删除 $app=new api();之前的最后一个花括号
        $str=preg_replace("/\}[\s\S]+(\$app=new api\(\);[\s\S]*)/",'${1}',$str);


    // 删除 $app=new api();
   $str=str_replace("\$app=new api();","",$str);

    //删除 $app->
    $str=str_replace("\$app->","",$str);
    file_put_contents($filepath,$str);

}