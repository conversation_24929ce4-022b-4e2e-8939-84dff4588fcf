import { Injectable } from '@nestjs/common';
import {
  DeleteResult,
  getConnection,
  Repository,
  UpdateResult,
  Not,
} from 'typeorm';
import { User } from './entities/user.entity';
import { UpdateUserDto } from './dto/update-user.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { Role } from 'src/role.enum';

@Injectable()
export class UserService {
  constructor(
    private readonly jwtService: JwtService,
    @InjectRepository(User) private usersRepository: Repository<User>,
  ) {}
  async create(user: User) {
    return await this.usersRepository.save(user);
  }
  async findUser(username) {
    const user = await this.usersRepository.findOne({ username });
    if (user) {
      // admin 和 superadmin 角色拥有所有权限
      if (user.roles === Role.Admin || user.roles === Role.SuperAdmin) {
        user.permissions = ['*']; // 使用 * 表示拥有所有权限
      } else {
        // 对于普通用户，处理其实际权限
        if (typeof user.permissions === 'string') {
          user.permissions = (user.permissions as string)
            .split(',')
            .filter((p) => p);
        }
        // 如果完全没有权限，则设置默认权限
        if (!user.permissions || user.permissions.length === 0) {
          if (user.roles === Role.User) {
            user.permissions = ['/dashboard'];
          }
          // 保存默认权限到数据库
          await this.usersRepository.save(user);
        }
      }
    }
    return user;
  }
  async findOne(id) {
    const out = await this.usersRepository.findOne({ id });
    return out;
  }
  async findAll({ pageNo, pageSize, username, roles, id }): Promise<any> {
    const where = {
      roles: Not(Role.SuperAdmin), // 排除superadmin角色
    };
    if (username) {
      where['username'] = username;
    }
    if (roles) {
      where['roles'] = roles;
    }
    if (id) {
      where['id'] = id;
    }
    const total = await this.usersRepository.count({ where });

    const list: Array<any> = await this.usersRepository.find({
      where,
      take: pageSize,
      skip: (pageNo - 1) * pageSize,
    });

    return {
      total,
      list: list.map((x) => {
        delete x.password;
        return x;
      }),
      pageNo,
      pageSize,
    };
  }
  async update(id: string, user: UpdateUserDto): Promise<UpdateResult> {
    console.log(id, user);
    return await this.usersRepository.update(id, user);
  }

  async delete(id): Promise<DeleteResult> {
    if (id !== '1') return await this.usersRepository.delete(id);
  }

  async getuserbyopenid(openid): Promise<any> {
    console.log(openid);
    const out = await this.usersRepository.findOne({ where: { openid } });
    console.log(out);
    // if (!out) return;
    const payload = {
      username: out.username,
      sub: out.id,
      roles: [out.roles],
    };
    delete out.password;
    return Object.assign({ access_token: this.jwtService.sign(payload) }, out);
  }

  async count(): Promise<number> {
    return await this.usersRepository.count();
  }
  async openid(id, openid: string): Promise<UpdateResult> {
    console.log(id, openid);
    //更新openid  需要先把原来的openid清空
    await getConnection()
      .createQueryBuilder()
      .update('user')
      .set({ openid: null })
      .where('openid = :openid', { openid })
      .execute();
    return await getConnection()
      .createQueryBuilder()
      .update('user')
      .set({ openid })
      .where('id = :id', { id })
      .execute();
  }
  async cancellation(id): Promise<UpdateResult> {
    console.log(id);
    await getConnection()
      .createQueryBuilder()
      .delete()
      .from('user')
      .where('id = :id', { id })
      .execute();
    return;
  } // 取消绑定删除用户

  async clearopenid(id): Promise<UpdateResult> {
    console.log(id);
    return await getConnection()
      .createQueryBuilder()
      .update('user')
      .set({ openid: null })
      .where('id = :id', { id })
      .execute();
  } // 清空openid
}
