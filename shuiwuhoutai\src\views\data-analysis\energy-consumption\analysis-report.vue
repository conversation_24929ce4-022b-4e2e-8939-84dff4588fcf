<template>
  <div class="energy-analysis-report">
    <el-card shadow="hover" class="page-header">
      <template #header>
        <div class="card-header">
          <div class="header-actions">
            <el-button type="primary" size="small" @click="generateReport"
              >生成报表</el-button
            >
            <el-button type="success" size="small" @click="scheduleReport"
              >定时报表</el-button
            >
          </div>
        </div>
      </template>

      <!-- 报表配置表单 -->
      <el-form :inline="true" :model="reportForm" class="report-form">
        <el-form-item label="报表类型">
          <el-select
            v-model="reportForm.reportType"
            placeholder="请选择报表类型"
          >
            <el-option label="日报" value="daily" />
            <el-option label="周报" value="weekly" />
            <el-option label="月报" value="monthly" />
            <el-option label="年报" value="yearly" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="报表维度">
          <el-select
            v-model="reportForm.dimension"
            placeholder="请选择维度"
            multiple
          >
            <el-option label="按区域" value="area" />
            <el-option label="按站点" value="site" />
            <el-option label="按设备" value="equipment" />
            <el-option label="按时间" value="time" />
          </el-select>
        </el-form-item>

        <el-form-item label="输出格式">
          <el-select v-model="reportForm.format" placeholder="请选择格式">
            <el-option label="Excel" value="excel" />
            <el-option label="PDF" value="pdf" />
            <el-option label="Word" value="word" />
            <el-option label="HTML" value="html" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 报表摘要信息 -->
    <el-card shadow="hover" class="summary-card">
      <template #header>
        <div class="card-header">
          <span>报表摘要</span>
          <span class="report-period">{{ currentPeriod }}</span>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col :span="6">
          <div class="summary-item">
            <div class="summary-icon electricity">⚡</div>
            <div class="summary-content">
              <div class="summary-value">{{ summary.totalElectricity }}</div>
              <div class="summary-label">总电力消耗(kWh)</div>
              <div class="summary-change">
                <span
                  :class="
                    summary.electricityChange >= 0 ? 'increase' : 'decrease'
                  "
                >
                  {{ summary.electricityChange >= 0 ? "+" : ""
                  }}{{ summary.electricityChange }}%
                </span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="summary-item">
            <div class="summary-icon cost">💰</div>
            <div class="summary-content">
              <div class="summary-value">{{ summary.totalCost }}</div>
              <div class="summary-label">总能耗成本(万元)</div>
              <div class="summary-change">
                <span
                  :class="summary.costChange >= 0 ? 'increase' : 'decrease'"
                >
                  {{ summary.costChange >= 0 ? "+" : ""
                  }}{{ summary.costChange }}%
                </span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="summary-item">
            <div class="summary-icon efficiency">📊</div>
            <div class="summary-content">
              <div class="summary-value">{{ summary.avgEfficiency }}%</div>
              <div class="summary-label">平均能效</div>
              <div class="summary-change">
                <span
                  :class="
                    summary.efficiencyChange >= 0 ? 'increase' : 'decrease'
                  "
                >
                  {{ summary.efficiencyChange >= 0 ? "+" : ""
                  }}{{ summary.efficiencyChange }}%
                </span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="summary-item">
            <div class="summary-icon carbon">🌱</div>
            <div class="summary-content">
              <div class="summary-value">{{ summary.carbonReduction }}</div>
              <div class="summary-label">碳减排量(t)</div>
              <div class="summary-change">
                <span class="decrease"
                  >-{{ summary.carbonReductionRate }}%</span
                >
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 能耗分析图表区域 -->
    <div class="analysis-charts">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>能耗趋势分析</span>
                <el-button-group size="small">
                  <el-button
                    :type="chartPeriod === 'day' ? 'primary' : ''"
                    @click="chartPeriod = 'day'"
                    >日</el-button
                  >
                  <el-button
                    :type="chartPeriod === 'week' ? 'primary' : ''"
                    @click="chartPeriod = 'week'"
                    >周</el-button
                  >
                  <el-button
                    :type="chartPeriod === 'month' ? 'primary' : ''"
                    @click="chartPeriod = 'month'"
                    >月</el-button
                  >
                </el-button-group>
              </div>
            </template>
            <div id="energyTrendAnalysisChart" style="height: 350px"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>能耗结构分析</span>
              </div>
            </template>
            <div id="energyStructureAnalysisChart" style="height: 350px"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="24">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>能耗对比分析</span>
                <el-select v-model="compareType" size="small">
                  <el-option label="区域对比" value="area" />
                  <el-option label="站点对比" value="site" />
                  <el-option label="时间对比" value="time" />
                </el-select>
              </div>
            </template>
            <div id="energyComparisonChart" style="height: 350px"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 能耗报表数据表格 -->
    <el-card shadow="hover" class="table-card">
      <template #header>
        <div class="card-header">
          <span>能耗报表数据</span>
          <div class="table-actions">
            <el-button size="small" @click="exportExcel">导出Excel</el-button>
            <el-button size="small" @click="exportPDF">导出PDF</el-button>
            <el-button size="small" type="primary" @click="printReport"
              >打印报表</el-button
            >
          </div>
        </div>
      </template>

      <el-table :data="reportTableData" stripe style="width: 100%" border>
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="period" label="统计期间" width="120" />
        <el-table-column prop="siteName" label="站点名称" width="120" />
        <el-table-column prop="area" label="区域" width="80" />
        <el-table-column
          prop="electricityConsumption"
          label="电力消耗(kWh)"
          width="130"
          sortable
        />
        <el-table-column
          prop="fuelConsumption"
          label="燃料消耗(L)"
          width="120"
          sortable
        />
        <el-table-column
          prop="waterConsumption"
          label="水耗(m³)"
          width="100"
          sortable
        />
        <el-table-column
          prop="totalCost"
          label="总成本(元)"
          width="120"
          sortable
        />
        <el-table-column prop="unitCost" label="单位成本" width="100" />
        <el-table-column prop="efficiency" label="能效比(%)" width="100">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.efficiency"
              :color="getEfficiencyColor(scope.row.efficiency)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="carbonEmission" label="碳排放(kg)" width="120" />
        <el-table-column prop="performanceLevel" label="能耗等级" width="100">
          <template #default="scope">
            <el-tag :type="getPerformanceLevelType(scope.row.performanceLevel)">
              {{ scope.row.performanceLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="notes" label="备注" min-width="150" />
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 分析结论和建议 -->
    <el-card shadow="hover" class="conclusion-card">
      <template #header>
        <div class="card-header">
          <span>分析结论与建议</span>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col :span="12">
          <div class="conclusion-section">
            <h4>主要发现</h4>
            <ul class="conclusion-list">
              <li v-for="(finding, index) in analysisFindings" :key="index">
                <el-icon><InfoFilled /></el-icon>
                {{ finding }}
              </li>
            </ul>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="conclusion-section">
            <h4>优化建议</h4>
            <ul class="recommendation-list">
              <li
                v-for="(recommendation, index) in recommendations"
                :key="index"
              >
                <el-icon><Star /></el-icon>
                {{ recommendation }}
              </li>
            </ul>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { InfoFilled, Star } from "@element-plus/icons-vue";

// 报表配置表单
const reportForm = reactive({
  reportType: "monthly",
  dimension: ["area", "site"],
  format: "excel",
});

// 时间范围
const dateRange = ref([]);

// 图表类型
const chartPeriod = ref("month");
const compareType = ref("area");

// 当前统计期间
const currentPeriod = ref("2024年1月 - 能耗分析报表");

// 摘要数据
const summary = reactive({
  totalElectricity: 25680.5,
  electricityChange: -3.2,
  totalCost: 68.5,
  costChange: -2.1,
  avgEfficiency: 82.3,
  efficiencyChange: 4.8,
  carbonReduction: 12.6,
  carbonReductionRate: 8.5,
});

// 报表表格数据
const reportTableData = ref([
  {
    period: "2024-01-01",
    siteName: "浦南一号泵站",
    area: "浦南",
    electricityConsumption: 2580.5,
    fuelConsumption: 120.3,
    waterConsumption: 450.2,
    totalCost: 8520.0,
    unitCost: 0.331,
    efficiency: 92,
    carbonEmission: 1250.8,
    performanceLevel: "A级",
    notes: "运行稳定，能效优良",
  },
  {
    period: "2024-01-01",
    siteName: "史北二号泵站",
    area: "史北",
    electricityConsumption: 3260.8,
    fuelConsumption: 180.5,
    waterConsumption: 620.8,
    totalCost: 12600.0,
    unitCost: 0.386,
    efficiency: 76,
    carbonEmission: 1680.3,
    performanceLevel: "B级",
    notes: "需要优化运行参数",
  },
]);

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 150,
});

// 分析发现
const analysisFindings = ref([
  "本月总能耗较上月下降3.2%，节能效果显著",
  "浦南区域能耗控制良好，效率提升4.8%",
  "史北区域部分站点能耗偏高，需要重点关注",
  "设备运行时间集中在高峰期，负荷分布不均",
]);

// 优化建议
const recommendations = ref([
  "优化史北区域站点运行策略，降低单位能耗",
  "加强设备维护保养，提高整体运行效率",
  "推广智能调度系统，实现削峰填谷",
  "建立能耗监控预警机制，及时发现异常",
]);

// 获取能效颜色
const getEfficiencyColor = (efficiency) => {
  if (efficiency >= 85) return "#67c23a";
  if (efficiency >= 70) return "#e6a23c";
  return "#f56c6c";
};

// 获取性能等级类型
const getPerformanceLevelType = (level) => {
  const typeMap = {
    A级: "success",
    B级: "warning",
    C级: "danger",
  };
  return typeMap[level] || "info";
};

// 查询处理
const handleQuery = () => {
  ElMessage.success("查询成功");
};

// 重置表单
const resetForm = () => {
  reportForm.reportType = "monthly";
  reportForm.dimension = ["area", "site"];
  reportForm.format = "excel";
  dateRange.value = [];
  ElMessage.info("已重置查询条件");
};

// 生成报表
const generateReport = () => {
  ElMessage.success("报表生成中，请稍候...");
};

// 定时报表
const scheduleReport = () => {
  ElMessage.info("定时报表设置功能开发中");
};

// 导出功能
const exportExcel = () => {
  ElMessage.success("Excel报表导出成功");
};

const exportPDF = () => {
  ElMessage.success("PDF报表导出成功");
};

const printReport = () => {
  ElMessage.success("正在打印报表...");
};

// 分页处理
const handleSizeChange = (size) => {
  pagination.pageSize = size;
};

const handleCurrentChange = (page) => {
  pagination.currentPage = page;
};

// 组件挂载
onMounted(() => {
  initCharts();
});

// 初始化图表
const initCharts = () => {
  console.log("初始化能耗分析报表图表");
};
</script>

<style scoped>
.energy-analysis-report {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.report-form {
  margin-bottom: 0;
}

.summary-card {
  margin-bottom: 20px;
}

.report-period {
  font-size: 14px;
  color: #666;
  background: #f5f5f5;
  padding: 4px 12px;
  border-radius: 4px;
}

.summary-item {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  background: #f8f9fa;
  transition: all 0.3s;
}

.summary-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.summary-icon {
  font-size: 36px;
  margin-right: 20px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.summary-icon.electricity {
  background: #fff2e8;
  color: #f39c12;
}

.summary-icon.cost {
  background: #f4e6ff;
  color: #9b59b6;
}

.summary-icon.efficiency {
  background: #e8f8f5;
  color: #2ecc71;
}

.summary-icon.carbon {
  background: #e8f6f3;
  color: #27ae60;
}

.summary-content {
  flex: 1;
}

.summary-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.summary-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.summary-change {
  font-size: 12px;
}

.increase {
  color: #f56c6c;
}

.decrease {
  color: #67c23a;
}

.analysis-charts {
  margin-bottom: 20px;
}

.chart-card {
  min-height: 450px;
}

.table-card {
  margin-bottom: 20px;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.conclusion-card {
  margin-bottom: 20px;
}

.conclusion-section h4 {
  color: #303133;
  margin-bottom: 15px;
  font-size: 16px;
}

.conclusion-list,
.recommendation-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.conclusion-list li,
.recommendation-list li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  font-size: 14px;
  line-height: 1.5;
}

.conclusion-list li .el-icon,
.recommendation-list li .el-icon {
  margin-right: 8px;
  margin-top: 2px;
  color: #409eff;
}

.recommendation-list li .el-icon {
  color: #e6a23c;
}
</style>
