<template>
  <div class="work-order-center">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <el-button type="primary" @click="handleCreate">创建工单</el-button>
        </div>
      </template>

      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">总工单数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value pending">{{ stats.pending }}</div>
              <div class="stat-label">待处理</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value processing">{{ stats.processing }}</div>
              <div class="stat-label">处理中</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value completed">{{ stats.completed }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="工单类型">
          <el-select
            v-model="searchForm.type"
            placeholder="请选择类型"
            clearable
          >
            <el-option label="设备维修" value="equipment" />
            <el-option label="管道维护" value="pipeline" />
            <el-option label="水质检测" value="quality" />
          </el-select>
        </el-form-item>
        <el-form-item label="紧急程度">
          <el-select
            v-model="searchForm.priority"
            placeholder="请选择紧急程度"
            clearable
          >
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>

      <!-- 工单列表 -->
      <el-table :data="tableData" stripe>
        <el-table-column prop="orderNo" label="工单编号" width="120" />
        <el-table-column prop="title" label="工单标题" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            {{ getTypeText(row.type) }}
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="紧急程度" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityType(row.priority)">
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="assignee" label="处理人" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="150" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)"
              >查看</el-button
            >
            <el-button type="success" size="small" @click="handleEdit(row)"
              >编辑</el-button
            >
            <el-button type="danger" size="small" @click="handleDelete(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";

// 统计数据
const stats = reactive({
  total: 156,
  pending: 23,
  processing: 45,
  completed: 88,
});

// 搜索表单
const searchForm = reactive({
  type: "",
  priority: "",
  dateRange: [],
});

// 表格数据
const tableData = ref([
  {
    id: 1,
    orderNo: "WO202401001",
    title: "1号泵站设备维修",
    type: "equipment",
    priority: "high",
    assignee: "张三",
    status: "pending",
    createTime: "2024-01-01 10:00:00",
  },
  {
    id: 2,
    orderNo: "WO202401002",
    title: "主管道漏水处理",
    type: "pipeline",
    priority: "urgent",
    assignee: "李四",
    status: "processing",
    createTime: "2024-01-01 11:00:00",
  },
]);

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0,
});

// 获取类型文本
const getTypeText = (type: string) => {
  const texts: Record<string, string> = {
    equipment: "设备维修",
    pipeline: "管道维护",
    quality: "水质检测",
  };
  return texts[type] || "其他";
};

// 获取优先级类型
const getPriorityType = (
  priority: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const types: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    low: "info",
    medium: "warning",
    high: "danger",
    urgent: "danger",
  };
  return types[priority] || "info";
};

// 获取优先级文本
const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    low: "低",
    medium: "中",
    high: "高",
    urgent: "紧急",
  };
  return texts[priority] || "未知";
};

// 获取状态类型
const getStatusType = (
  status: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const types: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    pending: "warning",
    processing: "primary",
    completed: "success",
  };
  return types[status] || "info";
};

// 获取状态文本
const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: "待处理",
    processing: "处理中",
    completed: "已完成",
  };
  return texts[status] || "未知";
};

// 创建工单
const handleCreate = () => {
  console.log("创建工单");
};

// 搜索
const handleSearch = () => {
  console.log("搜索", searchForm);
};

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    type: "",
    priority: "",
    dateRange: [],
  });
};

// 导出
const handleExport = () => {
  console.log("导出数据");
};

// 查看
const handleView = (row: any) => {
  console.log("查看工单", row);
};

// 编辑
const handleEdit = (row: any) => {
  console.log("编辑工单", row);
};

// 删除
const handleDelete = (row: any) => {
  console.log("删除工单", row);
};

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size;
};

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page;
};

onMounted(() => {
  pagination.total = tableData.value.length;
});
</script>

<style scoped>
.work-order-center {
  padding: 20px;
}

.page-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-item {
  padding: 10px;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
}

.stat-value.pending {
  color: #e6a23c;
}

.stat-value.processing {
  color: #409eff;
}

.stat-value.completed {
  color: #67c23a;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
