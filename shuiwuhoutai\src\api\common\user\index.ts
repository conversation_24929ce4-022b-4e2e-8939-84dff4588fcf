import request from "@/utils/request";
import { UserForm, UserInfo, UserPageVO, UserQuery } from "./model";

class UserAPI {
  /**
   * 登录成功后获取用户信息（昵称、头像、权限集合和角色集合）
   */
  static getInfo() {
    return request<any, UserInfo>({
      url: "/profile",
      method: "get",
    });
  }

  /**
   * 获取用户分页列表
   *
   * @param queryParams
   */
  static getPage(queryParams: UserQuery) {
    return request<any, PageResult<UserPageVO[]>>({
      url: "/user/page",
      method: "get",
      params: queryParams,
    });
  }

  /**
   * 获取用户表单详情
   *
   * @param userId
   */
  static getFormData(userId: number) {
    return request<any, UserForm>({
      url: "/user/" + userId + "/form",
      method: "get",
    });
  }

  /**
   * 修改密码
   *
   * @param oldPassword 旧密码
   * @param newPassword 新密码
   */
  static changePassword(oldPassword: string, newPassword: string) {
    return request({
      url: "/user/change-password",
      method: "post",
      data: {
        oldPassword,
        newPassword,
      },
    });
  }

  /**
   * 添加用户
   *
   * @param data
   */
  static add(data: UserForm) {
    return request({
      url: "/user",
      method: "post",
      data: data,
    });
  }

  /**
   * 修改用户
   *
   * @param id
   * @param data
   */
  static update(id: number, data: UserForm) {
    return request({
      url: "/user/" + id,
      method: "put",
      data: data,
    });
  }

  /**
   * 删除用户
   *
   * @param ids
   */
  static deleteByIds(ids: string) {
    return request({
      url: "/user/" + ids,
      method: "delete",
    });
  }
}

export default UserAPI;
