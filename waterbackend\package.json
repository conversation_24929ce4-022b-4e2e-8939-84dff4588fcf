{"name": "projectmanage_backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^7.6.18", "@nestjs/core": "^7.6.15", "@nestjs/jwt": "^7.2.0", "@nestjs/mongoose": "^7.2.4", "@nestjs/passport": "^7.1.6", "@nestjs/platform-express": "^7.6.15", "@nestjs/platform-socket.io": "^7.6.18", "@nestjs/serve-static": "^2.2.2", "@nestjs/swagger": "^4.8.0", "@nestjs/typeorm": "^7.1.5", "@nestjs/websockets": "^7.6.18", "@typegoose/typegoose": "^7.6.1", "@types/mockjs": "^1.0.3", "@types/socket.io": "^3.0.2", "bcryptjs": "^2.4.3", "beautify-qrcode": "^1.0.3", "cache-manager": "^3.4.4", "form-data": "^3.0.1", "js-base64": "^3.7.2", "md5": "^2.3.0", "mockjs": "^1.1.0", "moment": "^2.29.1", "moment-timezone": "^0.5.38", "mongoose": "^5.12.12", "mysql2": "^3.14.3", "nestjs-mongoose-crud": "^2.1.2", "nestjs-typegoose": "^7.1.38", "node-ssh": "^12.0.4", "passport": "^0.4.1", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "qrcode": "^1.5.0", "reflect-metadata": "^0.1.13", "request-ip": "^2.1.3", "rimraf": "^3.0.2", "rxjs": "^6.6.6", "sqlite3": "^5.0.2", "string_decoder": "^1.1.1", "swagger-ui-express": "^4.1.6", "ts-md5": "^1.2.11", "typeorm": "^0.2.34", "ws": "^7.4.6", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^7.6.0", "@nestjs/schematics": "^7.3.0", "@nestjs/testing": "^7.6.15", "@types/bcrypt": "^5.0.0", "@types/express": "^4.17.11", "@types/jest": "^26.0.22", "@types/mongoose": "^5.11.67", "@types/multer": "^1.4.7", "@types/node": "^14.14.36", "@types/passport-jwt": "^3.0.6", "@types/passport-local": "^1.0.34", "@types/request-ip": "^0.0.37", "@types/supertest": "^2.0.10", "@typescript-eslint/eslint-plugin": "^4.19.0", "@typescript-eslint/parser": "^4.19.0", "eslint": "^7.22.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.3.1", "jest": "^26.6.3", "prettier": "^2.2.1", "supertest": "^6.1.3", "ts-jest": "^26.5.4", "ts-loader": "^8.0.18", "ts-node": "^9.1.1", "tsconfig-paths": "^3.9.0", "typescript": "^4.2.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}