# /f:/水利站/backend/realtime_data_logger.py
"""
实时数据日志记录模块
用于记录所有接收到的设备数据，不进行降采样
与定时导出的日志系统配合使用，确保所有数据都被保存
"""

import os
import json
import datetime
import logging
from pathlib import Path
from typing import Dict, Any, Optional
import threading

class RealtimeDataLogger:
    """实时数据日志记录器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, logs_dir: str = "./logs"):
        """
        初始化实时数据日志记录器
        :param logs_dir: 日志文件存储目录
        """
        if hasattr(self, '_initialized'):
            return
            
        # 创建realtime_data子目录
        self.logs_dir = Path(logs_dir) / "realtime_data"
        self.logs_dir.mkdir(parents=True, exist_ok=True)
        
        # 当前日期和对应的日志记录器
        self._current_date = None
        self._current_logger = None
        self._logger_lock = threading.Lock()
        
        # 初始化当天的日志记录器
        self._setup_daily_logger()
        
        self._initialized = True
    
    def _setup_daily_logger(self):
        """设置当天的日志记录器"""
        today = datetime.date.today()
        
        # 如果日期发生变化，需要重新创建日志记录器
        if self._current_date != today or self._current_logger is None:
            # 生成日志文件名：realtime_data_YYYYMMDD.log
            log_filename = self.logs_dir / f"realtime_data_{today.strftime('%Y%m%d')}.log"
            
            # 创建新的日志记录器
            logger_name = f'daily_data_{today.strftime("%Y%m%d")}'
            logger = logging.getLogger(logger_name)
            logger.setLevel(logging.INFO)
            
            # 清除旧的handler
            for handler in logger.handlers[:]:
                logger.removeHandler(handler)
            
            # 创建文件handler
            file_handler = logging.FileHandler(log_filename, encoding='utf-8')
            file_handler.setLevel(logging.INFO)
            
            # 创建formatter - 简化格式，只记录时间戳和数据
            formatter = logging.Formatter('%(asctime)s - %(message)s')
            file_handler.setFormatter(formatter)
            
            # 添加handler到logger
            logger.addHandler(file_handler)
            
            self._current_date = today
            self._current_logger = logger
            
            print(f"[日志系统] 初始化日志文件: {log_filename}")
    
    def _get_current_logger(self) -> logging.Logger:
        """
        获取当前日期的日志记录器，如果日期发生变化会自动切换
        :return: 当前日志记录器
        """
        with self._logger_lock:
            # 检查日期是否发生变化
            today = datetime.date.today()
            if self._current_date != today:
                self._setup_daily_logger()
            
            return self._current_logger
    
    def log_device_data(self, device_sn: str, raw_data: Dict[str, Any], 
                       client_address: Optional[str] = None):
        """
        记录设备数据到当天的日志文件
        :param device_sn: 设备序列号
        :param raw_data: 原始设备数据
        :param client_address: 客户端地址（可选）
        """
        try:
            # 准备日志数据
            log_entry = {
                'timestamp': datetime.datetime.now().isoformat(),
                'device_sn': device_sn,
                'client_address': client_address,
                'raw_data': raw_data
            }
            
            # 转换为JSON字符串
            log_message = json.dumps(log_entry, ensure_ascii=False, separators=(',', ':'))
            
            # 获取当前日志记录器并记录
            logger = self._get_current_logger()
            logger.info(log_message)
            
        except Exception as e:
            # 如果日志记录失败，至少在控制台输出错误
            print(f"[日志系统] 记录设备 {device_sn} 数据失败: {e}")
    
    def log_raw_json(self, json_string: str, client_address: Optional[str] = None):
        """
        记录原始JSON字符串到当天的日志文件（用于无法解析设备SN的情况）
        :param json_string: 原始JSON字符串
        :param client_address: 客户端地址（可选）
        """
        try:
            log_entry = {
                'timestamp': datetime.datetime.now().isoformat(),
                'client_address': client_address,
                'raw_json': json_string,
                'note': 'Unable to extract device SN'
            }
            
            log_message = json.dumps(log_entry, ensure_ascii=False, separators=(',', ':'))
            
            # 获取当前日志记录器并记录
            logger = self._get_current_logger()
            logger.info(log_message)
            
        except Exception as e:
            print(f"[日志系统] 记录原始JSON失败: {e}")
    
    def cleanup_old_logs(self, keep_days: int = 7):
        """
        清理旧的日志文件
        :param keep_days: 保留天数
        """
        try:
            cutoff_date = datetime.date.today() - datetime.timedelta(days=keep_days)
            cutoff_date_str = cutoff_date.strftime('%Y%m%d')
            
            deleted_count = 0
            for log_file in self.logs_dir.glob("realtime_data_*.log"):
                try:
                    # 从文件名提取日期（格式：realtime_data_YYYYMMDD.log）
                    filename = log_file.stem
                    if filename.startswith('realtime_data_') and len(filename) == 21:
                        date_part = filename[13:]  # 提取YYYYMMDD部分
                        if len(date_part) == 8 and date_part.isdigit():
                            if date_part < cutoff_date_str:
                                log_file.unlink()
                                deleted_count += 1
                                print(f"[日志系统] 删除旧日志文件: {log_file.name}")
                except (ValueError, OSError) as e:
                    print(f"[日志系统] 处理文件 {log_file.name} 时出错: {e}")
            
            print(f"[日志系统] 清理完成，删除了 {deleted_count} 个文件")
            
        except Exception as e:
            print(f"[日志系统] 清理旧日志文件时出错: {e}")
    
    def get_log_stats(self) -> Dict[str, Any]:
        """
        获取日志统计信息
        :return: 统计信息字典
        """
        try:
            stats = {
                'total_log_files': 0,
                'today_log_file': None,
                'total_size_bytes': 0,
                'recent_dates': []
            }
            
            today = datetime.date.today().strftime('%Y%m%d')
            
            for log_file in self.logs_dir.glob("realtime_data_*.log"):
                stats['total_log_files'] += 1
                stats['total_size_bytes'] += log_file.stat().st_size
                
                # 检查文件名格式（realtime_data_YYYYMMDD.log）
                filename = log_file.stem
                if filename.startswith('realtime_data_') and len(filename) == 21:
                    date_part = filename[13:]  # 提取YYYYMMDD部分
                    if len(date_part) == 8 and date_part.isdigit():
                        stats['recent_dates'].append(date_part)
                        
                        if date_part == today:
                            stats['today_log_file'] = log_file.name
            
            # 排序日期列表，最新的在前
            stats['recent_dates'] = sorted(stats['recent_dates'], reverse=True)[:10]
            stats['total_size_mb'] = round(stats['total_size_bytes'] / (1024 * 1024), 2)
            
            return stats
            
        except Exception as e:
            print(f"[日志系统] 获取统计信息时出错: {e}")
            return {'error': str(e)}


# 全局实例
_realtime_logger = None
_logger_lock = threading.Lock()

def get_realtime_logger() -> RealtimeDataLogger:
    """
    获取全局实时日志记录器实例
    :return: 实时日志记录器实例
    """
    global _realtime_logger
    if _realtime_logger is None:
        with _logger_lock:
            if _realtime_logger is None:
                _realtime_logger = RealtimeDataLogger()
    return _realtime_logger

def log_device_data(device_sn: str, raw_data: Dict[str, Any], 
                   client_address: Optional[str] = None):
    """
    便捷函数：记录设备数据
    """
    logger = get_realtime_logger()
    logger.log_device_data(device_sn, raw_data, client_address)

def log_raw_json(json_string: str, client_address: Optional[str] = None):
    """
    便捷函数：记录原始JSON
    """
    logger = get_realtime_logger()
    logger.log_raw_json(json_string, client_address)

def cleanup_old_realtime_logs(keep_days: int = 7):
    """
    便捷函数：清理旧的日志
    """
    logger = get_realtime_logger()
    logger.cleanup_old_logs(keep_days)

def get_realtime_log_stats() -> Dict[str, Any]:
    """
    便捷函数：获取日志统计信息
    """
    logger = get_realtime_logger()
    return logger.get_log_stats()


if __name__ == "__main__":
    # 测试实时日志功能
    print("测试实时数据日志记录功能...")
    
    # 模拟设备数据
    test_device_sn = "TEST001"
    test_data = {
        'water_pump1': 1,
        'water_pump2': 0,
        'temperature': 25.5,
        'humidity': 60.2,
        'timestamp': datetime.datetime.now().isoformat()
    }
    
    # 记录测试数据
    log_device_data(test_device_sn, test_data, "*************:8889")
    
    # 获取统计信息
    stats = get_realtime_log_stats()
    print(f"日志统计: {json.dumps(stats, ensure_ascii=False, indent=2)}")
    
    print("测试完成！")