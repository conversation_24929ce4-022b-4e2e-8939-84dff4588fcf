-- 在正确的数据库中创建降采样表
USE water_station_config;

-- 创建分钟级数据表
CREATE TABLE IF NOT EXISTS device_data_minute (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    device_sn VARCHAR(50) NOT NULL COMMENT '设备序列号',
    timestamp DATETIME(3) NOT NULL COMMENT '分钟时间戳',
    avg_data JSON NOT NULL COMMENT '平均值数据',
    max_data JSON NOT NULL COMMENT '最大值数据',
    min_data JSON NOT NULL COMMENT '最小值数据',
    sample_count INT NOT NULL COMMENT '原始样本数量',
    created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
    
    INDEX idx_device_time_minute (device_sn, timestamp),
    INDEX idx_timestamp_minute (timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分钟级设备数据表';

-- 创建小时级数据表
CREATE TABLE IF NOT EXISTS device_data_hour (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    device_sn VARCHAR(50) NOT NULL COMMENT '设备序列号',
    timestamp DATETIME(3) NOT NULL COMMENT '小时时间戳',
    avg_data JSON NOT NULL COMMENT '平均值数据',
    max_data JSON NOT NULL COMMENT '最大值数据',
    min_data JSON NOT NULL COMMENT '最小值数据',
    minute_sample_count INT NOT NULL COMMENT '分钟级样本数量',
    original_sample_count INT NOT NULL COMMENT '原始5秒级样本总数',
    created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
    
    INDEX idx_device_time_hour (device_sn, timestamp),
    INDEX idx_timestamp_hour (timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小时级设备数据表';

-- 验证表创建
SHOW TABLES;
SELECT 'Tables created successfully' AS status;