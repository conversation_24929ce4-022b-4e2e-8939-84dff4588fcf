# /f:/水利站/backend/database.py
"""
数据库连接管理文件 - 混合架构版本
现在管理三个数据库的连接：
- SQLite: 保留用于兼容性 (已废弃，但保留接口)
- MongoDB: 用于实时数据和日志存储
- MySQL: 用于配置和状态数据
"""

# 保留原有SQLite相关代码以维护兼容性
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

# 导入新的数据库连接模块
from mongodb_connection import create_mongodb_indexes
from mysql_connection import create_mysql_db_and_tables, test_mysql_connection

# SQLite配置 (已完全废弃)
# SQLALCHEMY_DATABASE_URL = "sqlite:///./sql_app.db"

# 创建SQLite引擎 (已完全废弃)
# engine = create_engine(
#     SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False}
# )

# 创建SQLite会话类 (已完全废弃)  
# SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 为兼容性导入MySQL SessionLocal
from mysql_connection import MySQLSessionLocal as SessionLocal, MYSQL_DATABASE_URL as SQLALCHEMY_DATABASE_URL

# 创建Base类 (已废弃，保留兼容性)
Base = declarative_base()


def create_db_and_tables():
    """
    初始化所有数据库连接和表结构
    现在包括：MongoDB索引创建 和 MySQL表创建
    SQLite部分已废弃但保留以维护兼容性
    """
    print("[database] 开始初始化混合数据库架构...")
    
    # 1. 测试并初始化MySQL
    print("[database] 正在初始化MySQL...")
    if test_mysql_connection():
        create_mysql_db_and_tables()
        print("[database] MySQL初始化完成")
    else:
        print("[database] MySQL初始化失败，请检查连接配置")
        return False
    
    # 2. 初始化MongoDB索引
    print("[database] 正在初始化MongoDB...")
    try:
        create_mongodb_indexes()
        print("[database] MongoDB初始化完成")
    except Exception as e:
        print(f"[database] MongoDB初始化失败: {e}")
        return False
    
    # 3. SQLite部分（已废弃，但保留以防止现有代码报错）
    
    print("[database] 混合数据库架构初始化完成！")
    print("[database] - MongoDB: 设备数据日志、操作日志")
    print("[database] - MySQL: 任务元数据、系统键值对存储")
    
    return True


# 为了兼容性保留的函数，但实际上已经不再创建SQLite表
def create_sqlite_tables_legacy():
    """
    保留的SQLite表创建函数（已废弃）
    现在只用于兼容性，不会实际创建表
    """
    print("[database] SQLite表创建已废弃，数据已迁移至MongoDB+MySQL架构")
    pass