<template>
  <div class="alarm-list">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-button type="primary" @click="refreshData">刷新</el-button>
        </div>
      </template>

      <!-- 搜索条件 -->
      <el-form :model="searchForm" label-width="80px" inline>
        <el-form-item label="告警级别">
          <el-select v-model="searchForm.level" placeholder="请选择告警级别">
            <el-option label="全部" value="" />
            <el-option label="紧急" value="urgent" />
            <el-option label="重要" value="important" />
            <el-option label="一般" value="normal" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 告警列表表格 -->
      <el-table :data="alarmList" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="告警ID" width="100" />
        <el-table-column prop="title" label="告警标题" />
        <el-table-column prop="level" label="告警级别" width="100">
          <template #default="scope">
            <el-tag :type="getLevelType(scope.row.level)">
              {{ getLevelText(scope.row.level) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="source" label="告警来源" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag
              :type="scope.row.status === 'resolved' ? 'success' : 'danger'"
            >
              {{ scope.row.status === "resolved" ? "已解决" : "未解决" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="告警时间" width="180" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleView(scope.row)"
              >查看</el-button
            >
            <el-button
              type="success"
              size="small"
              @click="handleResolve(scope.row)"
              v-if="scope.row.status !== 'resolved'"
              >处理</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        style="margin-top: 20px"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";

// 搜索表单
const searchForm = reactive({
  level: "",
  dateRange: [],
});

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0,
});

// 告警列表数据
const alarmList = ref([
  {
    id: "AL001",
    title: "泵站001压力异常",
    level: "urgent",
    source: "泵站监控",
    status: "active",
    createTime: "2024-01-15 10:30:00",
  },
  {
    id: "AL002",
    title: "管网002流量超限",
    level: "important",
    source: "流量监测",
    status: "resolved",
    createTime: "2024-01-15 09:15:00",
  },
]);

const loading = ref(false);

// 获取告警级别类型
const getLevelType = (
  level: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const typeMap: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    urgent: "danger",
    important: "warning",
    normal: "info",
  };
  return typeMap[level] || "info";
};

// 获取告警级别文本
const getLevelText = (level: string) => {
  const textMap: Record<string, string> = {
    urgent: "紧急",
    important: "重要",
    normal: "一般",
  };
  return textMap[level] || "一般";
};

// 查询
const handleSearch = () => {
  console.log("搜索条件:", searchForm);
  loadData();
};

// 重置搜索
const resetSearch = () => {
  searchForm.level = "";
  searchForm.dateRange = [];
  loadData();
};

// 刷新数据
const refreshData = () => {
  loadData();
};

// 查看详情
const handleView = (row: any) => {
  console.log("查看告警详情:", row);
};

// 处理告警
const handleResolve = (row: any) => {
  console.log("处理告警:", row);
};

// 分页大小改变
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  loadData();
};

// 当前页改变
const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  loadData();
};

// 加载数据
const loadData = () => {
  loading.value = true;
  // 模拟API调用
  setTimeout(() => {
    pagination.total = 50;
    loading.value = false;
  }, 1000);
};

onMounted(() => {
  loadData();
});
</script>

<style scoped>
.alarm-list {
  padding: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
