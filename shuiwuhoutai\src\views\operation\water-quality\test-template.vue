<template>
  <div class="water-quality-test-template">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <el-button type="primary" @click="handleCreateTemplate"
            >创建模版</el-button
          >
        </div>
      </template>

      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="模版名称">
          <el-input
            v-model="searchForm.templateName"
            placeholder="请输入模版名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="检测类型">
          <el-select
            v-model="searchForm.testType"
            placeholder="请选择类型"
            clearable
          >
            <el-option label="常规检测" value="routine" />
            <el-option label="全面检测" value="comprehensive" />
            <el-option label="专项检测" value="special" />
          </el-select>
        </el-form-item>
        <el-form-item label="模版状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="templateData" stripe>
        <el-table-column prop="templateName" label="模版名称" />
        <el-table-column prop="testType" label="检测类型" width="120">
          <template #default="{ row }">
            {{ getTestTypeText(row.testType) }}
          </template>
        </el-table-column>
        <el-table-column prop="itemCount" label="检测项目数" width="120" />
        <el-table-column prop="applicableFor" label="适用对象" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === "active" ? "启用" : "禁用" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="150" />
        <el-table-column prop="creator" label="创建人" width="100" />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)"
              >查看</el-button
            >
            <el-button type="success" size="small" @click="handleEdit(row)"
              >编辑</el-button
            >
            <el-button type="warning" size="small" @click="handleCopy(row)"
              >复制</el-button
            >
            <el-button type="danger" size="small" @click="handleDelete(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";

const searchForm = reactive({
  templateName: "",
  testType: "",
  status: "",
});

const templateData = ref([
  {
    id: 1,
    templateName: "出厂水常规检测模版",
    testType: "routine",
    itemCount: 12,
    applicableFor: "出厂水",
    status: "active",
    createTime: "2024-01-01",
    creator: "管理员",
  },
  {
    id: 2,
    templateName: "管网水全面检测模版",
    testType: "comprehensive",
    itemCount: 42,
    applicableFor: "管网水",
    status: "active",
    createTime: "2024-01-02",
    creator: "张三",
  },
]);

const getTestTypeText = (type: string) => {
  const texts: Record<string, string> = {
    routine: "常规检测",
    comprehensive: "全面检测",
    special: "专项检测",
  };
  return texts[type] || "其他";
};

const handleCreateTemplate = () => console.log("创建模版");
const handleSearch = () => console.log("搜索", searchForm);
const handleReset = () =>
  Object.assign(searchForm, { templateName: "", testType: "", status: "" });
const handleView = (row: any) => console.log("查看", row);
const handleEdit = (row: any) => console.log("编辑", row);
const handleCopy = (row: any) => console.log("复制", row);
const handleDelete = (row: any) => console.log("删除", row);

onMounted(() => {
  // 初始化
});
</script>

<style scoped>
.water-quality-test-template {
  padding: 20px;
}

.page-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}
</style>
