<template>
  <div class="pipeline-info-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="header-actions">
            <el-button type="primary" @click="handleImport">
              <el-icon><Upload /></el-icon>
              批量导入
            </el-button>
            <el-button @click="handleExport">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
            <el-button @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form">
        <el-form :model="searchForm" inline>
          <el-form-item label="管线名称">
            <el-input
              v-model="searchForm.pipelineName"
              placeholder="请输入管线名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="管线编码">
            <el-input
              v-model="searchForm.pipelineCode"
              placeholder="请输入管线编码"
              clearable
              style="width: 150px"
            />
          </el-form-item>
          <el-form-item label="管线类型">
            <el-select
              v-model="searchForm.pipelineType"
              placeholder="请选择管线类型"
              clearable
              style="width: 150px"
            >
              <el-option label="供水管线" value="water_supply" />
              <el-option label="排水管线" value="drainage" />
              <el-option label="污水管线" value="sewage" />
              <el-option label="雨水管线" value="rainwater" />
              <el-option label="其他管线" value="other" />
            </el-select>
          </el-form-item>
          <el-form-item label="材质">
            <el-select
              v-model="searchForm.material"
              placeholder="请选择材质"
              clearable
              style="width: 120px"
            >
              <el-option label="铸铁" value="cast_iron" />
              <el-option label="钢管" value="steel" />
              <el-option label="PVC" value="pvc" />
              <el-option label="PE" value="pe" />
              <el-option label="混凝土" value="concrete" />
            </el-select>
          </el-form-item>
          <el-form-item label="运行状态">
            <el-select
              v-model="searchForm.runStatus"
              placeholder="请选择状态"
              clearable
              style="width: 120px"
            >
              <el-option label="正常" value="normal" />
              <el-option label="故障" value="fault" />
              <el-option label="维护" value="maintenance" />
              <el-option label="停用" value="stopped" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 管线信息统计 -->
      <div class="info-statistics">
        <el-row :gutter="20">
          <el-col :span="4">
            <div class="stat-card total">
              <div class="stat-icon">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.total }}</div>
                <div class="stat-label">管线总数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card length">
              <div class="stat-content">
                <div class="stat-number">{{ statistics.totalLength }}</div>
                <div class="stat-label">总长度(km)</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card supply">
              <div class="stat-icon">
                <el-icon><ArrowUp /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.supplyCount }}</div>
                <div class="stat-label">供水管线</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card drainage">
              <div class="stat-icon">
                <el-icon><ArrowDown /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.drainageCount }}</div>
                <div class="stat-label">排水管线</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card normal">
              <div class="stat-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.normalCount }}</div>
                <div class="stat-label">正常运行</div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card fault">
              <div class="stat-icon">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.faultCount }}</div>
                <div class="stat-label">故障管线</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <el-table
          :data="tableData"
          style="width: 100%"
          stripe
          border
          v-loading="tableLoading"
          :max-height="600"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column type="expand" width="60">
            <template #default="{ row }">
              <div class="expand-content">
                <el-descriptions :column="3" border>
                  <el-descriptions-item label="施工单位">{{
                    row.constructor
                  }}</el-descriptions-item>
                  <el-descriptions-item label="埋设深度"
                    >{{ row.depth || "-" }} 米</el-descriptions-item
                  >
                  <el-descriptions-item label="设计压力"
                    >{{ row.designPressure || "-" }} MPa</el-descriptions-item
                  >
                  <el-descriptions-item label="最后检测">{{
                    row.lastInspection || "-"
                  }}</el-descriptions-item>
                  <el-descriptions-item label="下次检测">{{
                    row.nextInspection || "-"
                  }}</el-descriptions-item>
                  <el-descriptions-item label="检测周期"
                    >{{ row.inspectionCycle || "-" }} 月</el-descriptions-item
                  >
                  <el-descriptions-item label="路径坐标" :span="3">
                    <span class="path-coordinates">{{
                      row.pathCoordinates || "暂无坐标数据"
                    }}</span>
                  </el-descriptions-item>
                  <el-descriptions-item label="管线描述" :span="3">
                    {{ row.description || "-" }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="pipelineCode"
            label="管线编码"
            width="120"
            sortable
            fixed="left"
          />
          <el-table-column
            prop="pipelineName"
            label="管线名称"
            width="150"
            sortable
          />
          <el-table-column prop="pipelineType" label="管线类型" width="120">
            <template #default="{ row }">
              <el-tag :type="getTypeTagType(row.pipelineType)" size="small">
                {{ getTypeLabel(row.pipelineType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="areaName" label="所属区域" width="120" />
          <el-table-column
            prop="startPoint"
            label="起点"
            width="130"
            show-overflow-tooltip
          />
          <el-table-column
            prop="endPoint"
            label="终点"
            width="130"
            show-overflow-tooltip
          />
          <el-table-column prop="length" label="长度(m)" width="100" sortable>
            <template #default="{ row }">
              <span class="number-cell">{{ row.length.toLocaleString() }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="diameter"
            label="管径(mm)"
            width="100"
            sortable
          />
          <el-table-column prop="material" label="材质" width="100">
            <template #default="{ row }">
              {{ getMaterialLabel(row.material) }}
            </template>
          </el-table-column>
          <el-table-column prop="runStatus" label="运行状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.runStatus)" size="small">
                {{ getStatusLabel(row.runStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="installDate"
            label="铺设日期"
            width="120"
            sortable
          />
          <el-table-column
            prop="lastMaintenance"
            label="上次维护"
            width="120"
            sortable
          />
          <el-table-column prop="manager" label="负责人" width="100" />
          <el-table-column prop="phone" label="联系电话" width="130" />
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <el-button
                size="small"
                type="primary"
                text
                @click="handleViewDetail(row)"
              >
                <el-icon><View /></el-icon>
                详情
              </el-button>
              <el-button
                size="small"
                type="success"
                text
                @click="handleViewPath(row)"
              >
                <el-icon><Location /></el-icon>
                路径
              </el-button>
              <el-button
                size="small"
                type="warning"
                text
                @click="handleMaintenance(row)"
              >
                <el-icon><Tools /></el-icon>
                维护
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="管线详细信息" width="900px">
      <div v-if="detailData" class="detail-content">
        <!-- 基本信息 -->
        <el-descriptions title="基本信息" :column="3" border>
          <el-descriptions-item label="管线编码">{{
            detailData.pipelineCode
          }}</el-descriptions-item>
          <el-descriptions-item label="管线名称">{{
            detailData.pipelineName
          }}</el-descriptions-item>
          <el-descriptions-item label="管线类型">{{
            getTypeLabel(detailData.pipelineType)
          }}</el-descriptions-item>
          <el-descriptions-item label="所属区域">{{
            detailData.areaName
          }}</el-descriptions-item>
          <el-descriptions-item label="起点">{{
            detailData.startPoint
          }}</el-descriptions-item>
          <el-descriptions-item label="终点">{{
            detailData.endPoint
          }}</el-descriptions-item>
          <el-descriptions-item label="长度"
            >{{ detailData.length }} 米</el-descriptions-item
          >
          <el-descriptions-item label="管径"
            >{{ detailData.diameter }} mm</el-descriptions-item
          >
          <el-descriptions-item label="材质">{{
            getMaterialLabel(detailData.material)
          }}</el-descriptions-item>
          <el-descriptions-item label="埋设深度"
            >{{ detailData.depth || "-" }} 米</el-descriptions-item
          >
          <el-descriptions-item label="设计压力"
            >{{ detailData.designPressure || "-" }} MPa</el-descriptions-item
          >
          <el-descriptions-item label="运行状态">
            <el-tag :type="getStatusTagType(detailData.runStatus)">
              {{ getStatusLabel(detailData.runStatus) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 工程信息 -->
        <el-descriptions
          title="工程信息"
          :column="3"
          border
          style="margin-top: 20px"
        >
          <el-descriptions-item label="铺设日期">{{
            detailData.installDate
          }}</el-descriptions-item>
          <el-descriptions-item label="施工单位">{{
            detailData.constructor
          }}</el-descriptions-item>
          <el-descriptions-item label="工程造价"
            >{{ detailData.projectCost || "-" }} 万元</el-descriptions-item
          >
          <el-descriptions-item label="设计单位">{{
            detailData.designer || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="监理单位">{{
            detailData.supervisor || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="竣工日期">{{
            detailData.completionDate || "-"
          }}</el-descriptions-item>
        </el-descriptions>

        <!-- 维护信息 -->
        <el-descriptions
          title="维护信息"
          :column="3"
          border
          style="margin-top: 20px"
        >
          <el-descriptions-item label="负责人">{{
            detailData.manager
          }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{
            detailData.phone
          }}</el-descriptions-item>
          <el-descriptions-item label="维护单位">{{
            detailData.maintenanceUnit || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="上次维护">{{
            detailData.lastMaintenance
          }}</el-descriptions-item>
          <el-descriptions-item label="下次维护">{{
            detailData.nextMaintenance || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="维护周期"
            >{{ detailData.maintenanceCycle || "-" }} 月</el-descriptions-item
          >
          <el-descriptions-item label="最后检测">{{
            detailData.lastInspection || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="下次检测">{{
            detailData.nextInspection || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="检测周期"
            >{{ detailData.inspectionCycle || "-" }} 月</el-descriptions-item
          >
        </el-descriptions>

        <!-- 其他信息 -->
        <el-descriptions
          title="其他信息"
          :column="2"
          border
          style="margin-top: 20px"
        >
          <el-descriptions-item label="创建时间">{{
            detailData.createTime
          }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{
            detailData.updateTime || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="管线描述" :span="2">
            {{ detailData.description || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="路径坐标" :span="2">
            <span class="path-coordinates">{{
              detailData.pathCoordinates || "暂无坐标数据"
            }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 路径查看对话框 -->
    <el-dialog v-model="pathDialogVisible" title="管线路径" width="800px">
      <div v-if="pathData" class="path-content">
        <div class="path-info">
          <h4>{{ pathData.pipelineName }} 路径详情</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">起点：</span>
                <span class="value">{{ pathData.startPoint }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">终点：</span>
                <span class="value">{{ pathData.endPoint }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">总长度：</span>
                <span class="value">{{ pathData.length }} 米</span>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="path-map">
          <div class="map-placeholder">
            <el-icon size="60"><Location /></el-icon>
            <p>管线路径三维地图展示</p>
            <div class="path-tools">
              <el-button size="small" type="primary">查看3D视图</el-button>
              <el-button size="small" type="success">导出路径</el-button>
              <el-button size="small" type="warning">编辑路径</el-button>
            </div>
          </div>
        </div>
        <div class="coordinates-info">
          <h4>路径坐标点</h4>
          <div class="coordinates-text">
            {{ pathData.pathCoordinates || "暂无坐标数据" }}
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 维护记录对话框 -->
    <el-dialog
      v-model="maintenanceDialogVisible"
      title="维护记录"
      width="700px"
    >
      <div v-if="maintenanceData" class="maintenance-content">
        <div class="maintenance-header">
          <h4>{{ maintenanceData.pipelineName }} 维护记录</h4>
          <el-button type="primary" size="small" @click="handleAddMaintenance">
            <el-icon><Plus /></el-icon>
            新增维护记录
          </el-button>
        </div>
        <el-table :data="maintenanceRecords" style="width: 100%">
          <el-table-column prop="date" label="维护日期" width="120" />
          <el-table-column prop="type" label="维护类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getMaintenanceTypeTag(row.type)" size="small">
                {{ getMaintenanceTypeLabel(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="维护内容" />
          <el-table-column prop="operator" label="操作人员" width="100" />
          <el-table-column prop="cost" label="费用(元)" width="100" />
          <el-table-column label="操作" width="80">
            <template #default="{ row }">
              <el-button
                size="small"
                type="primary"
                text
                @click="handleViewMaintenance(row)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";
import {
  Upload,
  Download,
  Refresh,
  Search,
  View,
  Location,
  Tools,
  Plus,
  Connection,
  ArrowUp,
  ArrowDown,
  CircleCheck,
  CircleClose,
} from "@element-plus/icons-vue";

// 搜索表单
const searchForm = reactive({
  pipelineName: "",
  pipelineCode: "",
  pipelineType: "",
  material: "",
  runStatus: "",
});

// 表格数据
const tableLoading = ref(false);
const tableData = ref([
  {
    id: "1",
    pipelineCode: "GX001",
    pipelineName: "主供水管线A",
    pipelineType: "water_supply",
    areaName: "南京市",
    startPoint: "水厂一号泵房",
    endPoint: "玄武区配水站",
    length: 8500,
    diameter: 800,
    depth: 1.5,
    material: "steel",
    runStatus: "normal",
    installDate: "2022-03-15",
    lastMaintenance: "2024-01-10",
    manager: "张三",
    phone: "13800138001",
    constructor: "南京水利工程公司",
    createTime: "2022-03-15 10:30:00",
    description: "城市主供水管线",
    pathCoordinates: "118.7800,32.0600;118.7850,32.0650;118.7900,32.0700",
    designPressure: 1.6,
    projectCost: 850,
    designer: "南京市政设计院",
    supervisor: "江苏监理公司",
    completionDate: "2022-05-15",
    maintenanceUnit: "南京管网维护公司",
    nextMaintenance: "2024-07-10",
    maintenanceCycle: 6,
    lastInspection: "2024-03-15",
    nextInspection: "2024-09-15",
    inspectionCycle: 6,
  },
  {
    id: "2",
    pipelineCode: "GX002",
    pipelineName: "污水处理管线B",
    pipelineType: "sewage",
    areaName: "苏州市",
    startPoint: "工业园区",
    endPoint: "污水处理厂",
    length: 12000,
    diameter: 1000,
    depth: 2.0,
    material: "concrete",
    runStatus: "normal",
    installDate: "2021-06-20",
    lastMaintenance: "2024-02-15",
    manager: "李四",
    phone: "13800138002",
    constructor: "苏州环保工程公司",
    createTime: "2021-06-20 14:20:00",
    description: "工业园区污水收集管线",
    pathCoordinates: "120.7000,31.3100;120.7100,31.3150;120.7200,31.3200",
    designPressure: 0.8,
    projectCost: 1200,
    designer: "苏州环保设计院",
    supervisor: "苏州工程监理",
    completionDate: "2021-08-20",
    maintenanceUnit: "苏州排水公司",
    nextMaintenance: "2024-08-15",
    maintenanceCycle: 6,
    lastInspection: "2024-05-20",
    nextInspection: "2024-11-20",
    inspectionCycle: 6,
  },
  {
    id: "3",
    pipelineCode: "GX003",
    pipelineName: "雨水排放管线C",
    pipelineType: "rainwater",
    areaName: "南京市",
    startPoint: "市区中心",
    endPoint: "长江入口",
    length: 6800,
    diameter: 600,
    depth: 1.2,
    material: "pvc",
    runStatus: "maintenance",
    installDate: "2023-04-10",
    lastMaintenance: "2024-08-20",
    manager: "王五",
    phone: "13800138003",
    constructor: "江苏排水工程公司",
    createTime: "2023-04-10 09:15:00",
    description: "城区雨水收集排放",
    pathCoordinates: "118.7700,32.0500;118.7750,32.0550;118.7800,32.0600",
    designPressure: 0.4,
    projectCost: 680,
    designer: "南京市政设计院",
    supervisor: "江苏监理公司",
    completionDate: "2023-06-10",
    maintenanceUnit: "南京排水公司",
    nextMaintenance: "2024-12-20",
    maintenanceCycle: 4,
    lastInspection: "2024-08-10",
    nextInspection: "2024-12-10",
    inspectionCycle: 4,
  },
]);

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 3,
});

// 统计数据
const statistics = computed(() => {
  const total = tableData.value.length;
  const totalLength = (
    tableData.value.reduce((sum, item) => sum + item.length, 0) / 1000
  ).toFixed(1);
  const supplyCount = tableData.value.filter(
    (item) => item.pipelineType === "water_supply"
  ).length;
  const drainageCount = tableData.value.filter((item) =>
    ["drainage", "sewage", "rainwater"].includes(item.pipelineType)
  ).length;
  const normalCount = tableData.value.filter(
    (item) => item.runStatus === "normal"
  ).length;
  const faultCount = tableData.value.filter(
    (item) => item.runStatus === "fault"
  ).length;

  return {
    total,
    totalLength,
    supplyCount,
    drainageCount,
    normalCount,
    faultCount,
  };
});

// 对话框
const detailDialogVisible = ref(false);
const detailData = ref<any>(null);
const pathDialogVisible = ref(false);
const pathData = ref<any>(null);
const maintenanceDialogVisible = ref(false);
const maintenanceData = ref<any>(null);

// 维护记录
const maintenanceRecords = ref([
  {
    date: "2024-08-20",
    type: "repair",
    description: "管道接头维修，更换密封圈",
    operator: "维护组A",
    cost: 1500,
  },
  {
    date: "2024-02-15",
    type: "inspection",
    description: "定期检测，管道状态良好",
    operator: "检测组B",
    cost: 800,
  },
  {
    date: "2023-11-10",
    type: "cleaning",
    description: "管道清洗，清理沉积物",
    operator: "清洗组C",
    cost: 2000,
  },
]);

// 方法
const getTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    water_supply: "供水管线",
    drainage: "排水管线",
    sewage: "污水管线",
    rainwater: "雨水管线",
    other: "其他管线",
  };
  return typeMap[type] || "未知";
};

const getTypeTagType = (
  type: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const typeTagMap: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    water_supply: "primary",
    drainage: "success",
    sewage: "warning",
    rainwater: "info",
    other: "danger",
  };
  return typeTagMap[type] || "info";
};

const getMaterialLabel = (material: string) => {
  const materialMap: Record<string, string> = {
    cast_iron: "铸铁",
    steel: "钢管",
    pvc: "PVC",
    pe: "PE",
    concrete: "混凝土",
    other: "其他",
  };
  return materialMap[material] || "未知";
};

const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    normal: "正常",
    fault: "故障",
    maintenance: "维护",
    stopped: "停用",
  };
  return statusMap[status] || "未知";
};

const getStatusTagType = (
  status: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const statusTagMap: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    normal: "success",
    fault: "danger",
    maintenance: "warning",
    stopped: "info",
  };
  return statusTagMap[status] || "info";
};

const getMaintenanceTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    repair: "维修",
    inspection: "检测",
    cleaning: "清洗",
    upgrade: "升级",
  };
  return typeMap[type] || "其他";
};

const getMaintenanceTypeTag = (
  type: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const tagMap: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    repair: "danger",
    inspection: "primary",
    cleaning: "success",
    upgrade: "warning",
  };
  return tagMap[type] || "info";
};

const handleSearch = () => {
  tableLoading.value = true;
  // TODO: 实现搜索逻辑
  setTimeout(() => {
    tableLoading.value = false;
    ElMessage.success("搜索完成");
  }, 1000);
};

const handleReset = () => {
  searchForm.pipelineName = "";
  searchForm.pipelineCode = "";
  searchForm.pipelineType = "";
  searchForm.material = "";
  searchForm.runStatus = "";
  ElMessage.success("搜索条件已重置");
};

const refreshData = () => {
  tableLoading.value = true;
  // TODO: 实现数据刷新
  setTimeout(() => {
    tableLoading.value = false;
    ElMessage.success("数据已刷新");
  }, 1000);
};

const handleImport = () => {
  ElMessage.info("批量导入功能待实现");
};

const handleExport = () => {
  ElMessage.info("导出功能待实现");
};

const handleViewDetail = (row: any) => {
  detailData.value = row;
  detailDialogVisible.value = true;
};

const handleViewPath = (row: any) => {
  pathData.value = row;
  pathDialogVisible.value = true;
};

const handleMaintenance = (row: any) => {
  maintenanceData.value = row;
  maintenanceDialogVisible.value = true;
};

const handleAddMaintenance = () => {
  ElMessage.info("新增维护记录功能待实现");
};

const handleViewMaintenance = (row: any) => {
  ElMessage.info("查看维护详情功能待实现");
};

const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  refreshData();
};

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page;
  refreshData();
};

onMounted(() => {
  refreshData();
});
</script>

<style scoped>
.pipeline-info-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.info-statistics {
  margin: 20px 0;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid;
  height: 80px;
}

.stat-card.total {
  border-left-color: #409eff;
}

.stat-card.length {
  border-left-color: #e6a23c;
}

.stat-card.supply {
  border-left-color: #67c23a;
}

.stat-card.drainage {
  border-left-color: #909399;
}

.stat-card.normal {
  border-left-color: #67c23a;
}

.stat-card.fault {
  border-left-color: #f56c6c;
}

.stat-icon {
  font-size: 32px;
  margin-right: 12px;
  color: #409eff;
}

.length .stat-icon {
  color: #e6a23c;
}

.supply .stat-icon {
  color: #67c23a;
}

.drainage .stat-icon {
  color: #909399;
}

.normal .stat-icon {
  color: #67c23a;
}

.fault .stat-icon {
  color: #f56c6c;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 13px;
  color: #909399;
  margin-top: 4px;
}

.table-container {
  margin-top: 20px;
}

.expand-content {
  padding: 20px;
  background-color: #f8f9fa;
}

.number-cell {
  font-family: monospace;
  font-weight: bold;
}

.path-coordinates {
  font-family: monospace;
  font-size: 12px;
  word-break: break-all;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.path-content {
  max-height: 500px;
  overflow-y: auto;
}

.path-info h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.info-item {
  margin-bottom: 8px;
}

.info-item .label {
  font-weight: bold;
  color: #606266;
}

.info-item .value {
  color: #303133;
}

.path-map {
  margin: 20px 0;
}

.map-placeholder {
  height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 2px dashed #ddd;
  border-radius: 6px;
  color: #999;
}

.map-placeholder .el-icon {
  color: #c0c4cc;
}

.path-tools {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.coordinates-info h4 {
  margin: 20px 0 10px 0;
  color: #303133;
}

.coordinates-text {
  padding: 10px;
  background-color: #f8f9fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  word-break: break-all;
  color: #606266;
}

.maintenance-content {
  max-height: 500px;
  overflow-y: auto;
}

.maintenance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.maintenance-header h4 {
  margin: 0;
  color: #303133;
}
</style>
