import  * as  fs  from "fs";
import  * as  path  from "path";
import  * as  child_process  from "child_process";
import  {NodeSSH} from 'node-ssh'

(async () => {
    // 打包
    // console.log("打包")
    // await build();
    // console.log("部署中")
   //部署
    // await deploy();
    // console.log("重启服务")
   //重启
    await restart();



})()

function  build() {
    return new Promise((resolve, reject) => {
        child_process.exec("npm run build", (error, stdout, stderr) => {
            if (error) {
                console.log(error);
                reject(error);
            }
            console.log(stdout);
            resolve();
        });
    }); 
}   

//部署  
function deploy() {
    return new Promise((resolve, reject) => {
        child_process.exec("scp -r dist/* ubuntu@*************:/home/<USER>/wuxiang/", (error, stdout, stderr) => {
            if (error) {
                console.log(error);
                reject(error);
            }
            console.log(stdout);
            resolve();
        }
        );
    });
}


//重启服务
function restart() {
    return new Promise((resolve, reject) => {
       let  ssh = new NodeSSH()
         ssh.connect({
            host: 'fastgpt.xinpanmen.com',
            username: 'ubuntu',
            privateKey: fs.readFileSync('C:/Users/<USER>/.ssh/id_rsa','utf-8')
        }).then(() => {
            ssh.execCommand('/home/<USER>/.nvm/versions/node/v20.12.0/bin/pm2 restart wuxiang').then((result) => {
                console.log(result);
                resolve();
            }).catch((error) => {
                console.log(error);
                reject(error);
            });
        }).catch((error) => {
            console.log(error);
            reject(error);
        });
    });
}