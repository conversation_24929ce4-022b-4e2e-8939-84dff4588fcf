<template>
  <div class="station-select-container">
    <!-- 导航栏 -->
    <van-nav-bar title="吴江污水处理站智能监测" fixed />

    <!-- 主要内容 -->
    <div class="content-wrapper">
      <!-- Banner区域 -->
      <div class="banner-section">
        <div class="banner-content">
          <div class="banner-title">智能监测平台</div>
          <div class="banner-subtitle">实时监控 · 智能预警 · 高效管理</div>
        </div>
        <div class="banner-decoration"></div>
      </div>

      <!-- 功能按钮区域 -->
      <div class="function-buttons">
        <div class="button-row">
          <div class="function-button" @click="goToAlarms">
            <div class="button-icon alarm-icon">⚠️</div>
            <div class="button-text">告警事件</div>
          </div>
          <div class="function-button" @click="goToInspection">
            <div class="button-icon inspection-icon">📋</div>
            <div class="button-text">巡查工单</div>
          </div>
          <div class="function-button" @click="goToMaintenance">
            <div class="button-icon maintenance-icon">🔧</div>
            <div class="button-text">故障维修</div>
          </div>
          <div class="function-button" @click="goToMessages">
            <div class="button-icon message-icon">💬</div>
            <div class="button-text">消息中心</div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="initialLoading" class="initial-loading">
        <van-loading type="spinner" color="#1989fa" vertical size="24px">
          正在加载站点信息...
        </van-loading>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="hasError" class="error-state">
        <van-empty image="error" :description="errorMessage">
          <van-button type="primary" @click="retryLoad">重试</van-button>
        </van-empty>
      </div>

      <!-- 正常内容 -->
      <template v-else>
        <!-- 筛选工具栏 -->
        <div class="filter-toolbar">
          <div class="filter-toolbar-left">
            <van-button type="primary" size="small" icon="filter-o" @click="showFilterDialog = true">
              筛选
              <van-tag v-if="activeFilterCount > 0" type="warning" class="filter-count-tag">
                {{ activeFilterCount }}
              </van-tag>
            </van-button>

            <!-- 快速筛选标签 -->
            <div class="quick-filters">
              <template v-for="(areaItem, index) in (Array.isArray(selectedArea) ? selectedArea : [])"
                :key="`area-${index}`">
                <van-tag v-if="areaItem" type="primary" closeable @close="removeAreaLevel(index)">
                  {{ areaItem }}
                </van-tag>
              </template>
              <van-tag v-if="selectedFacility && selectedFacility !== '全部'" type="primary" closeable
                @close="selectedFacility = ''">
                {{ selectedFacility }}
              </van-tag>
            </div>
          </div>

          <div class="filter-toolbar-right">
            <van-button v-if="activeFilterCount > 0" type="default" size="small" @click="resetFilter">
              清空
            </van-button>
            <van-button type="primary" size="small" icon="replay" @click="refreshStations" :loading="loading">
              刷新
            </van-button>
          </div>
        </div>

        <!-- 刷新下拉 -->
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <!-- 站点列表 -->
          <div class="station-list">
            <van-cell-group>
              <van-cell v-for="station in filteredStations" :key="station.id" :title="station.name"
                :label="`运行时间: ${station.runningTime}`" is-link @click="selectStation(station)">
                <template #icon>
                  <van-icon name="location-o" size="20" color="#1989fa" />
                </template>
                <template #right-icon>
                  <van-tag :type="getStatusType(station.status)">
                    {{ getStatusText(station.status) }}
                  </van-tag>
                </template>
              </van-cell>
            </van-cell-group>
          </div>
        </van-pull-refresh>

        <!-- 空状态 -->
        <van-empty v-if="filteredStations.length === 0 && !loading && !initialLoading" description="暂无符合条件的站点"
          image="search" />
      </template>

      <!-- 底部加载更多指示器 -->
      <div v-if="loading && !initialLoading" class="bottom-loading">
        <van-loading type="spinner" color="#1989fa" size="20px" />
        <span class="loading-text">更新中...</span>
      </div>
    </div>

    <!-- 筛选弹出框 -->
    <van-popup v-model:show="showFilterDialog" position="bottom" :style="{ height: '60%' }" round closeable
      close-icon-position="top-right">
      <div class="filter-dialog">
        <div class="filter-dialog-header">
          <h3>筛选站点</h3>
          <p>请选择筛选条件</p>
        </div>

        <div class="filter-dialog-content">
          <van-cell-group>
            <van-cell title="选择地区" :value="getAreaDisplayText()" is-link @click="showAreaCascader = true" />
            <van-cell title="设施类型" :value="selectedFacility || '全部'" is-link @click="showFacilityPicker = true" />
          </van-cell-group>
        </div>

        <div class="filter-dialog-actions">
          <van-button block plain @click="resetFilter">
            重置筛选
          </van-button>
          <van-button block type="primary" @click="applyFilter">
            确定筛选
          </van-button>
        </div>
      </div>
    </van-popup>

    <!-- 地区级联选择器 -->
    <van-popup v-model:show="showAreaCascader" position="bottom" round>
      <van-cascader :value="selectedArea" title="选择地区" :options="areaCascaderOptions" closeable
        @close="showAreaCascader = false" @finish="onAreaCascaderFinish" />
    </van-popup>

    <!-- 设施选择器 -->
    <van-popup v-model:show="showFacilityPicker" position="bottom">
      <van-picker :columns="[facilityOptions]" @confirm="onFacilityConfirm" @cancel="showFacilityPicker = false" />
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()

// 站点数据接口定义
interface Station {
  id: string
  name: string
  sn: string
  location: string
  status: 'online' | 'offline' | 'abnormal'  // 在线、离线、异常
  baseUrl: string
  runningTime: string  // 运行时间
  lastUpdated?: string // 最后更新时间
  township: string     // 镇区
  village: string      // 行政村
  naturalVillage: string // 自然村
  facility: string     // 设施
}

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const stations = ref<Station[]>([])
const initialLoading = ref(true) // 初始加载状态
const hasError = ref(false) // 错误状态
const errorMessage = ref('') // 错误信息

// 运行时间缓存
interface RuntimeCache {
  runtimeHours: number
  runtimeMinutes: number
  displayText: string
  lastFetched: number // 缓存获取时间
}

const CACHE_KEY = 'station_runtime_cache'
const runtimeCache = ref<Record<string, RuntimeCache>>({}) // 按设备SN缓存运行时间

// 从localStorage加载缓存
function loadCacheFromStorage() {
  try {
    const stored = localStorage.getItem(CACHE_KEY)
    if (stored) {
      const parsedCache = JSON.parse(stored)
      // 检查缓存是否过期（超过5分钟则清除）
      const now = Date.now()
      const validCache: Record<string, RuntimeCache> = {}

      Object.entries(parsedCache).forEach(([key, value]) => {
        const cache = value as RuntimeCache
        if (cache.lastFetched && (now - cache.lastFetched) < 5 * 60 * 1000) {
          validCache[key] = cache
        }
      })

      runtimeCache.value = validCache
    }
  } catch (error) {
    console.warn('加载运行时间缓存失败:', error)
    localStorage.removeItem(CACHE_KEY)
  }
}

// 保存缓存到localStorage
function saveCacheToStorage() {
  try {
    localStorage.setItem(CACHE_KEY, JSON.stringify(runtimeCache.value))
  } catch (error) {
    console.warn('保存运行时间缓存失败:', error)
  }
}

// 筛选相关数据
const selectedArea = ref<string[]>([]) // 级联选择的地区 [镇区, 行政村, 自然村]
const selectedFacility = ref('')
const showFilterDialog = ref(false) // 筛选弹出框显示状态
const showAreaCascader = ref(false) // 地区级联选择器
const showFacilityPicker = ref(false)

// 筛选选项
const areaCascaderOptions = ref([
  {
    text: '松陵街道',
    value: '松陵街道',
    children: [
      {
        text: '联丰村',
        value: '联丰村',
        children: [
          { text: '联丰自然村', value: '联丰自然村' },
          { text: '东村', value: '东村' },
          { text: '西村', value: '西村' }
        ]
      },
      {
        text: '建设村',
        value: '建设村',
        children: [
          { text: '建设自然村', value: '建设自然村' },
          { text: '南村', value: '南村' }
        ]
      }
    ]
  },
  {
    text: '盛泽镇',
    value: '盛泽镇',
    children: [
      {
        text: '大船港村',
        value: '大船港村',
        children: [
          { text: '大船港自然村', value: '大船港自然村' },
          { text: '曹村', value: '曹村' }
        ]
      },
      {
        text: '星明村',
        value: '星明村',
        children: [
          { text: '星明自然村', value: '星明自然村' },
          { text: '北村', value: '北村' }
        ]
      }
    ]
  },
  {
    text: '同里镇',
    value: '同里镇',
    children: [
      {
        text: '新村',
        value: '新村',
        children: [
          { text: '新村自然村', value: '新村自然村' }
        ]
      }
    ]
  },
  {
    text: '黎里镇',
    value: '黎里镇',
    children: [
      {
        text: '横扇社区',
        value: '横扇社区',
        children: [
          { text: '横扇自然村', value: '横扇自然村' }
        ]
      }
    ]
  }
])

const facilityOptions = ref(['全部', '污水处理设施', '雨水处理设施', '生活污水处理站', '工业污水处理站'])

// 计算过滤后的站点列表
const filteredStations = computed(() => {
  // 确保 stations.value 是数组类型
  if (!Array.isArray(stations.value)) {
    return []
  }

  // 假筛选：简单返回所有站点，暂时不执行实际筛选逻辑
  // 这是为了避免筛选功能报错，后续可以完善筛选逻辑
  return stations.value

  // 原有的筛选逻辑（暂时注释掉）
  /*
  let filtered = stations.value

  // 地区筛选（级联）
  if (selectedArea.value && selectedArea.value.length > 0) {
    const [township, village, naturalVillage] = selectedArea.value

    if (township) {
      filtered = filtered.filter(station => station.township === township)
    }

    if (village) {
      filtered = filtered.filter(station => station.village === village)
    }

    if (naturalVillage) {
      filtered = filtered.filter(station => station.naturalVillage === naturalVillage)
    }
  }

  // 设施筛选
  if (selectedFacility.value && selectedFacility.value !== '全部') {
    filtered = filtered.filter(station => station.facility === selectedFacility.value)
  }

  return filtered
  */
})

// 计算活跃筛选条件数量
const activeFilterCount = computed(() => {
  // 假筛选：暂时总是返回0，避免筛选逻辑错误
  return 0

  // 原有的计算逻辑（暂时注释掉）
  /*
  let count = 0
  if (selectedArea.value && selectedArea.value.length > 0) {
    count += selectedArea.value.filter(v => v).length
  }
  if (selectedFacility.value && selectedFacility.value !== '全部') {
    count += 1
  }
  return count
  */
})

/**
 * 功能按钮点击事件
 */
function goToAlarms() {
  showToast({
    message: '告警事件功能开发中...',
    type: 'loading',
    duration: 1500
  })
  // TODO: 跳转到告警事件页面
}

function goToInspection() {
  showToast({
    message: '巡查工单功能开发中...',
    type: 'loading',
    duration: 1500
  })
  // TODO: 跳转到巡查工单页面
}

function goToMaintenance() {
  showToast({
    message: '故障维修功能开发中...',
    type: 'loading',
    duration: 1500
  })
  // TODO: 跳转到故障维修页面
}

function goToMessages() {
  showToast({
    message: '消息中心功能开发中...',
    type: 'loading',
    duration: 1500
  })
  // TODO: 跳转到消息中心页面
}

/**
 * 选择站点并跳转
 * @param station 选中的站点
 */
function selectStation(station: Station) {
  // 检查站点状态
  if (station.status === 'offline') {
    showToast({
      message: '该站点当前离线，可能无法获取实时数据',
      type: 'fail',
      duration: 2000
    })
  }

  showToast({
    message: `正在进入${station.name}...`,
    type: 'loading',
    duration: 1000
  })

  setTimeout(() => {
    router.push({
      name: 'device',
      params: {
        stationId: station.sn  // 使用SN作为stationId参数
      },
      query: {
        sn: station.sn,
        stationName: station.name,
        runningTime: station.runningTime,
        isOnline: station.status === 'online' ? 'true' : 'false'
      }
    })
  }, 1000)
}

/**
 * 刷新站点列表
 */
async function onRefresh() {
  refreshing.value = true
  try {
    await fetchStations()
    showToast({
      message: '刷新成功',
      type: 'success',
      duration: 1500
    })
  } catch (error: unknown) {
    console.error('刷新站点列表失败:', error)
    showToast({
      message: '刷新失败，请检查网络连接',
      type: 'fail',
      duration: 2000
    })
  } finally {
    refreshing.value = false
  }
}

/**
 * 重试加载
 */
async function retryLoad() {
  initialLoading.value = true
  hasError.value = false
  await fetchStations(true)
}

/**
 * 手动刷新站点信息（通过按钮触发）
 */
async function refreshStations() {
  try {
    // 清除运行时间缓存，强制重新计算
    runtimeCache.value = {}
    // 清除localStorage缓存
    localStorage.removeItem(CACHE_KEY)
    // 使用强制刷新模式获取最新数据
    await fetchStations(true)
    showToast({
      message: '站点信息已刷新',
      type: 'success',
      duration: 1500
    })
  } catch (error: unknown) {
    console.error('刷新站点信息失败:', error)
    showToast({
      message: '刷新失败，请稍后重试',
      type: 'fail',
      duration: 2000
    })
  }
}

/**
 * 获取状态类型
 */
function getStatusType(status: Station['status']) {
  switch (status) {
    case 'online':
      return 'success'
    case 'offline':
      return 'default'
    case 'abnormal':
      return 'danger'
    default:
      return 'default'
  }
}

/**
 * 获取状态文本
 */
function getStatusText(status: Station['status']) {
  switch (status) {
    case 'online':
      return '正常运行'
    case 'offline':
      return '离线'
    case 'abnormal':
      return '异常'
    default:
      return '未知'
  }
}

/**
 * 获取站点列表（从实际API获取）
 */
async function fetchStations(forceRefresh: boolean = false) {
  loading.value = true
  hasError.value = false

  try {
    const stationConfigs = getStationConfigs()
    const stationPromises = stationConfigs.map(async (config) => {
      try {
        // 并行调用两个接口：device-data用于设备状态判断，runtime用于运行时间
        const [dataResponse, runningTime] = await Promise.all([
          fetch(`${config.baseUrl}/device-data/${config.sn}.json`),
          fetchRunningTimeWithCache(config.sn, forceRefresh)
        ])

        // 检查数据接口响应
        if (!dataResponse.ok) {
          throw new Error(`HTTP ${dataResponse.status}`)
        }
        const data = await dataResponse.json()

        // 判断设备状态（基于data接口的数据）
        const status = determineStationStatus(data, dataResponse.status)

        return {
          ...config,
          runningTime,
          status,
          lastUpdated: data.timestamp || new Date().toISOString()
        }
      } catch (error) {
        console.warn(`站点 ${config.name} 获取数据失败:`, error)
        // 在降级情况下，如果不是强制刷新则使用缓存，否则显示失败信息
        const fallbackRunningTime = forceRefresh ?
          '数据获取失败' :
          (runtimeCache.value[config.sn]?.displayText || '数据获取失败')

        return {
          ...config,
          runningTime: fallbackRunningTime,
          status: 'offline' as const,
          lastUpdated: new Date().toISOString()
        }
      }
    })

    stations.value = await Promise.all(stationPromises)

    // 成功加载后重置错误状态
    hasError.value = false
    errorMessage.value = ''

  } catch (error: unknown) {
    console.error('获取站点列表失败:', error)

    // 设置错误状态
    hasError.value = true
    errorMessage.value = error instanceof Error ? error.message : '获取站点列表失败'

    // 如果有缓存数据，尝试使用缓存
    if (stations.value.length === 0) {
      stations.value = getStationConfigs().map(config => ({
        ...config,
        runningTime: forceRefresh ? '数据获取失败' : (runtimeCache.value[config.sn]?.displayText || '0小时'),
        status: 'offline' as const,
        lastUpdated: new Date().toISOString()
      }))
    }

    if (!forceRefresh) {
      showToast('获取站点列表失败，显示缓存数据')
    }
  } finally {
    loading.value = false
    initialLoading.value = false
  }
}

/**
 * 带缓存的运行时间获取
 */
async function fetchRunningTimeWithCache(deviceSn: string, forceRefresh: boolean = false): Promise<string> {
  const now = Date.now()
  const cacheKey = deviceSn

  // 如果强制刷新，跳过缓存检查
  if (!forceRefresh) {
    // 检查缓存是否有效（5分钟内的缓存认为有效）
    const cached = runtimeCache.value[cacheKey]
    if (cached && (now - cached.lastFetched) < 5 * 60 * 1000) {
      return cached.displayText
    }
  }

  try {
    // 调用后端运行时间接口
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000) // 10秒超时

    const response = await fetch(`http://**************:8500/device-runtime/stats?device_sn=${deviceSn}`, {
      signal: controller.signal
    })

    clearTimeout(timeoutId)
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`)
    }

    const data = await response.json()

    // 检查接口返回状态
    if (data.status !== 'success' || !data.data) {
      throw new Error('运行时间数据获取失败')
    }

    // 使用新接口返回的格式化文本
    const displayText = data.data.online_time_formatted || '0分钟'
    const runtimeHours = data.data.hours || 0
    const runtimeMinutes = data.data.minutes || 0

    // 更新缓存
    runtimeCache.value[cacheKey] = {
      runtimeHours,
      runtimeMinutes,
      displayText,
      lastFetched: now
    }

    // 保存到localStorage
    saveCacheToStorage()

    return displayText
  } catch (error) {
    console.warn(`获取设备 ${deviceSn} 运行时间失败:`, error)
    // 如果有缓存数据，使用缓存，否则返回默认值
    const cached = runtimeCache.value[cacheKey]
    return cached?.displayText || '0小时'
  }
}

/**
 * 判断站点状态
 */
function determineStationStatus(data: Record<string, unknown>, httpStatus: number): Station['status'] {
  // 如果HTTP请求失败，视为离线
  if (httpStatus !== 200) {
    return 'offline'
  }

  // 检查数据时间戳，超过30分钟无更新视为离线
  if (data.timestamp && typeof data.timestamp === 'string') {
    const lastUpdate = new Date(data.timestamp)
    const now = new Date()
    const diffMinutes = (now.getTime() - lastUpdate.getTime()) / (1000 * 60)

    if (diffMinutes > 30) {
      return 'offline'
    }
  }

  // 只要站点在线，就是正常运行
  return 'online'
}

/**
 * 获取地区显示文本
 */
function getAreaDisplayText(): string {
  if (!Array.isArray(selectedArea.value) || selectedArea.value.length === 0) {
    return '全部'
  }
  return selectedArea.value.filter(item => item).join(' / ')
}

/**
 * 移除地区级别选择
 */
function removeAreaLevel(index: number) {
  // 确保 selectedArea.value 是数组
  if (!Array.isArray(selectedArea.value)) {
    selectedArea.value = []
    return
  }
  // 移除指定级别及其后面的所有级别
  selectedArea.value = selectedArea.value.slice(0, index)
}

/**
 * 地区级联选择完成
 */
function onAreaCascaderFinish({ value }: { value: string[] }) {
  // 确保传入的值是数组
  selectedArea.value = Array.isArray(value) ? value : []
  showAreaCascader.value = false
}

/**
 * 设施选择确认
 */
function onFacilityConfirm({ selectedValues }: { selectedValues: string[] }) {
  selectedFacility.value = selectedValues[0]
  showFacilityPicker.value = false
}

/**
 * 应用筛选
 */
function applyFilter() {
  const count = activeFilterCount.value
  showFilterDialog.value = false // 关闭弹出框
  if (count > 0) {
    showToast({
      message: `已应用${count}个筛选条件`,
      type: 'success',
      duration: 1500
    })
  } else {
    showToast({
      message: '已显示全部站点',
      type: 'success',
      duration: 1500
    })
  }
}

/**
 * 重置筛选
 */
function resetFilter() {
  selectedArea.value = []
  selectedFacility.value = ''
  showToast({
    message: '筛选条件已重置',
    type: 'success',
    duration: 1500
  })
}

/**
 * 获取站点基础配置
 */
function getStationConfigs(): Omit<Station, 'runningTime' | 'status' | 'lastUpdated'>[] {
  return [
    {
      id: '02800125081400008508',
      name: '联丰村污水处理站',
      sn: '02800125081400008508',
      location: '苏州市吴江区松陵街道联丰村',
      baseUrl: 'http://**************:8500',
      township: '松陵街道',
      village: '联丰村',
      naturalVillage: '联丰自然村',
      facility: '污水处理设施'
    },
    {
      id: '02800125071500004977',
      name: '大船港村污水处理站',
      sn: '02800125071500004977',
      location: '苏州市吴江区盛泽镇大船港村',
      baseUrl: 'http://**************:8500',
      township: '盛泽镇',
      village: '大船港村',
      naturalVillage: '大船港自然村',
      facility: '生活污水处理站'
    }
  ]
}

onMounted(() => {
  // 确保 selectedArea 初始化为数组
  if (!Array.isArray(selectedArea.value)) {
    selectedArea.value = []
  }

  // 从localStorage加载缓存
  loadCacheFromStorage()

  fetchStations()

  // 每30秒更新一次运行时间显示（不重新请求数据，只更新时间计算）
  setInterval(() => {
    updateRuntimeDisplay()
  }, 30000)
})

/**
 * 更新运行时间显示（基于缓存，不请求接口）
 */
function updateRuntimeDisplay() {
  if (!Array.isArray(stations.value)) return

  stations.value = stations.value.map(station => {
    const cached = runtimeCache.value[station.sn]
    if (cached) {
      // 使用缓存的显示文本
      return {
        ...station,
        runningTime: cached.displayText
      }
    }
    return station
  })
}
</script>

<style scoped>
.station-select-container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.content-wrapper {
  padding-top: 46px;
  /* 为固定导航栏留出空间 */
  padding-bottom: 20px;
  min-height: calc(100vh - 66px);
}

/* Banner区域样式 */
.banner-section {
  position: relative;
  margin: 16px;
  padding: 24px 20px;
  background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
  border-radius: 16px;
  overflow: hidden;
  color: white;
}

.banner-content {
  position: relative;
  z-index: 2;
}

.banner-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 4px;
}

.banner-subtitle {
  font-size: 14px;
  opacity: 0.9;
}

.banner-decoration {
  position: absolute;
  top: -20px;
  right: -20px;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  z-index: 1;
}

/* 功能按钮区域样式 */
.function-buttons {
  margin: 0 16px 16px;
}

.button-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.function-button {
  background: white;
  border-radius: 12px;
  padding: 16px 8px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #f0f0f0;
}

.function-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.function-button:active {
  transform: translateY(0);
}

.button-icon {
  font-size: 24px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
}

.button-text {
  font-size: 12px;
  color: #323233;
  font-weight: 500;
}

/* 功能按钮图标特定样式 */
.alarm-icon {
  filter: hue-rotate(0deg);
}

.inspection-icon {
  filter: hue-rotate(200deg);
}

.maintenance-icon {
  filter: hue-rotate(30deg);
}

.message-icon {
  filter: hue-rotate(120deg);
}

/* 加载和错误状态样式 */
.initial-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 40px 20px;
}

.error-state {
  padding: 40px 20px;
}

.bottom-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.9);
  margin: 12px 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.loading-text {
  font-size: 14px;
  color: #969799;
}

/* 站点列表优化 */
:deep(.van-cell) {
  padding: 16px;
  transition: background-color 0.2s ease;
}

:deep(.van-cell:active) {
  background-color: #f8f9fa;
}

:deep(.van-cell__title) {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
  margin-bottom: 4px;
}

:deep(.van-cell__label) {
  font-size: 13px;
  color: #969799;
}

/* 改进下拉刷新视觉效果 */
:deep(.van-pull-refresh__track) {
  min-height: calc(100vh - 320px);
}

.filter-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  margin: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.filter-toolbar-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.filter-toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-count-tag {
  margin-left: 4px !important;
}

.quick-filters {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
  overflow-x: auto;
  scrollbar-width: none;
}

.quick-filters::-webkit-scrollbar {
  display: none;
}

.filter-dialog {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.filter-dialog-header {
  text-align: center;
  margin-bottom: 24px;
}

.filter-dialog-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #323233;
}

.filter-dialog-header p {
  margin: 0;
  font-size: 14px;
  color: #969799;
}

.filter-dialog-content {
  flex: 1;
  margin-bottom: 24px;
}

.filter-dialog-actions {
  display: flex;
  gap: 12px;
}

.filter-dialog-actions .van-button {
  flex: 1;
  height: 44px;
  border-radius: 12px;
}

.station-list {
  margin: 16px;
}

/* 自定义样式覆盖 */
:deep(.van-cell-group) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.van-cell) {
  padding: 16px;
}

:deep(.van-cell__title) {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
}

:deep(.van-cell__label) {
  font-size: 14px;
  color: #969799;
  margin-top: 4px;
}

:deep(.van-loading) {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

:deep(.van-empty) {
  margin-top: 60px;
}

:deep(.van-tag) {
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 12px;
}

/* 移动端适配 */
@media (max-width: 480px) {
  .banner-section {
    margin: 8px;
    padding: 20px 16px;
  }

  .banner-title {
    font-size: 18px;
  }

  .banner-subtitle {
    font-size: 13px;
  }

  .function-buttons {
    margin: 0 8px 12px;
  }

  .button-row {
    gap: 8px;
  }

  .function-button {
    padding: 12px 6px;
  }

  .button-icon {
    font-size: 20px;
    height: 28px;
    margin-bottom: 6px;
  }

  .button-text {
    font-size: 11px;
  }

  .filter-toolbar {
    margin: 8px;
    padding: 8px 12px;
  }

  .filter-toolbar-left {
    gap: 6px;
  }

  .quick-filters {
    gap: 4px;
  }

  .filter-dialog {
    padding: 20px;
  }

  .filter-dialog-header {
    margin-bottom: 20px;
  }

  .filter-dialog-actions {
    gap: 8px;
  }

  .station-list {
    margin: 8px;
  }

  :deep(.van-cell) {
    padding: 12px;
  }
}
</style>
