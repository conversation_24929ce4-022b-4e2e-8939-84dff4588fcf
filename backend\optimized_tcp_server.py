import asyncio
import json
import datetime
from data_store import (
    DEVICE_DATA,
    DATA_LOCK,
    CONNECTED_CLIENTS,
    CLIENTS_LOCK,
)
from mysql_connection import MySQLSessionLocal as SessionLocal
import crud
import data_store
from mongodb_models import get_mongodb_manager
from realtime_data_logger import log_device_data, log_raw_json
from rabbitmq_publisher import get_rabbitmq_publisher

# 启动降采样服务
try:
    from data_downsampling_service import start_data_services
    print(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [init] 启动数据降采样服务...")
    start_data_services()
    print(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [init] 数据降采样服务启动成功")
except Exception as e:
    print(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [init] 降采样服务启动失败: {e}")
    print(f"[{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] [init] 系统将继续运行，但不会进行数据降采样")

# 服务器配置
HOST = "0.0.0.0"
PORT = 8889

# 全局连接管理
connected_clients = {}  # {client_id: (transport, protocol)}
client_counter = 0


class WaterStationProtocol(asyncio.Protocol):
    """水利站点异步协议处理器"""
    
    def __init__(self):
        global client_counter
        self.client_id = client_counter
        client_counter += 1
        self.transport = None
        self.peername = None
        self.buffer = ""
        self.device_sn = None
        
    def connection_made(self, transport):
        """连接建立时的回调"""
        self.transport = transport
        self.peername = transport.get_extra_info('peername')
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{current_time}] [handle_client] 接受来自 {self.peername[0]}:{self.peername[1]} 的连接")
        
        # 将客户端添加到全局连接字典
        connected_clients[self.client_id] = (self.transport, self)
        
    def data_received(self, data):
        """接收到数据时的回调"""
        # 将接收到的字节解码为字符串并拼接到缓冲区
        self.buffer += data.decode("utf-8", errors="ignore")
        
        # 持续尝试从缓冲区中提取并处理完整的JSON对象
        while True:
            start_index = self.buffer.find("{")
            # 如果缓冲区里连 '{' 都没有，那就没必要继续了
            if start_index == -1:
                break

            # 从找到的第一个'{'开始，通过计算括号配对来寻找完整的JSON
            brace_count = 0
            end_index = -1
            for i in range(start_index, len(self.buffer)):
                if self.buffer[i] == "{":
                    brace_count += 1
                elif self.buffer[i] == "}":
                    brace_count -= 1

                # 当括号计数回到0时，说明找到了匹配的'}'
                if brace_count == 0:
                    end_index = i
                    break

            # 如果找到了完整的JSON (end_index有效)
            if end_index != -1:
                json_str = self.buffer[start_index : end_index + 1]
                # 从缓冲区中移除已经处理过的JSON字符串
                self.buffer = self.buffer[end_index + 1 :]
                
                # 异步处理JSON数据
                asyncio.create_task(self.process_json_data(json_str))
            else:
                # 如果没有找到完整的JSON，退出内层循环，继续接收更多数据
                break
                
    async def process_json_data(self, json_str):
        """异步处理JSON数据"""
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        try:
            # 解析JSON数据
            parsed_data = json.loads(json_str)
            print(
                f"[{current_time}] [handle_client] 收到来自 {self.peername[0]}:{self.peername[1]} 的有效JSON"
            )
            
            # 注释掉无条件记录，改为在消息分类后有条件记录
            # log_raw_json(json_str, f"{self.peername[0]}:{self.peername[1]}")

            # 检查是否为单独的状态变化消息
            message_type = parsed_data.get("message_type")
            if message_type in ["status_change", "mode_change"]:
                # 这是单独的状态变化消息，直接记录到状态日志
                current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(f"[{current_time}] [async_tcp] 检测到单独状态变化消息: {message_type}")
                
                # 从状态变化消息中提取设备SN
                sn = None
                if "sn" in parsed_data:
                    sn = parsed_data["sn"]
                elif "data" in parsed_data and isinstance(parsed_data["data"], dict):
                    data = parsed_data["data"]
                    if "device_info" in data and isinstance(data["device_info"], dict):
                        sn = data["device_info"].get("sn")
                elif "device_info" in parsed_data and isinstance(parsed_data["device_info"], dict):
                    sn = parsed_data["device_info"].get("sn")
                elif "deviceSN" in parsed_data:
                    sn = parsed_data["deviceSN"]
                
                if sn:
                    # 写入状态变化日志文件
                    try:
                        from device_status_logger import _write_status_change_to_file
                        changes = [{
                            'message_type': message_type,
                            'change_source': parsed_data.get('change_source', 'unknown'),
                            'raw_data': parsed_data,
                            'timestamp': current_time
                        }]
                        _write_status_change_to_file(sn, changes, f"{self.peername[0]}:{self.peername[1]}", True)
                    except Exception as e:
                        print(f"[{current_time}] [状态日志] 记录单独状态变化失败: {e}")
                    
                    # 推送到RabbitMQ
                    try:
                        from rabbitmq_publisher import get_rabbitmq_publisher
                        publisher = get_rabbitmq_publisher()
                        status_change_data = {
                            'timestamp': current_time,
                            'device_sn': sn,
                            'client_address': f"{self.peername[0]}:{self.peername[1]}",
                            'message_type': message_type,
                            'change_source': parsed_data.get('change_source', 'unknown'),
                            'raw_message': parsed_data,
                            'log_type': '设备物理开关档位变换' if message_type == 'mode_change' else '设备DO状态变化'
                        }
                        publisher.publish_status_change(sn, [status_change_data], f"{self.peername[0]}:{self.peername[1]}", True)
                    except Exception as e:
                        print(f"[{current_time}] [rabbitmq] 推送单独状态变化到RabbitMQ失败: {e}")
                else:
                    print(f"[{current_time}] [async_tcp] 状态变化消息中无法提取设备SN")
                
                # 状态变化消息处理完毕，不需要继续处理为设备数据
                return

            # 从数据中提取设备序列号 'sn'
            sn = None
            is_status_only_message = False  # 标记是否为单独的状态变化消息
            
            # 检查是否为单独的状态变化消息
            status_fields = ['DO21_status', 'DO22_status', 'DO23_status', 'DO24_status']
            pump_fields = ['water_pump1', 'water_pump2', 'air_pump1', 'air_pump2']
            has_status_fields = any(field in parsed_data for field in status_fields + pump_fields)
            has_complete_data = 'wenshidu' in parsed_data or 'diannengbiao' in parsed_data
            
            if has_status_fields and not has_complete_data:
                is_status_only_message = True
                print(f"[{current_time}] [handle_client] 检测到单独的状态变化消息")
                
            # 优先检查根级别的sn字段（标准格式）
            if "sn" in parsed_data:
                sn = parsed_data["sn"]
            # 检查data.device_info.sn字段（数据结构中的备用位置）
            elif "data" in parsed_data and isinstance(parsed_data["data"], dict):
                data = parsed_data["data"]
                if "device_info" in data and isinstance(data["device_info"], dict):
                    sn = data["device_info"].get("sn")
            # 检查device_info.sn字段（M100格式）
            elif "device_info" in parsed_data and isinstance(
                parsed_data["device_info"], dict
            ):
                sn = parsed_data["device_info"].get("sn")
            # 检查deviceSN字段（其他格式）
            elif "deviceSN" in parsed_data:
                sn = parsed_data["deviceSN"]

            if sn:
                # 保存设备SN用于连接管理
                self.device_sn = sn
                
                # 优化后的异步连接管理逻辑
                current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                with CLIENTS_LOCK:
                    old_client = CONNECTED_CLIENTS.get(sn)
                    
                    if old_client is None:
                        # 情况1：新设备首次连接
                        CONNECTED_CLIENTS[sn] = {
                            "socket": self.transport,
                            "address": self.peername,
                            "protocol": self,  # 保存协议实例
                            "first_seen": current_time,
                            "last_data": current_time
                        }
                        print(f"[{current_time}] [async_tcp] 设备 {sn} 首次连接")
                        
                    elif old_client.get("protocol") == self:
                        # 情况2：同一连接的后续数据包（正常情况）
                        old_client["last_data"] = current_time
                        # 不打印日志，避免刷屏
                        
                    else:
                        # 情况3：真正的重新连接（不同协议实例）
                        print(f"[{current_time}] [async_tcp] 检测到设备 {sn} 重新连接")
                        print(f"[{current_time}] [async_tcp] 旧连接: {old_client['address']}, 新连接: {self.peername}")
                        
                        # 关闭旧连接
                        try:
                            if old_client["socket"] and not old_client["socket"].is_closing():
                                old_client["socket"].close()
                                print(f"[{current_time}] [async_tcp] 已关闭设备 {sn} 的旧连接")
                        except Exception as e:
                            print(f"[{current_time}] [async_tcp] 关闭旧连接失败: {e}")
                        
                        # 更新为新连接
                        CONNECTED_CLIENTS[sn] = {
                            "socket": self.transport,
                            "address": self.peername,
                            "protocol": self,
                            "first_seen": old_client.get("first_seen", current_time),
                            "last_data": current_time,
                            "reconnect_count": old_client.get("reconnect_count", 0) + 1
                        }
                        print(f"[{current_time}] [async_tcp] 设备 {sn} 重连完成 (重连次数: {CONNECTED_CLIENTS[sn]['reconnect_count']})")
                
                # 使用线程池执行数据库操作，避免阻塞事件循环
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(None, self.handle_device_data, sn, parsed_data, is_status_only_message)
                
                # 服务器回复
                response = "Server received your message."
                try:
                    self.transport.write(response.encode("utf-8"))
                except Exception as e:
                    print(
                        f"[{current_time}] [handle_client] 发送回复失败: {e}."
                    )
            else:
                # 如果JSON中没有 'sn', 我们假定它是一个命令执行后的响应包。
                print(
                    f"[{current_time}] [handle_client] 收到无设备SN的JSON数据，可能是指令回执: {json_str}"
                )

        except json.JSONDecodeError as e:
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(
                f"[{current_time}] [handle_client] JSON解析失败: {e}, 数据: {json_str}"
            )
            
    def handle_device_data(self, sn, parsed_data, is_status_only_message):
        """处理设备数据（在线程池中执行）"""
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        db = SessionLocal()
        try:
            # 提取实际的设备数据（处理嵌套格式）
            device_data = parsed_data
            if "data" in parsed_data and isinstance(parsed_data["data"], dict):
                # 数据格式：数据在data字段中，将其提取出来与根级别合并
                device_data = parsed_data["data"].copy()
                # 保留根级别的重要字段
                for key in ["sn", "timestamp", "topic"]:
                    if key in parsed_data:
                        device_data[key] = parsed_data[key]
            
            # 记录设备数据到实时日志文件
            # 注释掉直接日志记录，改由RabbitMQ消息处理器统一处理，确保格式一致
            # log_device_data(sn, device_data, f"{self.peername[0]}:{self.peername[1]}")
            
            # 推送设备数据到RabbitMQ消息队列
            try:
                publisher = get_rabbitmq_publisher()
                publisher.publish_device_data(sn, device_data, f"{self.peername[0]}:{self.peername[1]}")
            except Exception as e:
                print(f"[{current_time}] [rabbitmq] 推送设备数据到RabbitMQ失败: {e}")
            
            # 1. 更新内存缓存 (用于API快速返回最新状态)
            with DATA_LOCK:
                if sn not in DEVICE_DATA:
                    DEVICE_DATA[sn] = {}
                DEVICE_DATA[sn].update(device_data)
                DEVICE_DATA[sn]["last_updated"] = current_time

            # 2. 将5秒级实时数据存储到MongoDB（内存数据库）
            try:
                mongodb_manager = get_mongodb_manager()
                mongodb_manager.insert_device_data(sn, device_data)
                print(f"[{current_time}] [handle_client] 设备 {sn} 数据已存储到MongoDB")
            except Exception as mongo_error:
                print(f"[{current_time}] [handle_client] MongoDB存储失败: {mongo_error}")

        finally:
            db.close()

    def connection_lost(self, exc):
        """连接断开时的回调"""
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 从全局连接字典中移除连接信息
        if self.client_id in connected_clients:
            del connected_clients[self.client_id]
            
        # 如果有设备SN，也从设备连接字典中移除
        if self.device_sn:
            with CLIENTS_LOCK:
                if self.device_sn in CONNECTED_CLIENTS:
                    del CONNECTED_CLIENTS[self.device_sn]
            print(
                f"[{current_time}] [handle_client] 设备 {self.device_sn} 的连接信息已从连接池中移除。"
            )
            
        if exc:
            print(f"[{current_time}] [handle_client] 连接断开，原因: {exc}")
        else:
            print(
                f"[{current_time}] [handle_client] 关闭与客户端 {self.peername[0]}:{self.peername[1]} 的连接"
            )


async def start_server():
    """启动异步TCP服务器"""
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{current_time}] [start_server] 正在启动异步TCP服务器...")

    # 创建服务器
    loop = asyncio.get_running_loop()
    server = await loop.create_server(
        protocol_factory=WaterStationProtocol,
        host=HOST,
        port=PORT
    )

    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{current_time}] [start_server] 异步TCP服务器已启动，监听 {HOST}:{PORT}")

    # 保持服务器运行
    async with server:
        await server.serve_forever()


if __name__ == "__main__":
    asyncio.run(start_server())