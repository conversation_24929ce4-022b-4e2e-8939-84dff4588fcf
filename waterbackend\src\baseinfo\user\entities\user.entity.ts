import { ApiProperty } from '@nestjs/swagger';
import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

enum Role {
  User = 'user',
  Admin = 'admin',
}

@Entity()
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: '用户名', example: 'admin' })
  @Index({ unique: true }) // 唯一索引
  @Column('varchar', { length: 50 })
  username: string;

  @ApiProperty({ description: '最后登录时间', example: '2025-08-29T09:29:27.000Z' })
  @Column('datetime', { precision: 3, nullable: true })
  lastLoginTime: Date;

  @ApiProperty({ description: '最后登出时间', example: '2025-08-29T09:29:27.000Z' })
  @Column('datetime', { precision: 3, nullable: true })
  lastLogoutTime: Date;

  @ApiProperty({ description: '密码', example: '123456' })
  @Column('text', { nullable: true })
  password: string;

  @ApiProperty({
    description: '权限',
    example: 'user',
    enum: Role, // 使用枚举来限制可选值
  })
  @Column({
    type: 'text',
    nullable: true,
  })
  roles: string;

  @ApiProperty({ description: '最后登录ip' })
  @Column('text', { nullable: true })
  lastip: string;

  @ApiProperty({
    description: '页面权限列表',
    example: ['/dashboard', '/system/user'],
    type: [String],
  })
  @Column('simple-array', { nullable: true })
  permissions: string[];
}
