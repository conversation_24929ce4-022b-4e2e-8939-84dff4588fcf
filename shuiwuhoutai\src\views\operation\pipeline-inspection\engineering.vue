<template>
  <div class="pipeline-inspection-engineering">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <el-button type="primary" @click="handleCreateProject"
            >新建工程</el-button
          >
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="工程名称">
          <el-input
            v-model="searchForm.projectName"
            placeholder="请输入工程名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="工程类型">
          <el-select
            v-model="searchForm.projectType"
            placeholder="请选择类型"
            clearable
          >
            <el-option label="新建工程" value="new" />
            <el-option label="维修工程" value="repair" />
            <el-option label="改造工程" value="renovation" />
          </el-select>
        </el-form-item>
        <el-form-item label="工程状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="规划中" value="planning" />
            <el-option label="施工中" value="construction" />
            <el-option label="已完工" value="completed" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 工程列表 -->
      <el-table :data="projectData" stripe>
        <el-table-column prop="projectNo" label="工程编号" width="120" />
        <el-table-column prop="projectName" label="工程名称" />
        <el-table-column prop="projectType" label="工程类型" width="120">
          <template #default="{ row }">
            {{ getTypeText(row.projectType) }}
          </template>
        </el-table-column>
        <el-table-column prop="location" label="工程位置" width="150" />
        <el-table-column prop="startDate" label="开始时间" width="150" />
        <el-table-column prop="endDate" label="结束时间" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="budget" label="预算" width="120">
          <template #default="{ row }"> {{ row.budget }}万元 </template>
        </el-table-column>
        <el-table-column prop="manager" label="项目经理" width="100" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)"
              >查看</el-button
            >
            <el-button type="success" size="small" @click="handleEdit(row)"
              >编辑</el-button
            >
            <el-button type="danger" size="small" @click="handleDelete(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";

// 搜索表单
const searchForm = reactive({
  projectName: "",
  projectType: "",
  status: "",
});

// 工程数据
const projectData = ref([
  {
    id: 1,
    projectNo: "PE202401001",
    projectName: "主干道供水管网改造",
    projectType: "renovation",
    location: "市中心主干道",
    startDate: "2024-01-01",
    endDate: "2024-06-30",
    status: "construction",
    budget: 500,
    manager: "李工",
  },
]);

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0,
});

// 获取类型文本
const getTypeText = (type: string) => {
  const texts: Record<string, string> = {
    new: "新建工程",
    repair: "维修工程",
    renovation: "改造工程",
  };
  return texts[type] || "其他";
};

// 获取状态类型和文本
const getStatusType = (status: string): "success" | "primary" | "warning" | "info" | "danger" => {
  const types: Record<string, "success" | "primary" | "warning" | "info" | "danger"> = {
    planning: "warning",
    construction: "primary",
    completed: "success",
  };
  return types[status] || "info";
};

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    planning: "规划中",
    construction: "施工中",
    completed: "已完工",
  };
  return texts[status] || "未知";
};

// 事件处理函数
const handleCreateProject = () => console.log("新建工程");
const handleSearch = () => console.log("搜索工程", searchForm);
const handleReset = () =>
  Object.assign(searchForm, { projectName: "", projectType: "", status: "" });
const handleView = (row: any) => console.log("查看工程", row);
const handleEdit = (row: any) => console.log("编辑工程", row);
const handleDelete = (row: any) => console.log("删除工程", row);
const handleSizeChange = (size: number) => {
  pagination.size = size;
};
const handleCurrentChange = (page: number) => {
  pagination.page = page;
};

onMounted(() => {
  pagination.total = projectData.value.length;
});
</script>

<style scoped>
.pipeline-inspection-engineering {
  padding: 20px;
}

.page-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
