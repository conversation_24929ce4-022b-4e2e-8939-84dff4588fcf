<template>
  <div class="area-energy-analysis">
    <el-card shadow="hover" class="page-header">
      <template #header>
        <div class="card-header">
          <div class="header-actions">
            <el-button type="primary" size="small" @click="generateAreaReport"
              >生成区域报表</el-button
            >
            <el-button type="success" size="small" @click="compareAreas"
              >区域对比</el-button
            >
          </div>
        </div>
      </template>

      <!-- 查询表单 -->
      <el-form :inline="true" :model="queryForm" class="query-form">
        <el-form-item label="选择区域">
          <el-select
            v-model="queryForm.areas"
            placeholder="请选择区域"
            multiple
            clearable
          >
            <el-option label="浦南区域" value="punan" />
            <el-option label="史北区域" value="shibei" />
            <el-option label="东部区域" value="east" />
            <el-option label="西部区域" value="west" />
          </el-select>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="分析类型">
          <el-select
            v-model="queryForm.analysisType"
            placeholder="请选择分析类型"
          >
            <el-option label="总量分析" value="total" />
            <el-option label="强度分析" value="intensity" />
            <el-option label="效率分析" value="efficiency" />
            <el-option label="趋势分析" value="trend" />
          </el-select>
        </el-form-item>

        <el-form-item label="报表格式">
          <el-select v-model="queryForm.format" placeholder="请选择格式">
            <el-option label="详细报表" value="detailed" />
            <el-option label="摘要报表" value="summary" />
            <el-option label="对比报表" value="comparison" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 区域能耗概览 -->
    <div class="area-overview">
      <el-row :gutter="20">
        <el-col :span="6" v-for="(area, index) in areaOverview" :key="index">
          <el-card shadow="hover" :class="['area-card', area.code]">
            <div class="area-header">
              <div class="area-name">{{ area.name }}</div>
              <div class="area-status">
                <el-tag
                  :type="area.status === 'normal' ? 'success' : 'warning'"
                  size="small"
                >
                  {{ area.status === "normal" ? "正常" : "预警" }}
                </el-tag>
              </div>
            </div>
            <div class="area-metrics">
              <div class="metric-row">
                <span class="metric-label">总能耗:</span>
                <span class="metric-value">{{ area.totalEnergy }} kWh</span>
              </div>
              <div class="metric-row">
                <span class="metric-label">单位能耗:</span>
                <span class="metric-value">{{ area.unitEnergy }} kWh/m³</span>
              </div>
              <div class="metric-row">
                <span class="metric-label">能效比:</span>
                <span class="metric-value">{{ area.efficiency }}%</span>
              </div>
              <div class="metric-row">
                <span class="metric-label">环比变化:</span>
                <span
                  :class="[
                    'metric-value',
                    area.change >= 0 ? 'increase' : 'decrease',
                  ]"
                >
                  {{ area.change >= 0 ? "+" : "" }}{{ area.change }}%
                </span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 区域对比分析图表 -->
    <div class="comparison-charts">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>区域能耗总量对比</span>
                <el-radio-group v-model="totalCompareType" size="small">
                  <el-radio-button label="month">月度</el-radio-button>
                  <el-radio-button label="quarter">季度</el-radio-button>
                  <el-radio-button label="year">年度</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div id="areaTotalComparisonChart" style="height: 350px"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>区域能效对比</span>
              </div>
            </template>
            <div id="areaEfficiencyComparisonChart" style="height: 350px"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="24">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>区域能耗趋势分析</span>
                <div>
                  <el-select
                    v-model="trendMetric"
                    size="small"
                    style="margin-right: 10px"
                  >
                    <el-option label="总能耗" value="total" />
                    <el-option label="单位能耗" value="unit" />
                    <el-option label="成本" value="cost" />
                  </el-select>
                  <el-button-group size="small">
                    <el-button
                      :type="trendPeriod === '7d' ? 'primary' : ''"
                      @click="trendPeriod = '7d'"
                      >7天</el-button
                    >
                    <el-button
                      :type="trendPeriod === '30d' ? 'primary' : ''"
                      @click="trendPeriod = '30d'"
                      >30天</el-button
                    >
                    <el-button
                      :type="trendPeriod === '90d' ? 'primary' : ''"
                      @click="trendPeriod = '90d'"
                      >90天</el-button
                    >
                  </el-button-group>
                </div>
              </div>
            </template>
            <div id="areaTrendChart" style="height: 350px"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 区域能耗排行榜 -->
    <el-row :gutter="20" style="margin-bottom: 20px">
      <el-col :span="8">
        <el-card shadow="hover" class="ranking-card">
          <template #header>
            <div class="card-header">
              <span>能耗总量排行</span>
              <el-tag type="info" size="small">降序</el-tag>
            </div>
          </template>
          <div class="ranking-list">
            <div
              v-for="(item, index) in energyRanking"
              :key="index"
              class="ranking-item"
            >
              <div class="ranking-medal" :class="'rank-' + (index + 1)">
                {{ index + 1 }}
              </div>
              <div class="ranking-content">
                <div class="ranking-name">{{ item.areaName }}</div>
                <div class="ranking-value">{{ item.totalEnergy }} kWh</div>
              </div>
              <div class="ranking-badge">
                <el-tag :type="getRankingType(index)" size="small">
                  {{ getRankingLabel(index) }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover" class="ranking-card">
          <template #header>
            <div class="card-header">
              <span>能效排行</span>
              <el-tag type="success" size="small">升序</el-tag>
            </div>
          </template>
          <div class="ranking-list">
            <div
              v-for="(item, index) in efficiencyRanking"
              :key="index"
              class="ranking-item"
            >
              <div class="ranking-medal" :class="'rank-' + (index + 1)">
                {{ index + 1 }}
              </div>
              <div class="ranking-content">
                <div class="ranking-name">{{ item.areaName }}</div>
                <div class="ranking-value">{{ item.efficiency }}%</div>
              </div>
              <div class="ranking-badge">
                <el-tag type="success" size="small">优秀</el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover" class="ranking-card">
          <template #header>
            <div class="card-header">
              <span>节能率排行</span>
              <el-tag type="success" size="small">升序</el-tag>
            </div>
          </template>
          <div class="ranking-list">
            <div
              v-for="(item, index) in savingRanking"
              :key="index"
              class="ranking-item"
            >
              <div class="ranking-medal" :class="'rank-' + (index + 1)">
                {{ index + 1 }}
              </div>
              <div class="ranking-content">
                <div class="ranking-name">{{ item.areaName }}</div>
                <div class="ranking-value">{{ item.savingRate }}%</div>
              </div>
              <div class="ranking-badge">
                <el-tag type="success" size="small">领先</el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 区域详细分析表格 -->
    <el-card shadow="hover" class="table-card">
      <template #header>
        <div class="card-header">
          <span>区域能耗详细分析</span>
          <div class="table-actions">
            <el-button size="small" @click="exportAreaReport"
              >导出报表</el-button
            >
            <el-button size="small" type="warning" @click="setEnergyTarget"
              >设置目标</el-button
            >
            <el-button size="small" type="primary" @click="energyForecast"
              >能耗预测</el-button
            >
          </div>
        </div>
      </template>

      <el-table :data="areaDetailData" stripe style="width: 100%" border>
        <el-table-column type="selection" width="55" />
        <el-table-column
          prop="areaName"
          label="区域名称"
          width="100"
          fixed="left"
        />
        <el-table-column prop="stationCount" label="站点数量" width="80" />
        <el-table-column
          prop="totalEnergy"
          label="总能耗(kWh)"
          width="120"
          sortable
        />
        <el-table-column
          prop="electricityEnergy"
          label="电力能耗"
          width="110"
        />
        <el-table-column prop="fuelEnergy" label="燃料能耗" width="110" />
        <el-table-column
          prop="unitEnergy"
          label="单位能耗"
          width="100"
          sortable
        />
        <el-table-column prop="efficiency" label="能效比(%)" width="100">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.efficiency"
              :color="getEfficiencyColor(scope.row.efficiency)"
            />
          </template>
        </el-table-column>
        <el-table-column
          prop="totalCost"
          label="总成本(万元)"
          width="110"
          sortable
        />
        <el-table-column prop="unitCost" label="单位成本" width="100" />
        <el-table-column prop="monthlyChange" label="月环比(%)" width="100">
          <template #default="scope">
            <span
              :class="scope.row.monthlyChange >= 0 ? 'increase' : 'decrease'"
            >
              {{ scope.row.monthlyChange >= 0 ? "+" : ""
              }}{{ scope.row.monthlyChange }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="yearlyChange" label="年同比(%)" width="100">
          <template #default="scope">
            <span
              :class="scope.row.yearlyChange >= 0 ? 'increase' : 'decrease'"
            >
              {{ scope.row.yearlyChange >= 0 ? "+" : ""
              }}{{ scope.row.yearlyChange }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="targetCompletion" label="目标完成率" width="120">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.targetCompletion"
              :color="getTargetColor(scope.row.targetCompletion)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="riskLevel" label="风险等级" width="100">
          <template #default="scope">
            <el-tag :type="getRiskType(scope.row.riskLevel)">
              {{ scope.row.riskLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="200">
          <template #default="scope">
            <el-button size="small" @click="viewAreaDetails(scope.row)"
              >详情</el-button
            >
            <el-button
              size="small"
              type="warning"
              @click="optimizeArea(scope.row)"
              >优化</el-button
            >
            <el-button
              size="small"
              type="primary"
              @click="generateDetailReport(scope.row)"
              >报告</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[5, 10, 20, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";

// 查询表单数据
const queryForm = reactive({
  areas: ["punan", "shibei"],
  analysisType: "total",
  format: "detailed",
});

// 时间范围
const dateRange = ref([]);

// 图表类型选择
const totalCompareType = ref("month");
const trendMetric = ref("total");
const trendPeriod = ref("30d");

// 区域概览数据
const areaOverview = ref([
  {
    code: "punan",
    name: "浦南区域",
    status: "normal",
    totalEnergy: 15680.5,
    unitEnergy: 0.82,
    efficiency: 87.3,
    change: -2.5,
  },
  {
    code: "shibei",
    name: "史北区域",
    status: "warning",
    totalEnergy: 18920.8,
    unitEnergy: 1.15,
    efficiency: 81.2,
    change: 3.8,
  },
  {
    code: "east",
    name: "东部区域",
    status: "normal",
    totalEnergy: 12450.2,
    unitEnergy: 0.75,
    efficiency: 89.1,
    change: -1.2,
  },
  {
    code: "west",
    name: "西部区域",
    status: "normal",
    totalEnergy: 14280.6,
    unitEnergy: 0.88,
    efficiency: 85.7,
    change: -0.8,
  },
]);

// 能耗排行数据
const energyRanking = ref([
  { areaName: "史北区域", totalEnergy: 18920.8 },
  { areaName: "浦南区域", totalEnergy: 15680.5 },
  { areaName: "西部区域", totalEnergy: 14280.6 },
  { areaName: "东部区域", totalEnergy: 12450.2 },
]);

const efficiencyRanking = ref([
  { areaName: "东部区域", efficiency: 89.1 },
  { areaName: "浦南区域", efficiency: 87.3 },
  { areaName: "西部区域", efficiency: 85.7 },
  { areaName: "史北区域", efficiency: 81.2 },
]);

const savingRanking = ref([
  { areaName: "浦南区域", savingRate: 8.5 },
  { areaName: "东部区域", savingRate: 7.2 },
  { areaName: "西部区域", savingRate: 6.8 },
  { areaName: "史北区域", savingRate: 4.3 },
]);

// 区域详细数据
const areaDetailData = ref([
  {
    areaName: "浦南区域",
    stationCount: 28,
    totalEnergy: 15680.5,
    electricityEnergy: 12580.2,
    fuelEnergy: 3100.3,
    unitEnergy: 0.82,
    efficiency: 87,
    totalCost: 52.8,
    unitCost: 0.337,
    monthlyChange: -2.5,
    yearlyChange: -8.2,
    targetCompletion: 95,
    riskLevel: "低风险",
  },
  {
    areaName: "史北区域",
    stationCount: 17,
    totalEnergy: 18920.8,
    electricityEnergy: 15200.6,
    fuelEnergy: 3720.2,
    unitEnergy: 1.15,
    efficiency: 81,
    totalCost: 68.5,
    unitCost: 0.362,
    monthlyChange: 3.8,
    yearlyChange: 2.1,
    targetCompletion: 78,
    riskLevel: "中风险",
  },
]);

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 4,
});

// 获取排行类型
const getRankingType = (index) => {
  if (index === 0) return "danger";
  if (index === 1) return "warning";
  return "info";
};

const getRankingLabel = (index) => {
  if (index === 0) return "最高";
  if (index === 1) return "较高";
  return "一般";
};

// 获取能效颜色
const getEfficiencyColor = (efficiency) => {
  if (efficiency >= 85) return "#67c23a";
  if (efficiency >= 70) return "#e6a23c";
  return "#f56c6c";
};

// 获取目标完成率颜色
const getTargetColor = (completion) => {
  if (completion >= 90) return "#67c23a";
  if (completion >= 70) return "#e6a23c";
  return "#f56c6c";
};

// 获取风险等级类型
const getRiskType = (riskLevel) => {
  const typeMap = {
    低风险: "success",
    中风险: "warning",
    高风险: "danger",
  };
  return typeMap[riskLevel] || "info";
};

// 查询处理
const handleQuery = () => {
  ElMessage.success("查询成功");
};

// 重置查询
const resetQuery = () => {
  queryForm.areas = ["punan", "shibei"];
  queryForm.analysisType = "total";
  queryForm.format = "detailed";
  dateRange.value = [];
  ElMessage.info("已重置查询条件");
};

// 生成区域报表
const generateAreaReport = () => {
  ElMessage.success("区域报表生成中，请稍候...");
};

// 区域对比
const compareAreas = () => {
  ElMessage.info("区域对比分析功能开发中");
};

// 查看区域详情
const viewAreaDetails = (row) => {
  ElMessage.info(`查看 ${row.areaName} 的详细信息`);
};

// 优化区域
const optimizeArea = (row) => {
  ElMessage.success(`正在为 ${row.areaName} 生成优化建议`);
};

// 生成详细报告
const generateDetailReport = (row) => {
  ElMessage.success(`正在为 ${row.areaName} 生成详细报告`);
};

// 导出区域报表
const exportAreaReport = () => {
  ElMessage.success("区域报表导出成功");
};

// 设置能耗目标
const setEnergyTarget = () => {
  ElMessage.info("能耗目标设置功能开发中");
};

// 能耗预测
const energyForecast = () => {
  ElMessage.info("能耗预测分析功能开发中");
};

// 分页处理
const handleSizeChange = (size) => {
  pagination.pageSize = size;
};

const handleCurrentChange = (page) => {
  pagination.currentPage = page;
};

// 组件挂载
onMounted(() => {
  initCharts();
});

// 初始化图表
const initCharts = () => {
  console.log("初始化区域能耗分析图表");
};
</script>

<style scoped>
.area-energy-analysis {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.query-form {
  margin-bottom: 0;
}

.area-overview {
  margin-bottom: 20px;
}

.area-card {
  cursor: pointer;
  transition: all 0.3s;
  border-left: 4px solid;
}

.area-card.punan {
  border-left-color: #67c23a;
}

.area-card.shibei {
  border-left-color: #e6a23c;
}

.area-card.east {
  border-left-color: #409eff;
}

.area-card.west {
  border-left-color: #f56c6c;
}

.area-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.area-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.area-name {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.area-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-size: 14px;
  color: #606266;
}

.metric-value {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.metric-value.increase {
  color: #f56c6c;
}

.metric-value.decrease {
  color: #67c23a;
}

.comparison-charts {
  margin-bottom: 20px;
}

.chart-card {
  min-height: 450px;
}

.ranking-card {
  height: 400px;
  margin-bottom: 20px;
}

.ranking-list {
  padding: 10px 0;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-medal {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  border-radius: 50%;
  margin-right: 15px;
}

.rank-1 {
  background-color: #ffd700;
  color: #fff;
}

.rank-2 {
  background-color: #c0c0c0;
  color: #fff;
}

.rank-3 {
  background-color: #cd7f32;
  color: #fff;
}

.rank-4 {
  background-color: #f5f5f5;
  color: #666;
}

.ranking-content {
  flex: 1;
}

.ranking-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.ranking-value {
  font-size: 14px;
  color: #666;
}

.ranking-badge {
  margin-left: 10px;
}

.table-card {
  margin-bottom: 20px;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.increase {
  color: #f56c6c;
}

.decrease {
  color: #67c23a;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
