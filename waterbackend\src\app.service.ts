import { Injectable } from '@nestjs/common';
import { FileService } from 'src/file/file.service';

@Injectable()
export class AppService {
  constructor(private fileService: FileService) {}
  async uploadFile(file: Express.Multer.File): Promise<{ filePath: string }> {
    return this.fileService.uploadFile(file);
  }

  async deleteFile(filePath: string): Promise<{ success: boolean }> {
    return this.fileService.deleteFile(filePath);
  }
}
