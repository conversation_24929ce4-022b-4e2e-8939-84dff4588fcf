import {
  Controller,
  Get,
  Query,
  Post,
  Body,
  UseGuards,
  Delete,
  Request,
  Param,
  Put,
  Patch,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { UserService } from './user.service';
import { Md5 } from 'ts-md5/dist/md5';
import { UpdateUserDto } from './dto/update-user.dto';

import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/roles.decorator';
import { Role } from 'src/role.enum';
import { RolesGuard } from '../../roles.guard';
import { User } from './entities/user.entity';
import { JwtAuthGuard } from 'src/auth/auth.JwtAuthGuard';

@ApiTags('账户管理')
@Controller('user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  @UseGuards(RolesGuard)
  @Roles(Role.Admin, Role.SuperAdmin)
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '后台添加用户' })
  async create(@Body() user: User, @Request() req) {
    // 获取当前用户角色
    const currentUserRole = req.user.roles;

    // 角色值转换映射
    const roleMapping = {
      普通用户: Role.User,
      管理员: Role.Admin,
      超级管理员: Role.SuperAdmin,
    };

    // 如果传入的是中文角色名，转换为对应的枚举值
    if (roleMapping[user.roles]) {
      user.roles = roleMapping[user.roles];
    }

    // 验证权限:只有超级管理员可以创建管理员账号
    if (user.roles === Role.Admin || user.roles === Role.SuperAdmin) {
      if (currentUserRole !== Role.SuperAdmin) {
        return {
          code: 403,
          msg: '只有超级管理员可以创建管理员账号',
        };
      }
    }

    const saltOrRounds = '10';
    const pass = user.password + saltOrRounds;
    user.password = Md5.hashStr(pass);

    const createdUser = await this.userService.create(user);
    return {
      code: 0,
      data: createdUser,
      msg: '用户创建成功',
    };
  }

  @Get('page')
  @ApiOperation({
    summary:
      '分页显示用户列表,可带参数pageSize 每页多少条,pageNo 第几页,默认0页,10条',
  })
  @UseGuards(RolesGuard)
  @Roles(Role.Admin, Role.SuperAdmin)
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async findAll(
    @Query('pageNo') pageno,
    @Query('pageSize') pagesize,
    @Query('username') username,
    @Query('roles') roles,
    @Query('id') id,
  ) {
    const pageNo = pageno || 0;
    const pageSize = pagesize || 10;
    const result = await this.userService.findAll({
      pageNo,
      pageSize,
      username,
      roles,
      id,
    });
    return {
      code: 0,
      data: result,
      msg: '获取用户列表成功',
    };
  }

  @Get(':id/form')
  @ApiOperation({ summary: '根据ID获取用户信息' })
  @UseGuards(RolesGuard)
  @Roles(Role.Admin, Role.SuperAdmin)
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async findOne(@Param('id') id: string) {
    const user = await this.userService.findOne(id);
    if (user) {
      delete user.password; // 出于安全考虑，删除密码字段
      return {
        code: 0,
        data: user,
        msg: '获取用户信息成功',
      };
    }
    return {
      code: 404,
      msg: '用户不存在',
    };
  }

  @Get('options')
  @ApiOperation({ summary: '获取角色选项' })
  @UseGuards(RolesGuard)
  @Roles(Role.Admin, Role.SuperAdmin)
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  getRoleOptions(@Request() req) {
    // 根据当前用户角色返回可选的角色列表
    const currentUserRole = req.user.roles;

    const baseOptions = [
      {
        value: Role.User,
        label: '普通用户',
      },
    ];

    // 只有超级管理员可以看到所有角色选项
    if (
      Array.isArray(currentUserRole)
        ? currentUserRole.includes(Role.SuperAdmin)
        : currentUserRole === Role.SuperAdmin
    ) {
      baseOptions.push(
        {
          value: Role.Admin,
          label: '管理员',
        },
        {
          value: Role.SuperAdmin,
          label: '超级管理员',
        },
      );
    }

    return {
      code: 0,
      data: baseOptions,
      msg: 'ok',
    };
  }

  @UseGuards(RolesGuard)
  @Roles(Role.Admin, Role.SuperAdmin)
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '更新用户信息' })
  @Patch(':id')
  update(@Param('id') id: string, @Body() updateUserDto) {
    const userInfo = this.userService.update(id, updateUserDto);
    return { code: 0, data: userInfo, msg: 'ok' };
  }

  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '更新当前用户信息,如修改密码' })
  @Put('update')
  selfupdate(@Request() req, @Body() updateUserDto) {
    const id = req.user.userId;
    console.log('id', id);
    console.log('updateUserDto', updateUserDto);
    if (updateUserDto.username) delete updateUserDto.username; //不能更新用户名
    if (updateUserDto.roles) delete updateUserDto.roles; //不能更新权限
    const userInfo = this.userService.update(id, updateUserDto);
    return { code: 0, data: userInfo, msg: 'ok' };
  }

  @ApiOperation({ summary: '删除某条人员' })
  @Delete(':id')
  @UseGuards(RolesGuard)
  @Roles(Role.Admin, Role.SuperAdmin)
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  remove(@Param('id') id: string) {
    const userInfo = this.userService.delete(id);
    return { code: 0, data: userInfo, msg: 'ok' };
  }

  @Get('count')
  @ApiOperation({ summary: '显示用户数量' })
  count() {
    return this.userService.count();
  }

  @UseGuards(RolesGuard)
  @Roles(Role.Admin, Role.SuperAdmin)
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'put更新用户信息' })
  @Put(':id')
  async putupdate(@Param('id') id: string, @Body() updateUserDto) {
    const userInfo = await this.userService.update(id, updateUserDto);
    return { code: 0, data: userInfo, msg: 'ok' };
  }

  @Get('cancellation')
  @ApiOperation({ summary: '注销用户,带上验证头请求后从数据库真删除本用户' })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async cancellation(@Request() req) {
    // console.log(req.user);
    const id = req.user.userId;
    return this.userService.cancellation(id);
  }

  @Post('change-password')
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '修改用户自己的密码' })
  async changePassword(
    @Request() req,
    @Body() passwordData: { oldPassword: string; newPassword: string },
  ) {
    const userId = req.user.userId;

    // 验证旧密码
    const saltOrRounds = '10';
    const oldPasswordHash = Md5.hashStr(
      passwordData.oldPassword + saltOrRounds,
    );
    const user = await this.userService.findOne(userId);

    if (!user || user.password !== oldPasswordHash) {
      throw new HttpException('旧密码不正确', HttpStatus.BAD_REQUEST);
    }

    // 加密新密码
    const newPasswordHash = Md5.hashStr(
      passwordData.newPassword + saltOrRounds,
    );

    // 更新密码
    await this.userService.update(userId, {
      password: newPasswordHash,
    } as UpdateUserDto);

    return {
      code: 0,
      msg: '密码修改成功',
    };
  }
}
