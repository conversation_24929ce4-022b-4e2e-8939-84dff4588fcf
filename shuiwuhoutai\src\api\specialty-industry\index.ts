import request from "@/utils/request";
import {
  AgriculturalProductQuery,
  AgriculturalProductPageVO,
  AgriculturalProductForm,
  AttractionQuery,
  AttractionPageVO,
  AttractionForm,
  TiktokLinkQuery,
  TiktokLinkPageVO,
  TiktokLinkForm,
  LiveroomQuery,
  LiveroomPageVO,
  LiveroomForm,
  LivemomentQuery,
  LivemomentPageVO,
  LivemomentForm,
} from "./model";

class AgriculturalProductAPI {
  // 获取特色农产品分页列表
  static getAgriculturalProduct(queryParams: AgriculturalProductQuery) {
    return request<any, PageResult<AgriculturalProductPageVO[]>>({
      url: "/agriculturalproduct",
      method: "get",
      params: queryParams,
    });
  }

  // 获取特色农产品表单详情
  static getAgriculturalProductFormData(id: number) {
    return request<any, AgriculturalProductForm>({
      url: "/agriculturalproduct/" + id,
      method: "get",
    });
  }

  // 添加特色农产品
  static addAgriculturalProduct(data: AgriculturalProductForm) {
    return request({
      url: "/agriculturalproduct",
      method: "post",
      data: data,
    });
  }

  // 修改特色农产品
  static updateAgriculturalProduct(id: number, data: AgriculturalProductForm) {
    return request({
      url: "/agriculturalproduct/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除特色农产品
  static deleteAgriculturalProductByIds(ids: string) {
    return request({
      url: "/agriculturalproduct/" + ids,
      method: "delete",
    });
  }
}

class AttractionAPI {
  // 获取特色农文旅景点分页列表
  static getAttraction(queryParams: AttractionQuery) {
    return request<any, PageResult<AttractionPageVO[]>>({
      url: "/attraction",
      method: "get",
      params: queryParams,
    });
  }

  // 获取特色农文旅景点表单详情
  static getAttractionFormData(id: number) {
    return request<any, AttractionForm>({
      url: "/attraction/" + id,
      method: "get",
    });
  }

  // 添加特色农文旅景点
  static addAttraction(data: AttractionForm) {
    return request({
      url: "/attraction",
      method: "post",
      data: data,
    });
  }

  // 修改特色农文旅景点
  static updateAttraction(id: number, data: AttractionForm) {
    return request({
      url: "/attraction/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除特色农文旅景点
  static deleteAttractionByIds(ids: string) {
    return request({
      url: "/attraction/" + ids,
      method: "delete",
    });
  }

  // 导入特色农文旅景点
  static importAttraction(file: FormData) {
    return request({
      url: "/attraction/import",
      method: "post",
      data: file,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }
}

class TiktokLinkAPI {
  // 获取抖音链接分页列表
  static getTiktokLink(queryParams: TiktokLinkQuery) {
    return request<any, PageResult<TiktokLinkPageVO[]>>({
      url: "/tiktoklink",
      method: "get",
      params: queryParams,
    });
  }

  // 获取抖音链接表单详情
  static getTiktokLinkFormData(id: number) {
    return request<any, TiktokLinkForm>({
      url: "/tiktoklink/" + id,
      method: "get",
    });
  }

  // 添加抖音链接
  static addTiktokLink(data: TiktokLinkForm) {
    return request({
      url: "/tiktoklink",
      method: "post",
      data: data,
    });
  }

  // 修改抖音链接
  static updateTiktokLink(id: number, data: TiktokLinkForm) {
    return request({
      url: "/tiktoklink/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除抖音链接
  static deleteTiktokLinkByIds(ids: string) {
    return request({
      url: "/tiktoklink/" + ids,
      method: "delete",
    });
  }
}

class LiveroomAPI {
  // 获取直播间场景分页列表
  static getLiveroom(queryParams: LiveroomQuery) {
    return request<any, PageResult<LiveroomPageVO[]>>({
      url: "/liveroom",
      method: "get",
      params: queryParams,
    });
  }

  // 获取直播间场景表单详情
  static getLiveroomFormData(id: number) {
    return request<any, LiveroomForm>({
      url: "/liveroom/" + id,
      method: "get",
    });
  }

  // 添加直播间场景
  static addLiveroom(data: LiveroomForm) {
    return request({
      url: "/liveroom",
      method: "post",
      data: data,
    });
  }

  // 修改直播间场景
  static updateLiveroom(id: number, data: LiveroomForm) {
    return request({
      url: "/liveroom/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除直播间场景
  static deleteLiveroomByIds(ids: string) {
    return request({
      url: "/liveroom/" + ids,
      method: "delete",
    });
  }
}

class LivemomentAPI {
  // 获取抖音精彩直播瞬间分页列表
  static getLivemoment(queryParams: LivemomentQuery) {
    return request<any, PageResult<LivemomentPageVO[]>>({
      url: "/livemoment",
      method: "get",
      params: queryParams,
    });
  }

  // 获取抖音精彩直播瞬间表单详情
  static getLivemomentFormData(id: number) {
    return request<any, LivemomentForm>({
      url: "/livemoment/" + id,
      method: "get",
    });
  }

  // 添加抖音精彩直播瞬间
  static addLivemoment(data: LivemomentForm) {
    return request({
      url: "/livemoment",
      method: "post",
      data: data,
    });
  }

  // 修改抖音精彩直播瞬间
  static updateLivemoment(id: number, data: LivemomentForm) {
    return request({
      url: "/livemoment/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除抖音精彩直播瞬间
  static deleteLivemomentByIds(ids: string) {
    return request({
      url: "/livemoment/" + ids,
      method: "delete",
    });
  }
}

export default AgriculturalProductAPI;
export { AttractionAPI, TiktokLinkAPI, LiveroomAPI, LivemomentAPI };
