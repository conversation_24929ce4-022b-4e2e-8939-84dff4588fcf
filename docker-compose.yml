# /f:/水利站/backend/docker-compose.yml
name: water_station

services:
  # MongoDB服务 - 用于存储5秒级实时数据
  mongodb:
    image: dockerproxy.com/library/mongo:7.0
    container_name: water_station_mongodb
    restart: always
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: admin123
      MONGO_INITDB_DATABASE: water_station_realtime
    ports:
      - "27017:27017"
    volumes:
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    tmpfs:
      - /data/db:noexec,nosuid,size=10g
      - /data/configdb:noexec,nosuid,size=100m
    networks:
      - water_station_network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MySQL服务 - 用于存储分钟级和小时级聚合数据
  mysql:
    image: dockerproxy.com/library/mysql:8.0
    container_name: water_station_mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: water_station_config
      MYSQL_USER: water_user
      MYSQL_PASSWORD: water123
    ports:
      - "3311:3306"
    volumes:
      - ./data/mysql_data:/var/lib/mysql
      - ./data/init-mysql.sql:/docker-entrypoint-initdb.d/init-mysql.sql:ro
      - ./data/mysql-conf:/etc/mysql/conf.d
    networks:
      - water_station_network
    command: >
      --default-authentication-plugin=mysql_native_password
      --innodb-buffer-pool-size=256M
      --max-connections=500
      --slow-query-log=1
      --slow-query-log-file=/var/lib/mysql/mysql-slow.log
      --long-query-time=2
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-proot123"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # RabbitMQ消息队列服务 - 用于设备数据消息队列处理
  rabbitmq:
    image: rabbitmq:3.12-management
    container_name: water_station_rabbitmq
    restart: always
    environment:
      RABBITMQ_DEFAULT_USER: water_user
      RABBITMQ_DEFAULT_PASS: water123
      RABBITMQ_DEFAULT_VHOST: /
    ports:
      - "5672:5672"   # AMQP端口
      - "15672:15672" # 管理界面端口
    networks:
      - water_station_network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "check_port_connectivity"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # 水利站后端服务
  # water_station_backend:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #   container_name: water_station_backend
  #   restart: always
  #   ports:
  #     - "8500:8500"  # API服务端口
  #     - "8889:8889"  # TCP服务端口
  #   environment:
  #     # MongoDB配置
  #     MONGODB_HOST: mongodb
  #     MONGODB_PORT: 27017
  #     MONGODB_DATABASE: water_station_realtime
  #     MONGODB_USERNAME: admin
  #     MONGODB_PASSWORD: admin123
  #     MONGODB_CONNECTION_STRING: ******************************************************************************
      
  #     # MySQL配置
  #     MYSQL_HOST: mysql
  #     MYSQL_PORT: 3306
  #     MYSQL_DATABASE: water_station
  #     MYSQL_USERNAME: water_user
  #     MYSQL_PASSWORD: water123
  #     MYSQL_CONNECTION_STRING: mysql+pymysql://water_user:water123@mysql:3306/water_station
      
  #     # Redis配置（可选）
  #     REDIS_HOST: redis
  #     REDIS_PORT: 6379
  #     REDIS_PASSWORD: redis123
      
  #     # 系统配置
  #     LOG_LEVEL: INFO
  #     TCP_HOST: 0.0.0.0
  #     TCP_PORT: 8889
  #     API_HOST: 0.0.0.0
  #     API_PORT: 8500
  #   volumes:
  #     - ./logs:/app/logs
  #     - ./data:/app/data
  #   networks:
  #     - water_station_network
  #   depends_on:
  #     mongodb:
  #       condition: service_healthy
  #     mysql:
  #       condition: service_healthy
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:8500/health"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 60s




networks:
  water_station_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
