<template>
  <div class="equipment-runtime">
    <el-card shadow="hover" class="page-header">
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="设备类型">
          <el-select
            v-model="searchForm.equipmentType"
            placeholder="请选择设备类型"
            clearable
          >
            <el-option label="水泵" value="pump" />
            <el-option label="电机" value="motor" />
            <el-option label="变频器" value="inverter" />
            <el-option label="控制器" value="controller" />
          </el-select>
        </el-form-item>

        <el-form-item label="站点选择">
          <el-select
            v-model="searchForm.stationId"
            placeholder="请选择站点"
            clearable
          >
            <el-option label="浦南一号泵站" value="pn001" />
            <el-option label="浦南二号泵站" value="pn002" />
            <el-option label="史北一号泵站" value="sb001" />
            <el-option label="史北二号泵站" value="sb002" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="success" @click="exportData">数据导出</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计概览卡片 -->
    <div class="statistics-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ statistics.totalEquipment }}</div>
              <div class="stat-label">设备总数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ statistics.avgRuntime }}</div>
              <div class="stat-label">平均运行时长(h)</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ statistics.maxRuntime }}</div>
              <div class="stat-label">最长运行时长(h)</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ statistics.utilizationRate }}%</div>
              <div class="stat-label">设备利用率</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表展示区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>设备运行时长趋势图</span>
              </div>
            </template>
            <div id="runtimeTrendChart" style="height: 400px"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>设备类型运行时长分布</span>
              </div>
            </template>
            <div id="equipmentTypeChart" style="height: 400px"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>站点设备运行对比</span>
              </div>
            </template>
            <div id="stationCompareChart" style="height: 400px"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细数据表格 -->
    <el-card shadow="hover" class="table-card">
      <template #header>
        <div class="card-header">
          <span>设备运行时长详细数据</span>
        </div>
      </template>

      <el-table :data="tableData" stripe style="width: 100%">
        <el-table-column prop="equipmentName" label="设备名称" width="150" />
        <el-table-column prop="equipmentType" label="设备类型" width="100" />
        <el-table-column prop="stationName" label="所属站点" width="120" />
        <el-table-column
          prop="dailyRuntime"
          label="日运行时长(h)"
          width="120"
        />
        <el-table-column
          prop="weeklyRuntime"
          label="周运行时长(h)"
          width="120"
        />
        <el-table-column
          prop="monthlyRuntime"
          label="月运行时长(h)"
          width="120"
        />
        <el-table-column prop="utilizationRate" label="利用率(%)" width="100" />
        <el-table-column prop="status" label="运行状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastMaintenance" label="最后维护时间" />
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";

// 搜索表单数据
const searchForm = reactive({
  equipmentType: "",
  stationId: "",
});

// 时间范围
const dateRange = ref([]);

// 统计数据
const statistics = reactive({
  totalEquipment: 156,
  avgRuntime: 16.8,
  maxRuntime: 23.5,
  utilizationRate: 78.3,
});

// 表格数据
const tableData = ref([
  {
    equipmentName: "1#水泵",
    equipmentType: "水泵",
    stationName: "浦南一号泵站",
    dailyRuntime: 18.5,
    weeklyRuntime: 126.8,
    monthlyRuntime: 510.2,
    utilizationRate: 89.2,
    status: "正常运行",
    lastMaintenance: "2024-01-15 09:00:00",
  },
  {
    equipmentName: "2#电机",
    equipmentType: "电机",
    stationName: "史北二号泵站",
    dailyRuntime: 14.2,
    weeklyRuntime: 98.6,
    monthlyRuntime: 422.8,
    utilizationRate: 76.5,
    status: "维护中",
    lastMaintenance: "2024-01-18 14:30:00",
  },
]);

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 156,
});

// 获取状态标签类型
const getStatusType = (status) => {
  const typeMap = {
    正常运行: "success",
    维护中: "warning",
    故障: "danger",
    停机: "info",
  };
  return typeMap[status] || "info";
};

// 搜索处理
const handleSearch = () => {
  ElMessage.success("查询成功");
  // 这里添加实际的查询逻辑
};

// 重置搜索
const resetSearch = () => {
  searchForm.equipmentType = "";
  searchForm.stationId = "";
  dateRange.value = [];
  ElMessage.info("已重置搜索条件");
};

// 数据导出
const exportData = () => {
  ElMessage.success("数据导出成功");
  // 这里添加实际的导出逻辑
};

// 分页处理
const handleSizeChange = (size) => {
  pagination.pageSize = size;
  // 重新加载数据
};

const handleCurrentChange = (page) => {
  pagination.currentPage = page;
  // 重新加载数据
};

// 组件挂载
onMounted(() => {
  // 初始化图表
  initCharts();
});

// 初始化图表
const initCharts = () => {
  // 这里可以添加ECharts初始化代码
  console.log("初始化设备运行时长图表");
};
</script>

<style scoped>
.equipment-runtime {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.search-form {
  margin-bottom: 0;
}

.statistics-cards {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-content {
  padding: 10px 0;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-card {
  height: 500px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
