import { ApiProperty } from '@nestjs/swagger';
export class CreateLoginlogDto {
  @ApiProperty({ description: '登录ip地址', example: '127.0.0.1' })
  ip: string;
  @ApiProperty({ description: '登录时间', example: '11665655555' })
  addtime: number;
  @ApiProperty({ description: '用户名', example: 'admin' })
  username: string;
  @ApiProperty({ description: '用户id', example: '3rediofoefds98i3r3' })
  userid: string;
}
