<!-- 修改密码 -->
<template>
  <div class="app-container bg-gray-50 min-h-[calc(100vh-50px)]">
    <div class="max-w-[600px] mx-auto pt-[100px]">
      <el-card shadow="hover" class="password-card">
        <template #header>
          <div class="card-header flex items-center gap-2 py-2">
            <el-icon class="text-primary"><Lock /></el-icon>
            <span class="text-lg font-medium">修改密码</span>
          </div>
        </template>

        <div class="tips mb-6 px-1">
          <el-alert type="info" :closable="false" show-icon>
            <template #title>
              <span class="text-gray-600"
                >为了保证账号安全，请设置至少6位字符的密码</span
              >
            </template>
          </el-alert>
        </div>

        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width="120px"
          class="password-form"
        >
          <el-form-item label="旧密码" prop="oldPassword" class="mb-6">
            <el-input
              v-model="formData.oldPassword"
              type="password"
              placeholder="请输入旧密码"
              show-password
              :prefix-icon="Key"
            />
          </el-form-item>
          <el-form-item label="新密码" prop="password" class="mb-6">
            <el-input
              v-model="formData.password"
              type="password"
              placeholder="请输入新密码"
              show-password
              :prefix-icon="Key"
            />
          </el-form-item>
          <el-form-item label="确认新密码" prop="confirmPassword" class="mb-8">
            <el-input
              v-model="formData.confirmPassword"
              type="password"
              placeholder="请再次输入新密码"
              show-password
              :prefix-icon="Key"
            />
          </el-form-item>

          <el-form-item class="flex justify-center mb-0">
            <el-button type="primary" @click="handleSubmit" class="w-[120px]">
              <template #icon><Check /></template>
              确定
            </el-button>
            <el-button @click="resetForm" class="w-[120px]">
              <template #icon><Refresh /></template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "EditPassword",
  inheritAttrs: false,
});

import UserAPI from "@/api/common/user";
import { useUserStore } from "@/store/modules/user";
import { Lock, Key, Check, Refresh } from "@element-plus/icons-vue";

const userStore = useUserStore();
const formRef = ref(ElForm);

// 表单数据
const formData = reactive({
  oldPassword: "",
  password: "",
  confirmPassword: "",
});

// 校验两次密码是否一致
const validateConfirmPassword = (rule: any, value: string, callback: any) => {
  if (value === "") {
    callback(new Error("请再次输入密码"));
  } else if (value !== formData.password) {
    callback(new Error("两次输入密码不一致"));
  } else {
    callback();
  }
};

// 校验规则
const rules = reactive({
  oldPassword: [{ required: true, message: "请输入旧密码", trigger: "blur" }],
  password: [
    { required: true, message: "请输入新密码", trigger: "blur" },
    { min: 6, message: "密码长度不能小于6位", trigger: "blur" },
  ],
  confirmPassword: [
    { required: true, validator: validateConfirmPassword, trigger: "blur" },
  ],
});

/** 提交表单 */
const handleSubmit = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      UserAPI.changePassword(formData.oldPassword, formData.password)
        .then(() => {
          ElMessage.success("密码修改成功");
          resetForm();
        })
        .catch((error) => {
          ElMessage.error(`修改密码失败: ${error.message}`);
        });
    }
  });
};

/** 重置表单 */
const resetForm = () => {
  formRef.value.resetFields();
};
</script>

<style scoped>
.password-card {
  border-radius: 8px;
  transition: all 0.3s;
}

.password-card :deep(.el-card__header) {
  border-bottom: 1px solid #f0f0f0;
}

.password-form :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6;
}

.password-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-color-primary);
}

.password-form :deep(.el-button) {
  padding: 12px 20px;
  border-radius: 4px;
}
</style>
