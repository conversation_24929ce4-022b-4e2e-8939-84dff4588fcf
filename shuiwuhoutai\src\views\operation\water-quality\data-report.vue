<template>
  <div class="water-quality-data-report">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <el-button type="primary" @click="handleNewReport"
            >新建填报</el-button
          >
        </div>
      </template>

      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="监测点">
          <el-select
            v-model="searchForm.monitorPoint"
            placeholder="请选择监测点"
            clearable
          >
            <el-option label="1号水厂出厂水" value="plant1_outlet" />
            <el-option label="2号水厂出厂水" value="plant2_outlet" />
            <el-option label="市区管网末梢水" value="network_end" />
          </el-select>
        </el-form-item>
        <el-form-item label="填报状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="草稿" value="draft" />
            <el-option label="已提交" value="submitted" />
            <el-option label="已审核" value="approved" />
          </el-select>
        </el-form-item>
        <el-form-item label="填报时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="reportData" stripe>
        <el-table-column prop="reportNo" label="报告编号" width="120" />
        <el-table-column prop="monitorPoint" label="监测点" />
        <el-table-column prop="sampleDate" label="采样日期" width="120" />
        <el-table-column prop="testItems" label="检测项目数" width="120" />
        <el-table-column prop="reporter" label="填报人" width="100" />
        <el-table-column prop="reportTime" label="填报时间" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">{{
              getStatusText(row.status)
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)"
              >查看</el-button
            >
            <el-button type="success" size="small" @click="handleEdit(row)"
              >编辑</el-button
            >
            <el-button type="warning" size="small" @click="handleSubmit(row)"
              >提交</el-button
            >
            <el-button type="danger" size="small" @click="handleDelete(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";

const searchForm = reactive({
  monitorPoint: "",
  status: "",
  dateRange: [],
});

const reportData = ref([
  {
    id: 1,
    reportNo: "WR202401001",
    monitorPoint: "1号水厂出厂水",
    sampleDate: "2024-01-01",
    testItems: 12,
    reporter: "张三",
    reportTime: "2024-01-01 16:30",
    status: "submitted",
  },
]);

const getStatusType = (status: string): "success" | "primary" | "warning" | "info" | "danger" => {
  const types: Record<string, "success" | "primary" | "warning" | "info" | "danger"> = { 
    draft: "info", 
    submitted: "warning", 
    approved: "success" 
  };
  return types[status] || "info";
};

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    draft: "草稿",
    submitted: "已提交",
    approved: "已审核",
  };
  return texts[status] || "未知";
};

const handleNewReport = () => console.log("新建填报");
const handleSearch = () => console.log("搜索", searchForm);
const handleReset = () =>
  Object.assign(searchForm, { monitorPoint: "", status: "", dateRange: [] });
const handleView = (row: any) => console.log("查看", row);
const handleEdit = (row: any) => console.log("编辑", row);
const handleSubmit = (row: any) => console.log("提交", row);
const handleDelete = (row: any) => console.log("删除", row);

onMounted(() => {
  // 初始化
});
</script>

<style scoped>
.water-quality-data-report {
  padding: 20px;
}

.page-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}
</style>
