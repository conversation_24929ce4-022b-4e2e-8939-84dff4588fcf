<!-- 账号管理 -->
<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 用户列表 -->
      <el-col :lg="24" :xs="24">
        <div class="search-container">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="关键字" prop="keywords">
              <el-input
                v-model="queryParams.keywords"
                placeholder="用户名"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
              />
            </el-form-item>

            <el-form-item label="创建时间">
              <el-date-picker
                class="!w-[240px]"
                v-model="dateTimeRange"
                type="daterange"
                range-separator="~"
                start-placeholder="开始时间"
                end-placeholder="截止时间"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="handleQuery"
                ><i-ep-search />搜索</el-button
              >
              <el-button @click="handleResetQuery">
                <i-ep-refresh />
                重置</el-button
              >
            </el-form-item>
          </el-form>
        </div>

        <el-card shadow="never" class="table-container">
          <template #header>
            <div class="flex justify-between">
              <div>
                <el-button
                  type="success"
                  v-if="hasPermission(['admin:add'])"
                  @click="openDialog('user-form')"
                  ><i-ep-plus />新增</el-button
                >
                <el-button
                  type="danger"
                  v-if="hasPermission(['admin:delete'])"
                  :disabled="removeIds.length === 0"
                  @click="handleDelete()"
                  ><i-ep-delete />删除</el-button
                >
              </div>
            </div>
          </template>

          <el-table
            border
            v-loading="loading"
            :data="pageData"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column
              key="username"
              label="用户名"
              align="center"
              prop="username"
              fit
            />
            <el-table-column label="角色" align="center" prop="roles" fit />
            <el-table-column
              label="最后登录时间"
              align="center"
              prop="lastLoginTimeFormat"
              fit
            />
            <el-table-column label="操作" align="center" fixed="right">
              <template #default="scope">
                <el-button
                  type="primary"
                  link
                  size="default"
                  v-if="hasPermission(['admin:edit'])"
                  @click="openDialog('user-form', scope.row.id)"
                  ><i-ep-edit />编辑</el-button
                >
                <el-button
                  type="primary"
                  link
                  size="default"
                  v-if="hasPermission(['admin:edit'])"
                  @click="handleResetPassword(scope.row.id)"
                  ><i-ep-key />重置密码</el-button
                >
                <el-button
                  type="primary"
                  link
                  size="default"
                  v-if="hasPermission(['admin:delete'])"
                  @click="handleDelete(scope.row.id)"
                  ><i-ep-delete />删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.pageNo"
            v-model:limit="queryParams.pageSize"
            @pagination="handleQuery"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 弹窗 -->
    <el-dialog
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <!-- 用户新增/编辑表单 -->
      <el-form
        v-if="dialog.type === 'user-form'"
        ref="userFormRef"
        :model="formData"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="formData.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="formData.password" placeholder="请输入密码" />
        </el-form-item>
        <el-form-item label="角色" prop="roles">
          <el-select v-model="formData.roles" placeholder="请选择">
            <el-option
              v-for="item in roleList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="权限设置" prop="permissions">
          <el-tree-select
            v-model="formData.permissions"
            :data="routeOptions"
            show-checkbox
            multiple
            node-key="value"
            :props="{
              label: 'title',
              children: 'children',
              disabled: (data: RouteOption) =>
                data.children &&
                data.children.length > 0 &&
                !data.children.some((child) => child.value.endsWith(':view')),
            }"
            :render-after-expand="false"
            :default-expanded-keys="expandedKeys"
            @expand-change="handleExpandChange"
            placeholder="请选择权限"
            class="w-full"
            @change="handlePermissionChange"
            collapse-tags
            collapse-tags-tooltip
          />
          <div class="mt-4">
            <el-collapse v-if="groupedPermissionsByModule.size > 0">
              <el-collapse-item>
                <template #title>
                  <div class="flex items-center">
                    <el-icon class="mr-2"><Document /></el-icon>
                    <span>已选择的权限</span>
                    <el-tag class="ml-2" size="small" type="info" round>
                      {{
                        Array.from(groupedPermissionsByModule.keys()).length
                      }}个模块
                    </el-tag>
                  </div>
                </template>
                <div class="permission-container">
                  <div class="permission-groups">
                    <div
                      v-for="[module, perms] of groupedPermissionsByModule"
                      :key="module"
                      class="permission-group"
                    >
                      <div class="permission-module">
                        <el-icon class="mr-1"><Folder /></el-icon>
                        {{ module }}
                      </div>
                      <div class="permission-items">
                        <el-tag
                          v-for="perm in perms"
                          :key="perm"
                          size="small"
                          :class="getTagClass(perm)"
                          effect="light"
                        >
                          {{ formatPermission(perm) }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-form-item>
      </el-form>
      <!-- 弹窗底部操作按钮 -->
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
          <el-button @click="closeDialog">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { hasAuth } from "@/plugins/permission";

defineOptions({
  name: "Admin",
  inheritAttrs: false,
});

import UserAPI from "@/api/common/user";
import RoleAPI from "@/api/common/role";
import moment from "moment";
import { UserForm, UserQuery, UserPageVO } from "@/api/common/user/model";
import { constantRoutes } from "@/router";
import { RouteRecordRaw } from "vue-router";
import type { FormInstance } from "element-plus";

// 添加 RouteMeta 类型定义
interface CustomRouteMeta {
  title?: string;
  permissions?: string[];
  hidden?: boolean;
}

declare module "vue-router" {
  interface RouteMeta extends CustomRouteMeta {}
}

const queryFormRef = ref(ElForm); // 查询表单
const userFormRef = ref<FormInstance>(); // 用户表单

const loading = ref(false); //  加载状态
const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<UserQuery>({
  pageNo: 1,
  pageSize: 20,
});
const dateTimeRange = ref("");
const total = ref(0); // 数据总数
const pageData = ref<UserPageVO[]>(); // 用户分页数据
const roleList = ref<Array<{ label: string; value: string | number }>>(); // 角色下拉数据源

// 在 script setup 部分添加类型定义
interface RouteOption {
  title: string;
  value: string;
  children?: RouteOption[];
}

// 路由选项数据
const routeOptions = computed(() => {
  const formatRoutes = (routes: RouteRecordRaw[]): RouteOption[] => {
    return routes
      .filter((route) => {
        // 基础过滤：过滤掉隐藏路由、根路由和账号管理模块
        if (
          route.meta?.hidden ||
          route.path === "/" ||
          route.path === "/admin"
        ) {
          return false;
        }
        return true;
      })
      .map((route) => {
        const tmpRoute = { ...route };
        const option: RouteOption = {
          title: (tmpRoute.meta?.title as string) || (tmpRoute.name as string),
          value: tmpRoute.path,
        };

        if (tmpRoute.children) {
          // 处理子路由
          const children = formatRoutes(tmpRoute.children);
          if (children.length > 0) {
            option.children = children;
          }
        }

        // 如果是功能页面（没有子路由或子路由为空），添加操作权限节点
        if (!option.children) {
          // 获取权限前缀
          const permPrefix = tmpRoute.path
            ? (tmpRoute.path as string).split("/").pop() || tmpRoute.path
            : (tmpRoute.meta?.parentPath as string)?.split("/").pop() || "";

          // 基础权限节点
          const basePermissions: RouteOption[] = [
            { title: "访问页面", value: `${permPrefix}:view` },
            { title: "新增", value: `${permPrefix}:add` },
            { title: "编辑", value: `${permPrefix}:edit` },
            { title: "删除", value: `${permPrefix}:delete` },
          ];

          // 需要导入导出权限的页面
          const pagesNeedImportExport = [
            "member", // 党员信息管理
            "tenant", // 出租户管理
            "shop", // 沿街店铺管理
            "info", // 人房信息管理
          ];

          // 如果当前页面需要导入导出权限，添加相应的权限节点
          if (pagesNeedImportExport.includes(permPrefix)) {
            basePermissions.push(
              { title: "导入", value: `${permPrefix}:import` },
              { title: "导出", value: `${permPrefix}:export` }
            );
          }

          option.children = basePermissions;
        }

        return option;
      })
      .filter((option) => option.children && option.children.length > 0); // 过滤掉没有子节点的路由
  };

  return formatRoutes(constantRoutes);
});

watch(dateTimeRange, (newVal) => {
  if (newVal) {
    queryParams.startTime = newVal[0];
    queryParams.endTime = newVal[1];
  }
});

// 在 script setup 部分
const expandedKeys = ref<string[]>([
  "/admin",
  "/party",
  "/governance",
  "/industry",
  "/humanroom",
  "/service",
]);

// 处理节点展开/收起
const handleExpandChange = (keys: string[]) => {
  expandedKeys.value = keys;
};

// 弹窗对象
const dialog = reactive({
  visible: false,
  type: "user-form",
  width: 1000, // 增加弹窗宽度
  title: "",
});

// 用户表单数据
const formData = ref<UserForm>({
  username: "",
  password: "",
  roles: "",
  permissions: [], // 所有权限
});

// 校验规则
const rules = reactive({
  username: [
    {
      required: true,
      message: "用户名不能为空",
      trigger: "blur",
    },
  ],
  password: computed(() => {
    // 编辑时不校验密码，新增时校验密码
    return formData.value.id
      ? [
          {
            min: 6,
            message: "密码不能少于6位",
            trigger: "blur",
          },
        ]
      : [
          {
            required: true,
            message: "密码不能为空",
            trigger: "blur",
          },
          {
            min: 6,
            message: "密码不能少于6位",
            trigger: "blur",
          },
        ];
  }),
  roles: [
    {
      required: true,
      message: "请选择用户角色",
      trigger: ["blur", "change"],
    },
  ],
});

// 权限判断方法
const hasPermission = (permissions: string[]) => {
  return hasAuth(permissions);
};

/** 查询 */
function handleQuery() {
  loading.value = true;
  UserAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.list.map((item) => {
        return {
          ...item,
          lastLoginTimeFormat: moment(item.lastLoginTime).format(
            "YYYY-MM-DD HH:mm:ss"
          ),
        };
      });
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置查询 */
function handleResetQuery() {
  dateTimeRange.value = "";
  queryParams.pageNo = 1;
  queryParams.startTime = undefined;
  queryParams.endTime = undefined;
  handleQuery();
}

/** 行选中 */
function handleSelectionChange(selection: any) {
  removeIds.value = selection.map((item: any) => item.id);
}

/** 加载角色下拉数据源 */
async function loadRoleOptions() {
  RoleAPI.getOptions().then((data) => {
    roleList.value = data
      .filter((item) => item.value !== undefined)
      .map((item) => {
        return {
          label: item.label || "",
          value: item.value || "",
        };
      });
  });
}

/**
 * 打开弹窗
 *
 * @param type 弹窗类型  用户表单：user-form | 用户导入：user-import
 * @param id 用户ID
 */
async function openDialog(type: string, id?: number) {
  // 先重置表单数据和验证状态
  formData.value = {
    username: "",
    password: "",
    roles: "",
    permissions: [],
  };
  nextTick(() => {
    userFormRef.value?.clearValidate();
  });

  dialog.visible = true;
  dialog.type = type;
  if (dialog.type === "user-form") {
    await loadRoleOptions();
    if (id) {
      dialog.title = "修改用户";
      loading.value = true;
      try {
        const data = await UserAPI.getFormData(id);
        formData.value = {
          ...data,
          permissions: data.permissions || [],
        };
      } catch (error) {
        ElMessage.error("获取用户信息失败");
      } finally {
        loading.value = false;
      }
    } else {
      dialog.title = "新增用户";
    }
  }
}

/**
 * 关闭弹窗
 */
function closeDialog() {
  dialog.visible = false;
  if (userFormRef.value) {
    userFormRef.value.resetFields();
    userFormRef.value.clearValidate();
  }
}

/** 表单提交 */
const handleSubmit = useThrottleFn(() => {
  if (!userFormRef.value) return;

  userFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      loading.value = true;
      try {
        const submitData = {
          ...formData.value,
          permissions: formData.value.permissions || [],
        };

        if (formData.value.id) {
          await UserAPI.update(formData.value.id, submitData);
        } else {
          await UserAPI.add(submitData);
        }
        ElMessage.success(formData.value.id ? "修改成功" : "新增成功");
        closeDialog();
        handleQuery();
      } catch (error: any) {
        ElMessage.error(`操作失败: ${error.message}`);
      } finally {
        loading.value = false;
      }
    }
  });
}, 3000);

/** 删除用户 */
function handleDelete(id?: number) {
  const userIds = [id || removeIds.value].join(",");
  if (!userIds) {
    ElMessage.warning("请勾选删除项");
    return;
  }
  ElMessageBox.confirm("确认删除用户?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(function () {
    UserAPI.deleteByIds(userIds).then(() => {
      ElMessage.success("删除成功");
      handleResetQuery();
    });
  });
}

/** 重置密码 */
function handleResetPassword(id: number) {
  ElMessageBox.confirm("确认将该用户密码重置为123456?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(function () {
    loading.value = true;
    UserAPI.update(id, { password: "123456" })
      .then(() => {
        ElMessage.success("密码重置成功");
      })
      .catch((error) => {
        ElMessage.error(`密码重置失败: ${error.message}`);
      })
      .finally(() => {
        loading.value = false;
      });
  });
}

/** 处理权限变更 */
function handlePermissionChange(values: string[]) {
  // 去重并保持顺序
  const uniqueValues = Array.from(new Set(values));
  // 如果有变化，更新表单数据
  if (uniqueValues.length !== values.length) {
    formData.value.permissions = uniqueValues;
  }
}

// 替换之前的selectedModules计算属性
const groupedPermissionsByModule = computed(() => {
  const permissions = formData.value.permissions || [];
  const groupedPerms = new Map<string, string[]>();

  permissions.forEach((permission) => {
    const modulePrefix = permission.split(":")[0];
    for (const route of constantRoutes) {
      if (route.children) {
        const matchedChild = route.children.find((child) => {
          // 获取该子路由对应的权限前缀
          const childPrefix = child.path
            ? child.path.split("/").pop() || child.path
            : (child.meta?.parentPath as string)?.split("/").pop() || "";

          // 精确匹配权限前缀
          return modulePrefix === childPrefix;
        });

        if (matchedChild?.meta?.title) {
          if (!groupedPerms.has(matchedChild.meta.title)) {
            groupedPerms.set(matchedChild.meta.title, []);
          }
          groupedPerms.get(matchedChild.meta.title)?.push(permission);
          break;
        }
      }
    }
  });

  // 按模块名称排序
  return new Map([...groupedPerms.entries()].sort());
});

// 添加格式化权限的方法
const formatPermission = (permission: string) => {
  const actionMap: Record<string, string> = {
    view: "访问页面",
    add: "新增",
    edit: "编辑",
    delete: "删除",
    import: "导入",
    export: "导出",
  };
  const action = permission.split(":")[1];
  return actionMap[action] || action;
};

// 替换 getTagType 方法为 getTagClass 方法
const getTagClass = (permission: string): string => {
  const actionMap: Record<string, string> = {
    view: "perm-tag-view",
    add: "perm-tag-add",
    edit: "perm-tag-edit",
    delete: "perm-tag-delete",
    import: "perm-tag-import",
    export: "perm-tag-export",
  };
  const action = permission.split(":")[1];
  return actionMap[action] || "";
};

// 更新样式
const style = document.createElement("style");
style.textContent = `
.permission-container {
  max-height: 400px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--el-border-color-lighter) transparent;
}
.permission-container::-webkit-scrollbar {
  width: 6px;
}
.permission-container::-webkit-scrollbar-thumb {
  background-color: var(--el-border-color-lighter);
  border-radius: 3px;
}
.permission-groups {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  padding-right: 6px;
}
.permission-group {
  padding: 12px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
  transition: all 0.3s;
  height: fit-content;
}
.permission-group:hover {
  background-color: var(--el-fill-color);
}
.permission-module {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}
.permission-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.el-tree-select__tags {
  display: none !important;
}
.el-collapse-item__header {
  font-size: 14px;
}

/* 自定义权限标签颜色 */
.perm-tag-view {
  --el-tag-bg-color: #e8f4ff !important;
  --el-tag-border-color: #a8d4ff !important;
  --el-tag-text-color: #1890ff !important;
}
.perm-tag-add {
  --el-tag-bg-color: #f0f9eb !important;
  --el-tag-border-color: #b3e19d !important;
  --el-tag-text-color: #67c23a !important;
}
.perm-tag-edit {
  --el-tag-bg-color: #fdf6ec !important;
  --el-tag-border-color: #f5dab1 !important;
  --el-tag-text-color: #e6a23c !important;
}
.perm-tag-delete {
  --el-tag-bg-color: #fef0f0 !important;
  --el-tag-border-color: #fbc4c4 !important;
  --el-tag-text-color: #f56c6c !important;
}
.perm-tag-import {
  --el-tag-bg-color: #f0f9f6 !important;
  --el-tag-border-color: #8fcec4 !important;
  --el-tag-text-color: #13c2c2 !important;
}
.perm-tag-export {
  --el-tag-bg-color: #f9f0ff !important;
  --el-tag-border-color: #d3adf7 !important;
  --el-tag-text-color: #722ed1 !important;
}
`;
document.head.appendChild(style);

onMounted(() => {
  handleQuery();
});
</script>
