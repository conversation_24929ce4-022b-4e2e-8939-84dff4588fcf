# /f:/水利站/backend/models.py
"""
数据库模型定义文件
现在使用混合架构：
- MongoDB: DeviceDataLog, OperationLog (实时和日志数据)
- MySQL: TaskMetadata, SystemKVStore (配置和状态数据)
"""

# 保持原有导入以兼容现有代码
from sqlalchemy import Integer, String, DateTime, JSON
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.sql import func
from database import Base
import datetime

# 注意：以下模型类现在仅作为数据结构定义
# 实际的数据库操作请使用相应的连接模块：
# - mongodb_connection.py 用于 DeviceDataLog 和 OperationLog
# - mysql_connection.py 用于 TaskMetadata 和 SystemKVStore

class DeviceDataLog(Base):
    """
    设备数据日志表 ORM模型
    注意：此模型现已迁移至MongoDB，请使用mongodb_connection.MongoDBOperations
    """

    # 表名
    __tablename__ = "device_data_logs"

    # 列表
    id: Mapped[int] = mapped_column(
        Integer, primary_key=True, index=True, comment="记录ID"
    )
    device_sn: Mapped[str] = mapped_column(
        String, index=True, nullable=False, comment="设备序列号"
    )

    # 使用服务器时间作为默认时间戳，确保时间一致性
    timestamp: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        comment="数据入库时间（服务器时间）",
    )

    # 使用JSON类型来存储原始的、完整的JSON数据，具有良好的扩展性
    raw_data: Mapped[dict] = mapped_column(JSON, nullable=False, comment="原始JSON数据")


class TaskMetadata(Base):
    """
    用于持久化存储定时任务元数据的表
    注意：此模型现已迁移至MySQL，请使用mysql_connection.MySQLOperations
    """

    __tablename__ = "task_metadata"

    # 任务的唯一ID (例如 cycle_id, sequence_id)
    task_id: Mapped[str] = mapped_column(String, primary_key=True, index=True)
    # 任务类型，用于恢复时区分
    task_type: Mapped[str] = mapped_column(
        String, nullable=False, comment="任务类型 (e.g., 'cycle', 'sequence')"
    )
    # 存储任务的所有详细信息
    task_data: Mapped[dict] = mapped_column(
        JSON, nullable=False, comment="任务的完整元数据"
    )


class SystemKVStore(Base):
    """
    用于存储系统级别的键值对状态，例如水泵轮换的下一个目标。
    这确保了即使在多进程或服务重启后，状态也能保持一致。
    注意：此模型现已迁移至MySQL，请使用mysql_connection.MySQLOperations
    """

    __tablename__ = "system_kv_store"

    key: Mapped[str] = mapped_column(String, primary_key=True, comment="状态的唯一键")
    value: Mapped[str] = mapped_column(String, nullable=False, comment="状态的值")


class OperationLog(Base):
    """
    系统操作日志表，记录所有关键操作的执行情况
    注意：此模型现已迁移至MongoDB，请使用mongodb_connection.MongoDBOperations
    """

    __tablename__ = "operation_logs"

    id: Mapped[int] = mapped_column(
        Integer, primary_key=True, index=True, comment="日志记录ID"
    )
    
    timestamp: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        comment="操作时间",
    )
    
    operation_type: Mapped[str] = mapped_column(
        String, nullable=False, index=True, comment="操作类型 (e.g., 'pump_control', 'device_command', 'scheduler_task')"
    )
    
    device_sn: Mapped[str] = mapped_column(
        String, index=True, nullable=True, comment="相关设备序列号"
    )
    
    operation_details: Mapped[str] = mapped_column(
        String, nullable=False, comment="操作详细描述"
    )
    
    command_sent: Mapped[str] = mapped_column(
        String, nullable=True, comment="发送的命令内容"
    )
    
    execution_status: Mapped[str] = mapped_column(
        String, nullable=False, comment="执行状态 (success, failed, pending)"
    )
    
    error_message: Mapped[str] = mapped_column(
        String, nullable=True, comment="错误信息（如果有）"
    )
    
    additional_data: Mapped[dict] = mapped_column(
        JSON, nullable=True, comment="额外的上下文数据"
    )