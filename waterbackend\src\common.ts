// 企业微信公共接口

import * as fs from 'fs';

import axios from 'axios';

//获取token
async function gettoken(corpid, corpsecret) {
  const url = `https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=${corpid}&corpsecret=${corpsecret}`;
  const res = await axios.get(url);
  return res.data.access_token;
}

//缓存token
async function cachetoken(corpid, corpsecret) {
  const token = await gettoken(corpid, corpsecret);
  const data = {
    access_token: token,
    time: new Date().getTime(),
  };
  fs.writeFileSync(`./${corpsecret}_token.json`, JSON.stringify(data));
  return token;
}

//判断缓存获取token
async function getcacheToken(corpid, corpsecret) {
  if (fs.existsSync(`./${corpsecret}_token.json`)) {
    const data = fs.readFileSync(`./${corpsecret}_token.json`);
    const token = JSON.parse(data.toString());
    const now = new Date().getTime();
    if (now - token.time < 7200 * 1000) {
      console.log('未过时', token.access_token);
      return token.access_token;
    }
  }
  return await cachetoken(corpid, corpsecret);
}

//发送信息
async function sendMsg(access_token, data) {
  const url = `https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=${access_token}`;
  const res = await axios.post(url, data);
  return res.data;
}

//获取手机号
async function getPhone(code) {
  const appid = 'wx10e983f4af327d82';
  const secret = '1dc5ad6d01b66ac3b45818193285ed83';
  let res = await axios.get(
    `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appid}&secret=${secret}`,
  );
  const url = `https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=${res.data['access_token']}`;
  const data = { code };
  res = await axios.post(url, data);
  return res.data;
}

export { getcacheToken, sendMsg, getPhone };
