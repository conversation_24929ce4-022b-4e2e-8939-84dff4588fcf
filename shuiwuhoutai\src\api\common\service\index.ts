import request from "@/utils/request";
import { UserQuery } from "./model";
class ServiceAPI {
  // 获取公共服务机构分页列表
  static getService(queryParams: UserQuery) {
    return request<any, any>({
      url: "/publicservice",
      method: "get",
      params: queryParams,
    });
  }

  // 获取公共服务机构表单详情
  static getServiceFormData(userId: number) {
    return request<any, any>({
      url: "/publicservice/" + userId,
      method: "get",
    });
  }

  // 添加公共服务机构
  static addService(data: any) {
    return request({
      url: "/publicservice",
      method: "post",
      data: data,
    });
  }

  // 修改公共服务机构
  static updateService(id: number, data: any) {
    return request({
      url: "/publicservice/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除公共服务机构
  static deleteServiceByIds(ids: string) {
    return request({
      url: "/publicservice/" + ids,
      method: "delete",
    });
  }

  // 获取宣传片分页列表
  static getFilm(queryParams: UserQuery) {
    return request<any, any>({
      url: "/film",
      method: "get",
      params: queryParams,
    });
  }

  // 获取宣传片表单详情
  static getFilmFormData(userId: number) {
    return request<any, any>({
      url: "/film/" + userId,
      method: "get",
    });
  }

  // 添加宣传片
  static addFilm(data: any) {
    return request({
      url: "/film",
      method: "post",
      data: data,
    });
  }

  // 修改宣传片
  static updateFilm(id: number, data: any) {
    return request({
      url: "/film/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除宣传片
  static deleteFilmByIds(ids: string) {
    return request({
      url: "/film/" + ids,
      method: "delete",
    });
  }

  // 获取720全景分页列表
  static getPanoramic(queryParams: UserQuery) {
    return request<any, any>({
      url: "/panoramic",
      method: "get",
      params: queryParams,
    });
  }

  // 获取720全景表单详情
  static getPanoramicFormData(userId: number) {
    return request<any, any>({
      url: "/panoramic/" + userId,
      method: "get",
    });
  }

  // 添加720全景
  static addPanoramic(data: any) {
    return request({
      url: "/panoramic",
      method: "post",
      data: data,
    });
  }

  // 修改720全景
  static updatePanoramic(id: number, data: any) {
    return request({
      url: "/panoramic/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除720全景
  static deletePanoramicByIds(ids: string) {
    return request({
      url: "/panoramic/" + ids,
      method: "delete",
    });
  }
}

export default ServiceAPI;
