import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as express from 'express';
import { json, urlencoded } from 'express';
import { AppModule } from './app.module';
import { ExpressAdapter } from '@nestjs/platform-express';
import * as fs from 'fs';
import * as http from 'http';
import * as https from 'https';
async function bootstrap() {
  // const httpsOptions = {
  //   key: fs.readFileSync('./secrets/newtown.xinpanmen.com.key'),
  //   cert: fs.readFileSync('./secrets/newtown.xinpanmen.com.pem'),
  // };
  // const app = await NestFactory.create(AppModule, { httpsOptions });
  // const app = await NestFactory.create(AppModule);
  const server = express();
  const app = await NestFactory.create(AppModule, new ExpressAdapter(server));
  app.use(json({ limit: '500mb' }));
  app.use(urlencoded({ extended: true, limit: '500mb' }));
  app.enableCors();

  // 创建一个websocket代理

  const config = new DocumentBuilder()
    .addBearerAuth()
    .setTitle('吴江水务api文档')
    .setDescription('吴江水务接口文档')
    .setVersion('1.0')
    .addTag('吴江水务')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api-docs', app, document);
  // console.log('http://localhost:8800/api-docs/');

  await app.init();

  http.createServer(server).listen(8800);
  // http.createServer(server).listen(80);
  // https.createServer(httpsOptions, server).listen(8800);
}
bootstrap();
