import { defineStore } from "pinia";
import { store } from "@/store";
import AuthAPI from "@/api/auth";
import User<PERSON><PERSON> from "@/api/common/user";
import { LoginData } from "@/api/auth/model";
import { TOKEN_KEY } from "@/enums/CacheEnum";
import { UserInfo } from "@/api/common/user/model";

export const useUserStore = defineStore("user", () => {
  const user = ref<UserInfo>({
    roles: [],
    perms: [],
  });

  function login(loginData: LoginData) {
    return new Promise<void>((resolve, reject) => {
      AuthAPI.login(loginData)
        .then((data) => {
          const { tokenType, accessToken } = data;
          localStorage.setItem(TOKEN_KEY, tokenType + " " + accessToken);
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  function getUserInfo() {
    return new Promise<UserInfo>((resolve, reject) => {
      UserAPI.getInfo()
        .then((data) => {
          if (!data) {
            reject("验证失败，请重新登录。");
            return;
          }
          if (!data.roles) {
            reject("角色必须是非空！");
            return;
          }

          const userInfo = {
            ...data,
            roles: data.roles,
            perms: data.permissions || [],
          };

          user.value = userInfo;
          resolve(userInfo);
        })
        .catch((error) => {
          console.error("Get user info error:", error);
          reject(error);
        });
    });
  }

  function logout() {
    localStorage.removeItem(TOKEN_KEY);
    user.value = { userId: "", username: "", roles: [], perms: [] };
  }

  function resetToken() {
    return new Promise<void>((resolve) => {
      localStorage.removeItem(TOKEN_KEY);
      // 重置用户信息
      user.value = { userId: "", username: "", roles: [], perms: [] };
      resolve();
    });
  }

  return {
    user,
    login,
    getUserInfo,
    logout,
    resetToken,
  };
});

export function useUserStoreHook() {
  return useUserStore(store);
}
