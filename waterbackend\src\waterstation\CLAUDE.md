[根目录](../../CLAUDE.md) > [src](../) > [waterstation](./) > **waterstation**

# 水利站模块 (Waterstation Module)

## 模块职责

水利站设备数据采集、存储、查询和分析的核心模块，支持多时间粒度的数据管理（5秒、分钟、小时），以及设备运行时状态监控。

## 入口与启动

- **模块入口**: `waterstation.module.ts`
- **数据库支持**: 双数据库架构
  - MySQL (TypeORM) - 分钟级、小时级数据和处理日志
  - MongoDB (Mongoose) - 5秒级高频数据存储

## 对外接口

### 分钟级数据接口
- `GET /device-data-minute` - 获取分钟级设备数据，支持设备和时间范围过滤
- `GET /device-data-minute/latest` - 获取最新的分钟级数据
- `GET /device-data-minute/statistics` - 获取分钟级数据统计信息
- `GET /device-data-minute/:deviceSn` - 根据设备序列号获取分钟级数据

### 小时级数据接口
- `GET /device-data-hour` - 获取小时级设备数据，支持设备和时间范围过滤
- `GET /device-data-hour/latest` - 获取最新的小时级数据
- `GET /device-data-hour/statistics` - 获取小时级数据统计信息
- `GET /device-data-hour/:deviceSn` - 根据设备序列号获取小时级数据

### 5秒级高频数据接口
- `GET /device-data-5s` - 获取5秒级设备数据（MongoDB存储）
- `GET /device-data-5s/latest` - 获取最新的5秒级数据
- `GET /device-data-5s/:deviceSn` - 根据设备序列号获取5秒级数据

### 设备运行时状态接口
- `GET /station-runtime` - 获取设备运行时状态信息
- `GET /station-runtime/status` - 获取设备在线状态统计

## 关键依赖与配置

### 外部依赖
- `@nestjs/typeorm` - MySQL ORM 框架
- `@nestjs/mongoose` - MongoDB ODM 框架
- `@nestjs/swagger` - API 文档生成
- `typeorm` - 数据库操作
- `mongoose` - MongoDB 操作

### 数据存储策略
- **5秒级数据**: MongoDB 存储，用于实时监控和短期分析
- **分钟级数据**: MySQL 存储，聚合统计（平均值、最大值、最小值）
- **小时级数据**: MySQL 存储，长期趋势分析
- **处理日志**: MySQL 存储，数据处理过程跟踪

## 数据模型

### DeviceDataMinute 实体 (MySQL)
```typescript
class DeviceDataMinute {
  id: number;                    // 主键
  deviceSn: string;              // 设备序列号
  timestamp: Date;               // 分钟时间戳
  avgData: Record<string, any>;  // 平均值数据 (JSON)
  maxData: Record<string, any>;  // 最大值数据 (JSON)
  minData: Record<string, any>;  // 最小值数据 (JSON)
  sampleCount: number;           // 原始样本数量
  createdAt: Date;              // 创建时间
}
```

### DeviceDataHour 实体 (MySQL)
```typescript
class DeviceDataHour {
  id: number;                    // 主键
  deviceSn: string;              // 设备序列号
  timestamp: Date;               // 小时时间戳
  avgData: Record<string, any>;  // 平均值数据 (JSON)
  maxData: Record<string, any>;  // 最大值数据 (JSON)
  minData: Record<string, any>;  // 最小值数据 (JSON)
  sampleCount: number;           // 原始样本数量
  createdAt: Date;              // 创建时间
}
```

### DeviceData5s Schema (MongoDB)
```typescript
interface DeviceData5s {
  deviceSn: string;              // 设备序列号
  timestamp: Date;               // 5秒时间戳
  data: Record<string, any>;     // 实时数据 (JSON)
  createdAt: Date;              // 创建时间
}
```

### DataProcessingLog 实体 (MySQL)
```typescript
class DataProcessingLog {
  id: number;                    // 主键
  logType: string;               // 日志类型
  message: string;               // 日志消息
  deviceSn?: string;             // 相关设备序列号
  processingTime?: Date;         // 处理时间
  createdAt: Date;              // 创建时间
}
```

## 测试与质量

### 架构优势
- **分层存储**: 不同时间粒度使用不同数据库，优化查询性能
- **聚合计算**: 分钟级和小时级数据包含统计信息（平均值、最大最小值）
- **高频支持**: MongoDB 处理5秒级高频数据写入
- **可扩展**: 模块化设计，便于添加新的数据处理逻辑

### 性能考虑
- 5秒级数据量较大，使用 MongoDB 优化读写性能
- 分钟级、小时级数据使用 MySQL 便于复杂查询和关联分析
- JSON 字段存储灵活的设备数据格式

## 常见问题 (FAQ)

**Q: 如何添加新的设备数据字段？**
A: 由于使用 JSON 字段存储数据，可以灵活添加新字段而不需要修改数据库结构

**Q: 数据聚合是如何实现的？**
A: 从5秒级数据聚合到分钟级，从分钟级聚合到小时级，包含样本数量统计

**Q: 如何优化大数据量查询？**
A: 建议添加设备序列号和时间戳的复合索引，使用时间范围查询

**Q: MongoDB和MySQL数据如何同步？**
A: 通过后台服务定时聚合5秒级数据到分钟级，再聚合到小时级

## 相关文件清单

```
src/waterstation/
├── waterstation.module.ts                          # 模块定义
├── entities/                                       # TypeORM实体
│   ├── device-data-minute.entity.ts                # 分钟级数据实体
│   ├── device-data-hour.entity.ts                  # 小时级数据实体
│   └── data-processing-log.entity.ts               # 数据处理日志实体
├── schemas/                                        # Mongoose模式
│   └── device-data-5s.schema.ts                    # 5秒级数据模式
├── controllers/                                    # REST API控制器
│   ├── device-data-minute.controller.ts            # 分钟级数据接口
│   ├── device-data-hour.controller.ts              # 小时级数据接口
│   ├── device-data-5s.controller.ts                # 5秒级数据接口
│   └── station-runtime.controller.ts               # 设备运行时接口
└── services/                                       # 业务逻辑服务
    ├── device-data-minute.service.ts               # 分钟级数据服务
    ├── device-data-hour.service.ts                 # 小时级数据服务
    ├── device-data-5s.service.ts                   # 5秒级数据服务
    └── station-runtime.service.ts                  # 设备运行时服务
```

## 变更记录 (Changelog)
- **2025-08-29**: 初始文档创建，完整梳理水利站数据采集和管理功能