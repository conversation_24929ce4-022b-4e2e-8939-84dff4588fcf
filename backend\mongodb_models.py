# /f:/水利站/backend/mongodb_models.py
from pymongo import MongoClient, IndexModel
from datetime import datetime, timedelta
import logging
from typing import Optional, Dict, List
from bson import ObjectId

logger = logging.getLogger(__name__)

class MongoDBManager:
    """
    MongoDB管理器 - 用于存储5秒级实时数据
    数据保留策略：仅保留当天数据，自动清理过期数据
    """
    
    def __init__(self, connection_string: str = "********************************************************************************", database_name: str = "water_station_realtime"):
        """
        初始化MongoDB连接
        """
        self.client = MongoClient(connection_string)
        self.db = self.client[database_name]
        self.device_data_collection = self.db["device_data_5s"]
        
        # 创建索引以优化查询性能
        self._create_indexes()
        
        # 设置TTL索引，自动删除24小时前的数据
        self._setup_ttl_index()
        
    def _create_indexes(self):
        """创建必要的索引"""
        indexes = [
            IndexModel([("device_sn", 1), ("timestamp", -1)]),  # 设备和时间复合索引
            IndexModel([("timestamp", -1)]),  # 时间索引，用于时间范围查询
            IndexModel([("device_sn", 1)]),   # 设备索引
        ]
        
        try:
            self.device_data_collection.create_indexes(indexes)
            logger.info("MongoDB索引创建成功")
        except Exception as e:
            logger.error(f"MongoDB索引创建失败: {e}")
    
    def _setup_ttl_index(self):
        """设置TTL索引，自动删除超过24小时的数据"""
        try:
            # 创建TTL索引，24小时后自动删除文档
            self.device_data_collection.create_index(
                [("timestamp", 1)], 
                expireAfterSeconds=86400  # 24小时 = 86400秒
            )
            logger.info("MongoDB TTL索引设置成功，数据将在24小时后自动删除")
        except Exception as e:
            logger.error(f"MongoDB TTL索引设置失败: {e}")
    
    def insert_device_data(self, device_sn: str, raw_data: dict) -> Optional[str]:
        """
        插入5秒级设备数据
        
        Args:
            device_sn: 设备序列号
            raw_data: 原始数据字典
            
        Returns:
            str: 插入的文档ID，失败时返回None
        """
        try:
            document = {
                "device_sn": device_sn,
                "timestamp": datetime.utcnow(),
                "raw_data": raw_data
            }
            
            result = self.device_data_collection.insert_one(document)
            logger.debug(f"MongoDB数据插入成功: device_sn={device_sn}, id={result.inserted_id}")
            return str(result.inserted_id)
            
        except Exception as e:
            logger.error(f"MongoDB数据插入失败: device_sn={device_sn}, error={e}")
            return None
    
    def get_device_data_for_downsampling(self, start_time: datetime, end_time: datetime, device_sn: Optional[str] = None) -> List[Dict]:
        """
        获取指定时间范围内的数据用于降采样
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            device_sn: 设备序列号（可选）
            
        Returns:
            List[Dict]: 数据列表
        """
        try:
            query = {
                "timestamp": {
                    "$gte": start_time,
                    "$lt": end_time
                }
            }
            
            if device_sn:
                query["device_sn"] = device_sn
            
            cursor = self.device_data_collection.find(query).sort("timestamp", 1)
            return list(cursor)
            
        except Exception as e:
            logger.error(f"MongoDB数据查询失败: {e}")
            return []
    
    def get_latest_device_data(self, device_sn: Optional[str] = None, limit: int = 100) -> List[Dict]:
        """
        获取最新的设备数据
        
        Args:
            device_sn: 设备序列号（可选）
            limit: 返回记录数量限制
            
        Returns:
            List[Dict]: 最新数据列表
        """
        try:
            query = {}
            if device_sn:
                query["device_sn"] = device_sn
            
            cursor = self.device_data_collection.find(query).sort("timestamp", -1).limit(limit)
            return list(cursor)
            
        except Exception as e:
            logger.error(f"MongoDB最新数据查询失败: {e}")
            return []
    
    def delete_processed_data(self, end_time: datetime, device_sn: Optional[str] = None) -> int:
        """
        删除已经降采样处理的数据
        
        Args:
            end_time: 删除此时间之前的数据
            device_sn: 设备序列号（可选）
            
        Returns:
            int: 删除的文档数量
        """
        try:
            query = {
                "timestamp": {"$lt": end_time}
            }
            
            if device_sn:
                query["device_sn"] = device_sn
            
            result = self.device_data_collection.delete_many(query)
            deleted_count = result.deleted_count
            
            if deleted_count > 0:
                logger.info(f"MongoDB删除已处理数据: {deleted_count}条记录")
            
            return deleted_count
            
        except Exception as e:
            logger.error(f"MongoDB数据删除失败: {e}")
            return 0
    
    def get_collection_stats(self) -> Dict:
        """
        获取集合统计信息
        
        Returns:
            Dict: 集合统计信息
        """
        try:
            stats = self.db.command("collStats", "device_data_5s")
            count = self.device_data_collection.count_documents({})
            
            return {
                "document_count": count,
                "storage_size": stats.get("storageSize", 0),
                "index_size": stats.get("totalIndexSize", 0),
                "avg_obj_size": stats.get("avgObjSize", 0)
            }
            
        except Exception as e:
            logger.error(f"MongoDB统计信息获取失败: {e}")
            return {}
    
    def close_connection(self):
        """关闭MongoDB连接"""
        try:
            self.client.close()
            logger.info("MongoDB连接已关闭")
        except Exception as e:
            logger.error(f"MongoDB连接关闭失败: {e}")

# 全局MongoDB管理器实例
mongodb_manager = None

def get_mongodb_manager() -> MongoDBManager:
    """获取MongoDB管理器实例"""
    global mongodb_manager
    if mongodb_manager is None:
        mongodb_manager = MongoDBManager()
    return mongodb_manager

def close_mongodb_connection():
    """关闭MongoDB连接"""
    global mongodb_manager
    if mongodb_manager:
        mongodb_manager.close_connection()
        mongodb_manager = None