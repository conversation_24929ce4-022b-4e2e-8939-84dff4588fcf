import { createRouter, createWeb<PERSON>ash<PERSON><PERSON>ory, RouteRecordRaw } from "vue-router";

export const Layout = () => import("@/layout/index.vue");

// 静态路由
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: "/redirect",
    component: Layout,
    meta: { hidden: true },
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect/index.vue"),
      },
    ],
  },

  {
    path: "/login",
    component: () => import("@/views/login/index.vue"),
    meta: { hidden: true },
  },

  {
    path: "/",
    component: Layout,
    redirect: "/admin/manage", // 重定向到具体的页面而不是父路由
    children: [
      {
        path: "401",
        component: () => import("@/views/error-page/401.vue"),
        meta: { hidden: true },
      },
      {
        path: "404",
        component: () => import("@/views/error-page/404.vue"),
        meta: { hidden: true },
      },
    ],
  },

  {
    path: "/admin",
    component: Layout,
    name: "admin",
    meta: {
      title: "账号管理",
      icon: "user",
      hidden: false,
      params: null,
      affix: true,
      alwaysShow: true,
    },
    children: [
      {
        path: "manage",
        component: () => import("@/views/admin/index.vue"),
        name: "Admin",
        meta: {
          title: "账号管理",
          icon: "user",
          keepAlive: true,
          permissions: ["admin:manage"],
        },
      },
      {
        path: "edit",
        component: () => import("@/views/admin/edit.vue"),
        name: "AdminEdit",
        meta: {
          title: "修改密码",
          icon: "cipher",
        },
      },
    ],
  },

  // 告警中心
  {
    path: "/alarm",
    component: Layout,
    name: "alarm",
    meta: {
      title: "告警中心",
      icon: "warning",
      hidden: false,
      alwaysShow: true,
    },
    children: [
      {
        path: "management",
        component: () => import("@/views/alarm/management/index.vue"),
        name: "AlarmManagement",
        meta: {
          title: "告警管理",
          icon: "management",
          alwaysShow: true,
        },
        children: [
          {
            path: "list",
            component: () => import("@/views/alarm/management/list.vue"),
            name: "AlarmList",
            meta: {
              title: "告警列表",
              icon: "list",
            },
          },
          {
            path: "sms-log",
            component: () => import("@/views/alarm/management/sms-log.vue"),
            name: "SmsLog",
            meta: {
              title: "短信日志",
              icon: "imessage",
            },
          },
        ],
      },
      {
        path: "analysis",
        component: () => import("@/views/alarm/analysis/index.vue"),
        name: "AlarmAnalysis",
        meta: {
          title: "告警分析",
          icon: "analysis",
          alwaysShow: true,
        },
        children: [
          {
            path: "facility-statistics",
            component: () =>
              import("@/views/alarm/analysis/facility-statistics.vue"),
            name: "FacilityAlarmStatistics",
            meta: {
              title: "设施告警统计",
              icon: "statistics",
            },
          },
          {
            path: "area-statistics",
            component: () =>
              import("@/views/alarm/analysis/area-statistics.vue"),
            name: "AreaAlarmStatistics",
            meta: {
              title: "区域告警统计",
              icon: "area",
            },
          },
          {
            path: "code-statistics",
            component: () =>
              import("@/views/alarm/analysis/code-statistics.vue"),
            name: "AlarmCodeStatistics",
            meta: {
              title: "告警代码统计",
              icon: "code",
            },
          },
          {
            path: "reason-analysis",
            component: () =>
              import("@/views/alarm/analysis/reason-analysis.vue"),
            name: "AlarmReasonAnalysis",
            meta: {
              title: "告警原因分析",
              icon: "ianalysis",
            },
          },
        ],
      },
    ],
  },

  // 运维中心
  {
    path: "/operation",
    component: Layout,
    name: "operation",
    meta: {
      title: "运维中心",
      icon: "operation",
      hidden: false,
      alwaysShow: true,
    },
    children: [
      {
        path: "event-center",
        component: () => import("@/views/operation/event-center.vue"),
        name: "EventCenter",
        meta: {
          title: "事件中心",
          icon: "event",
        },
      },
      {
        path: "work-order",
        component: () => import("@/views/operation/work-order/index.vue"),
        name: "WorkOrder",
        meta: {
          title: "工单中心",
          icon: "work-order",
          alwaysShow: true,
        },
        children: [
          {
            path: "center",
            component: () => import("@/views/operation/work-order/center.vue"),
            name: "WorkOrderCenter",
            meta: {
              title: "工单中心",
              icon: "work-order",
            },
          },
          {
            path: "statistics",
            component: () =>
              import("@/views/operation/work-order/statistics.vue"),
            name: "WorkOrderStatistics",
            meta: {
              title: "工单数量统计",
              icon: "istatistics",
            },
          },
          {
            path: "delay-rate",
            component: () =>
              import("@/views/operation/work-order/delay-rate.vue"),
            name: "WorkOrderDelayRate",
            meta: {
              title: "工单延期率",
              icon: "delay",
            },
          },
          {
            path: "evaluation",
            component: () =>
              import("@/views/operation/work-order/evaluation.vue"),
            name: "WorkOrderEvaluation",
            meta: {
              title: "工单评价模型",
              icon: "evaluation",
            },
          },
        ],
      },
      {
        path: "operation-log",
        component: () => import("@/views/operation/operation-log.vue"),
        name: "OperationLog",
        meta: {
          title: "运维日志",
          icon: "log",
        },
      },
      {
        path: "pipeline-inspection",
        component: () =>
          import("@/views/operation/pipeline-inspection/index.vue"),
        name: "PipelineInspection",
        meta: {
          title: "管网巡检",
          icon: "pipeline",
          alwaysShow: true,
        },
        children: [
          {
            path: "progress",
            component: () =>
              import("@/views/operation/pipeline-inspection/progress.vue"),
            name: "PipelineProgress",
            meta: {
              title: "管网巡检进度",
              icon: "progress",
            },
          },
          {
            path: "record",
            component: () =>
              import("@/views/operation/pipeline-inspection/record.vue"),
            name: "PipelineRecord",
            meta: {
              title: "管网运维记录",
              icon: "record",
            },
          },
          {
            path: "template",
            component: () =>
              import("@/views/operation/pipeline-inspection/template.vue"),
            name: "PipelineTemplate",
            meta: {
              title: "管网运维模版",
              icon: "template",
            },
          },
          {
            path: "plan",
            component: () =>
              import("@/views/operation/pipeline-inspection/plan.vue"),
            name: "PipelinePlan",
            meta: {
              title: "管网运维计划",
              icon: "plan",
            },
          },
          // {
          //   path: "engineering",
          //   component: () =>
          //     import("@/views/operation/pipeline-inspection/engineering.vue"),
          //   name: "PipelineEngineering",
          //   meta: {
          //     title: "管网工程管理",
          //     icon: "engineering",
          //   },
          // },
          // {
          //   path: "strategy",
          //   component: () =>
          //     import("@/views/operation/pipeline-inspection/strategy.vue"),
          //   name: "PipelineStrategy",
          //   meta: {
          //     title: "管网运维策略",
          //     icon: "strategy",
          //   },
          // },
        ],
      },
      {
        path: "site-inspection",
        component: () => import("@/views/operation/site-inspection/index.vue"),
        name: "SiteInspection",
        meta: {
          title: "站点巡检",
          icon: "site",
          alwaysShow: true,
        },
        children: [
          {
            path: "progress",
            component: () =>
              import("@/views/operation/site-inspection/progress.vue"),
            name: "SiteProgress",
            meta: {
              title: "站点运维进度",
              icon: "progress",
            },
          },
          {
            path: "plan",
            component: () =>
              import("@/views/operation/site-inspection/plan.vue"),
            name: "SitePlan",
            meta: {
              title: "站点运维计划",
              icon: "plan",
            },
          },
          {
            path: "record",
            component: () =>
              import("@/views/operation/site-inspection/record.vue"),
            name: "SiteRecord",
            meta: {
              title: "站点运维记录",
              icon: "record",
            },
          },
          {
            path: "template",
            component: () =>
              import("@/views/operation/site-inspection/template.vue"),
            name: "SiteTemplate",
            meta: {
              title: "站点运维模版",
              icon: "template",
            },
          },
        ],
      },
      {
        path: "water-quality",
        component: () => import("@/views/operation/water-quality/index.vue"),
        name: "WaterQuality",
        meta: {
          title: "水质填报",
          icon: "water",
          alwaysShow: true,
        },
        children: [
          {
            path: "data-report",
            component: () =>
              import("@/views/operation/water-quality/data-report.vue"),
            name: "DataReport",
            meta: {
              title: "数据填报",
              icon: "report",
            },
          },
          {
            path: "test-plan",
            component: () =>
              import("@/views/operation/water-quality/test-plan.vue"),
            name: "TestPlan",
            meta: {
              title: "检测计划",
              icon: "plan",
            },
          },
          {
            path: "test-template",
            component: () =>
              import("@/views/operation/water-quality/test-template.vue"),
            name: "TestTemplate",
            meta: {
              title: "检测模版",
              icon: "template",
            },
          },
        ],
      },
    ],
  },

  // 数据分析
  {
    path: "/data-analysis",
    component: Layout,
    name: "dataAnalysis",
    meta: {
      title: "数据分析",
      icon: "analysis",
      hidden: false,
      alwaysShow: true,
    },
    children: [
      {
        path: "working-condition",
        component: () =>
          import("@/views/data-analysis/working-condition/index.vue"),
        name: "WorkingCondition",
        meta: {
          title: "工况统计分析",
          icon: "condition",
          alwaysShow: true,
        },
        children: [
          {
            path: "equipment-runtime",
            component: () =>
              import(
                "@/views/data-analysis/working-condition/equipment-runtime.vue"
              ),
            name: "EquipmentRuntime",
            meta: {
              title: "动力设备运行时长",
              icon: "runtime",
            },
          },
        ],
      },
      {
        path: "comprehensive",
        component: () =>
          import("@/views/data-analysis/comprehensive/index.vue"),
        name: "Comprehensive",
        meta: {
          title: "综合数据分析",
          icon: "comprehensive",
          alwaysShow: true,
        },
        children: [
          {
            path: "area-site-analysis",
            component: () =>
              import(
                "@/views/data-analysis/comprehensive/area-site-analysis.vue"
              ),
            name: "AreaSiteAnalysis",
            meta: {
              title: "区域站点运行分析",
              icon: "area",
            },
          },
        ],
      },
      {
        path: "energy-consumption",
        component: () =>
          import("@/views/data-analysis/energy-consumption/index.vue"),
        name: "EnergyConsumption",
        meta: {
          title: "能耗统计分析",
          icon: "energy",
          alwaysShow: true,
        },
        children: [
          {
            path: "analysis-report",
            component: () =>
              import(
                "@/views/data-analysis/energy-consumption/analysis-report.vue"
              ),
            name: "EnergyAnalysisReport",
            meta: {
              title: "能耗分析报表",
              icon: "statis",
            },
          },
          {
            path: "area-analysis-report",
            component: () =>
              import(
                "@/views/data-analysis/energy-consumption/area-analysis-report.vue"
              ),
            name: "AreaEnergyAnalysisReport",
            meta: {
              title: "区域能耗分析报表",
              icon: "statis",
            },
          },
        ],
      },
      {
        path: "load",
        component: () => import("@/views/data-analysis/load/index.vue"),
        name: "Load",
        meta: {
          title: "负荷统计分析",
          icon: "load",
          alwaysShow: true,
        },
        children: [
          {
            path: "site-report",
            component: () =>
              import("@/views/data-analysis/load/site-report.vue"),
            name: "SiteLoadReport",
            meta: {
              title: "站点负荷报表",
              icon: "statis",
            },
          },
          {
            path: "area-analysis-report",
            component: () =>
              import("@/views/data-analysis/load/area-analysis-report.vue"),
            name: "AreaLoadAnalysisReport",
            meta: {
              title: "区域负荷分析报表",
              icon: "statis",
            },
          },
        ],
      },
      {
        path: "maintenance",
        component: () => import("@/views/data-analysis/maintenance/index.vue"),
        name: "Maintenance",
        meta: {
          title: "维保统计分析",
          icon: "maintenance",
          alwaysShow: true,
        },
        children: [
          {
            path: "multi-site-plan-report",
            component: () =>
              import(
                "@/views/data-analysis/maintenance/multi-site-plan-report.vue"
              ),
            name: "MultiSitePlanReport",
            meta: {
              title: "多站点维保统计",
              icon: "statis",
            },
          },
          {
            path: "area-statistics",
            component: () =>
              import("@/views/data-analysis/maintenance/area-statistics.vue"),
            name: "AreaMaintenanceStatistics",
            meta: {
              title: "区域维保统计",
              icon: "statis",
            },
          },
        ],
      },
    ],
  },

  // 基础信息库
  {
    path: "/basic-info",
    component: Layout,
    name: "basicInfo",
    meta: {
      title: "基础信息库",
      icon: "database",
      hidden: false,
      alwaysShow: true,
    },
    children: [
      {
        path: "organization",
        component: () => import("@/views/basic-info/organization.vue"),
        name: "Organization",
        meta: {
          title: "组织架构",
          icon: "organization",
        },
      },
      {
        path: "area-management",
        component: () => import("@/views/basic-info/area-management.vue"),
        name: "AreaManagement",
        meta: {
          title: "区域管理",
          icon: "area",
        },
      },
      {
        path: "facility-management",
        component: () =>
          import("@/views/basic-info/facility-management/index.vue"),
        name: "FacilityManagement",
        meta: {
          title: "设施管理",
          icon: "facility",
          alwaysShow: true,
        },
        children: [
          {
            path: "overview",
            component: () =>
              import("@/views/basic-info/facility-management/overview.vue"),
            name: "FacilityOverview",
            meta: {
              title: "设施一览表",
              icon: "overview",
            },
          },
          {
            path: "type-management",
            component: () =>
              import(
                "@/views/basic-info/facility-management/type-management.vue"
              ),
            name: "FacilityTypeManagement",
            meta: {
              title: "设施类型管理",
              icon: "type",
            },
          },
          {
            path: "coordinates-audit",
            component: () =>
              import(
                "@/views/basic-info/facility-management/coordinates-audit.vue"
              ),
            name: "CoordinatesAudit",
            meta: {
              title: "经纬度审核",
              icon: "coordinates",
            },
          },
        ],
      },
      {
        path: "pipeline-management",
        component: () =>
          import("@/views/basic-info/pipeline-management/index.vue"),
        name: "PipelineManagement",
        meta: {
          title: "管线管理",
          icon: "pipeline",
          alwaysShow: true,
        },
        children: [
          {
            path: "type",
            component: () =>
              import("@/views/basic-info/pipeline-management/type.vue"),
            name: "PipelineType",
            meta: {
              title: "管线类型",
              icon: "type",
            },
          },
          {
            path: "info",
            component: () =>
              import("@/views/basic-info/pipeline-management/info.vue"),
            name: "PipelineInfo",
            meta: {
              title: "管线信息",
              icon: "info",
            },
          },
        ],
      },
    ],
  },
];

/**
 * 创建路由
 */
const router = createRouter({
  history: createWebHashHistory(),
  routes: constantRoutes,
  scrollBehavior: () => ({ left: 0, top: 0 }),
});

/**
 * 重置路由
 */
export function resetRouter() {
  router.replace({ path: "/login" });
}

export default router;
