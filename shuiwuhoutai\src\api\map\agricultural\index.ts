import request from "@/utils/request";
import type {
  AgriculturalInfo,
  AgriculturalPageQuery,
  AgriculturalPageResult,
} from "./model";

/**
 * 耕地管理 API
 */
class AgriculturalAPI {
  /**
   * 分页获取耕地列表
   *
   * @param params 查询参数
   * @returns 耕地列表
   */
  static getPage(
    params: AgriculturalPageQuery
  ): Promise<AgriculturalPageResult> {
    return request({
      url: "/agricultural",
      method: "get",
      params,
    });
  }

  /**
   * 获取耕地详情
   *
   * @param id 耕地ID
   * @returns 耕地详情
   */
  static getById(id: number): Promise<AgriculturalInfo> {
    return request({
      url: `/agricultural/${id}`,
      method: "get",
    });
  }

  /**
   * 获取耕地表单数据（用于编辑）
   *
   * @param id 耕地ID
   * @returns 耕地表单数据
   */
  static getFormData(id: number): Promise<AgriculturalInfo> {
    return request({
      url: `/agricultural/${id}`,
      method: "get",
    });
  }

  /**
   * 新增耕地
   *
   * @param data 耕地表单数据
   * @returns 创建结果
   */
  static add(data: AgriculturalInfo): Promise<any> {
    return request({
      url: "/agricultural",
      method: "post",
      data,
    });
  }

  /**
   * 更新耕地
   *
   * @param id 耕地ID
   * @param data 耕地表单数据
   * @returns 更新结果
   */
  static update(id: number, data: AgriculturalInfo): Promise<any> {
    return request({
      url: `/agricultural/${id}`,
      method: "put",
      data,
    });
  }

  /**
   * 删除耕地
   *
   * @param id 耕地ID
   * @returns 删除结果
   */
  static deleteById(id: number): Promise<any> {
    return request({
      url: `/agricultural/${id}`,
      method: "delete",
    });
  }

  /**
   * 批量删除耕地
   *
   * @param ids 耕地ID，多个用逗号分隔
   * @returns 删除结果
   */
  static deleteByIds(ids: string): Promise<any> {
    return request({
      url: `/agricultural/${ids}`,
      method: "delete",
    });
  }

  /**
   * 导入耕地数据
   *
   * @param file 上传的文件
   * @returns 导入结果
   */
  static import(file: FormData): Promise<any> {
    return request({
      url: "/agricultural/import",
      method: "post",
      data: file,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }
}

export default AgriculturalAPI;
