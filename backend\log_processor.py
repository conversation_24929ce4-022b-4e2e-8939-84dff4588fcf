# RabbitMQ消息队列消费者和日志处理器
import pika
import json
import time
import threading
import signal
import sys
import os
from datetime import datetime
from typing import List, Dict, Any
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('log_processor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LogProcessor:
    """单例日志处理器 - 从RabbitMQ消费消息并批量写入日志文件"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(LogProcessor, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if hasattr(self, '_initialized'):
            return

        self._initialized = True
        self.connection = None
        self.channel = None
        self.is_running = False
        self.message_buffer = []
        self.buffer_lock = threading.Lock()
        self.max_batch_size = 1000  # 最多1000条数据
        self.batch_interval = 1.0   # 1秒间隔

        # 日志文件相关
        self.logs_dir = "./logs"
        self.realtime_logs_dir = os.path.join(self.logs_dir, "realtime_data")
        self.status_logs_dir = os.path.join(self.logs_dir, "device_status")

        # 确保日志目录存在
        os.makedirs(self.realtime_logs_dir, exist_ok=True)
        os.makedirs(self.status_logs_dir, exist_ok=True)

        # 处理线程
        self.processing_thread = None

        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """处理终止信号"""
        logger.info(f"接收到信号 {signum}，准备优雅关闭...")
        self.stop()
        sys.exit(0)

    def _connect_rabbitmq(self) -> bool:
        """连接到RabbitMQ"""
        try:
            connection_params = pika.ConnectionParameters(
                host="localhost",
                port=5672,
                virtual_host="/",
                credentials=pika.PlainCredentials("water_user", "water123"),
                heartbeat=600,
                blocked_connection_timeout=300,
            )

            self.connection = pika.BlockingConnection(connection_params)
            self.channel = self.connection.channel()

            # 确保队列存在
            self.channel.queue_declare(queue='device_data_queue', durable=True)

            # 设置QoS，每次最多预取1000条消息
            self.channel.basic_qos(prefetch_count=1000)

            logger.info("RabbitMQ消费者连接成功建立")
            return True

        except Exception as e:
            logger.error(f"RabbitMQ消费者连接失败: {e}")
            return False

    def _get_log_file_path(self, message_type: str) -> str:
        """根据消息类型获取日志文件路径"""
        today = datetime.now().strftime("%Y%m%d")

        if message_type == 'status_change':
            filename = f"device_status_{today}.log"
            return os.path.join(self.status_logs_dir, filename)
        else:
            filename = f"realtime_data_{today}.log"
            return os.path.join(self.realtime_logs_dir, filename)

    def _write_batch_to_file(self, messages: List[Dict[str, Any]]):
        """批量写入消息到日志文件"""
        if not messages:
            return

        # 按消息类型分组
        realtime_messages = []
        status_messages = []

        for msg in messages:
            if msg.get('message_type') == 'status_change':
                status_messages.append(msg)
            else:
                realtime_messages.append(msg)

        # 写入实时数据日志
        if realtime_messages:
            self._write_messages_to_file(realtime_messages, 'device_data')

        # 写入状态变化日志
        if status_messages:
            self._write_messages_to_file(status_messages, 'status_change')

    def _write_messages_to_file(self, messages: List[Dict[str, Any]], message_type: str):
        """写入指定类型的消息到对应的日志文件"""
        log_file_path = self._get_log_file_path(message_type)

        try:
            with open(log_file_path, 'a', encoding='utf-8') as f:
                for message in messages:
                    # 格式化日志记录
                    if message_type == 'status_change':
                        log_entry = self._format_status_change_log(message)
                    else:
                        log_entry = self._format_device_data_log(message)

                    f.write(log_entry + '\n')

            logger.debug(f"成功写入 {len(messages)} 条 {message_type} 日志到 {log_file_path}")

        except Exception as e:
            logger.error(f"写入日志文件失败 {log_file_path}: {e}")

    def _format_device_data_log(self, message: Dict[str, Any]) -> str:
        """格式化设备数据日志"""
        data = message.get('data', {})
        log_entry = {
            'timestamp': message.get('timestamp'),
            'device_sn': data.get('device_sn'),
            'client_address': data.get('client_address'),
            'raw_data': data.get('raw_data', {}),
            'log_type': '设备实时数据'
        }
        return json.dumps(log_entry, ensure_ascii=False)

    def _format_status_change_log(self, message: Dict[str, Any]) -> str:
        """格式化状态变化日志"""
        data = message.get('data', {})
        log_entry = {
            'timestamp': message.get('timestamp'),
            'device_sn': data.get('device_sn'),
            'client_address': data.get('client_address'),
            'change_count': len(data.get('changes', [])),
            'changes': data.get('changes', []),
            'log_type': '设备物理开关档位变换' if data.get('is_status_only') else '设备状态数据变化'
        }
        return json.dumps(log_entry, ensure_ascii=False)

    def _message_callback(self, ch, method, properties, body):
        """RabbitMQ消息回调函数"""
        try:
            message = json.loads(body.decode('utf-8'))

            with self.buffer_lock:
                self.message_buffer.append(message)

            # 手动确认消息
            ch.basic_ack(delivery_tag=method.delivery_tag)

        except Exception as e:
            logger.error(f"处理RabbitMQ消息失败: {e}")
            # 拒绝消息但不重新投递
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)

    def _batch_processing_loop(self):
        """批量处理循环"""
        while self.is_running:
            try:
                # 等待指定时间间隔
                time.sleep(self.batch_interval)

                # 获取缓冲区中的消息
                with self.buffer_lock:
                    if not self.message_buffer:
                        continue

                    # 取出最多max_batch_size条消息
                    batch_messages = self.message_buffer[:self.max_batch_size]
                    self.message_buffer = self.message_buffer[self.max_batch_size:]

                # 批量写入日志文件
                if batch_messages:
                    self._write_batch_to_file(batch_messages)
                    logger.info(f"批量处理了 {len(batch_messages)} 条消息")

            except Exception as e:
                logger.error(f"批量处理循环异常: {e}")
                time.sleep(5)  # 异常时等待5秒

    def start(self):
        """启动日志处理器"""
        if self.is_running:
            logger.warning("日志处理器已在运行")
            return

        logger.info("启动RabbitMQ日志处理器...")

        # 连接RabbitMQ
        if not self._connect_rabbitmq():
            logger.error("无法连接到RabbitMQ，日志处理器启动失败")
            return

        self.is_running = True

        # 启动批量处理线程
        self.processing_thread = threading.Thread(target=self._batch_processing_loop, daemon=True)
        self.processing_thread.start()

        # 开始消费消息
        try:
            self.channel.basic_consume(
                queue='device_data_queue',
                on_message_callback=self._message_callback
            )

            logger.info("日志处理器启动成功，开始消费RabbitMQ消息...")
            self.channel.start_consuming()

        except KeyboardInterrupt:
            logger.info("接收到中断信号，停止消息消费...")
            self.stop()
        except Exception as e:
            logger.error(f"消息消费异常: {e}")
            self.stop()

    def stop(self):
        """停止日志处理器"""
        if not self.is_running:
            return

        logger.info("停止日志处理器...")
        self.is_running = False

        # 停止消息消费
        if self.channel and not self.channel.is_closed:
            self.channel.stop_consuming()

        # 等待处理线程完成
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=5)

        # 处理剩余的缓冲区消息
        with self.buffer_lock:
            if self.message_buffer:
                logger.info(f"处理剩余的 {len(self.message_buffer)} 条消息...")
                self._write_batch_to_file(self.message_buffer)
                self.message_buffer.clear()

        # 关闭RabbitMQ连接
        if self.channel and not self.channel.is_closed:
            self.channel.close()
        if self.connection and not self.connection.is_closed:
            self.connection.close()

        logger.info("日志处理器已停止")

def main():
    """主函数 - 运行单例日志处理器"""
    logger.info("启动RabbitMQ日志处理器程序...")
    
    # 创建并启动日志处理器
    processor = LogProcessor()
    
    try:
        processor.start()
    except Exception as e:
        logger.error(f"日志处理器运行异常: {e}")
    finally:
        processor.stop()

if __name__ == "__main__":
    main()
