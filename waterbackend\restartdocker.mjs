import  * as  fs  from "fs";
import  * as  path  from "path";
import  * as  child_process  from "child_process";
import  {NodeSSH} from 'node-ssh'
(async () => {
    // 拉取docker 
    console.log("拉取docker")
    await pull()

    //重启docker

    console.log("重启docker")
    await stopdocker()
    await removedocker()
    await  startdocker()

})()

function pull() {
    return new Promise((resolve, reject) => {
       let  ssh = new NodeSSH()
         ssh.connect({
            host: 'newtown.xinpanmen.com',
            username: 'ubuntu',
            privateKey: fs.readFileSync('C:/Users/<USER>/.ssh/id_rsa','utf-8')
        }).then(() => {
            ssh.execCommand('docker pull songyi1999/liaoli').then((result) => {
                console.log(result);
                resolve();
            }).catch((error) => {
                console.log(error);
                reject(error);
            });
        }).catch((error) => {
            console.log(error);
            reject(error);
        });
    });
}

function stopdocker() {
    return new Promise((resolve, reject) => {
       let  ssh = new NodeSSH()
         ssh.connect({
            host: 'newtown.xinpanmen.com',
            username: 'ubuntu',
            privateKey: fs.readFileSync('C:/Users/<USER>/.ssh/id_rsa','utf-8')
        }).then(() => {
            ssh.execCommand('docker stop liaoli').then((result) => {
                console.log(result);
                resolve();
            }).catch((error) => {
                console.log(error);
                reject(error);
            });
        }).catch((error) => {
            console.log(error);
            reject(error);
        });
    });
}

function removedocker() {
    return new Promise((resolve, reject) => {
       let  ssh = new NodeSSH()
         ssh.connect({
            host: 'newtown.xinpanmen.com',
            username: 'ubuntu',
            privateKey: fs.readFileSync('C:/Users/<USER>/.ssh/id_rsa','utf-8')
        }).then(() => {
            ssh.execCommand('docker rm  liaoli').then((result) => {
                console.log(result);
                resolve();
            }).catch((error) => {
                console.log(error);
                reject(error);
            });
        }).catch((error) => {
            console.log(error);
            reject(error);
        });
    });
}



function startdocker() {
    return new Promise((resolve, reject) => {
       let  ssh = new NodeSSH()
         ssh.connect({
            host: 'newtown.xinpanmen.com',
            username: 'ubuntu',
            privateKey: fs.readFileSync('C:/Users/<USER>/.ssh/id_rsa','utf-8')
        }).then(() => {
            ssh.execCommand('docker run -d --name liaoli --restart=always -p 8888:8080 -p 9001:9001 -p 9002:9002 -p 9003:9003 -v /home/<USER>/liaoli/client:/usr/src/app/client -v /home/<USER>/database:/usr/src/app/database songyi1999/liaoli node main.js').then((result) => {
                console.log(result);
                resolve();
            }).catch((error) => {
                console.log(error);
                reject(error);
            });
        }).catch((error) => {
            console.log(error);
            reject(error);
        });
    });
}