[根目录](../../../../CLAUDE.md) > [src](../../../) > [baseinfo](../../) > [user](../) > **user**

# 用户管理模块 (User Module)

## 模块职责

提供完整的用户生命周期管理，包括用户CRUD操作、角色权限控制、密码管理等核心功能。

## 入口与启动

- **模块入口**: `user.module.ts`
- **控制器**: `UserController`
- **服务类**: `UserService`
- **数据实体**: `User` (TypeORM)

## 对外接口

### 用户管理接口
- `POST /user` - 创建用户 (管理员权限)
- `GET /user/page` - 分页查询用户列表
- `GET /user/:id/form` - 获取单个用户信息
- `PATCH /user/:id` - 更新用户信息 (管理员)
- `PUT /user/:id` - 完整更新用户
- `DELETE /user/:id` - 删除用户
- `GET /user/count` - 获取用户总数

### 用户自助接口
- `PUT /user/update` - 用户自更新信息
- `POST /user/change-password` - 修改自己的密码
- `GET /user/cancellation` - 注销账户

### 权限管理接口
- `GET /user/options` - 获取角色选项列表

## 关键依赖与配置

### 外部依赖
- `@nestjs/typeorm` - 数据库ORM
- `ts-md5` - 密码加密
- `@nestjs/swagger` - API文档

### 权限配置
```typescript
enum Role {
  User = 'user',          // 普通用户
  Admin = 'admin',        // 管理员  
  SuperAdmin = 'superadmin' // 超级管理员
}
```

### 权限矩阵
- **普通用户**: 仅可修改自己的信息和密码
- **管理员**: 可管理普通用户，不能创建管理员账户
- **超级管理员**: 拥有所有权限

## 数据模型

### User 实体结构
```typescript
class User {
  id: number;                    // 主键
  username: string;              // 用户名 (唯一)
  password: string;              // 加密密码
  roles: string;                 // 角色权限
  lastLoginTime: number;         // 最后登录时间戳
  lastip: string;                // 最后登录IP
  permissions: string[];         // 页面权限列表
}
```

### 密码加密策略
- 算法: MD5 + 固定盐值 '10'
- 格式: `Md5.hashStr(password + '10')`

## 测试与质量

### 测试文件
- `user.controller.spec.ts` - 控制器单元测试
- `user.service.spec.ts` - 服务层单元测试

### 数据验证
- 用户名唯一性约束
- 角色权限验证
- 密码复杂度要求

## 常见问题 (FAQ)

**Q: 如何重置用户密码？**
A: 管理员可通过 PUT /user/:id 接口重置，用户可通过 change-password 自行修改

**Q: 删除用户后数据如何处理？**
A: 目前为物理删除，相关联的数据需要额外处理

**Q: 用户名是否可以修改？**
A: 用户自更新时会过滤掉 username 字段，不允许修改

**Q: 权限控制如何实现？**
A: 通过 @Roles() 装饰器 + RolesGuard 实现接口级权限控制

## 相关文件清单

```
src/baseinfo/user/
├── user.module.ts              # 模块定义
├── user.controller.ts          # REST API控制器
├── user.service.ts             # 业务逻辑服务
├── entities/user.entity.ts     # TypeORM实体定义
├── dto/
│   ├── create-user.dto.ts      # 创建用户DTO
│   └── update-user.dto.ts      # 更新用户DTO
├── user.schema.ts              # 数据模式
├── user.seed.ts                # 种子数据
└── *.spec.ts                   # 单元测试文件
└── *.php                       # 历史遗留文件
```

## 变更记录 (Changelog)
- **2025-08-28**: 初始文档创建，完整梳理用户管理功能和权限体系