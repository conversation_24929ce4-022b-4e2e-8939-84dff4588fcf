{"mysqld_version_id": 80043, "dd_version": 80023, "sdi_version": 80019, "dd_object_type": "Table", "dd_object": {"name": "prepared_statements_instances", "mysql_version_id": 80043, "created": 20250812063400, "last_altered": 20250812063400, "hidden": 1, "options": "avg_row_length=0;key_block_size=0;keys_disabled=0;pack_record=1;server_p_s_table=1;stats_auto_recalc=0;stats_sample_pages=0;", "columns": [{"name": "OBJECT_INSTANCE_BEGIN", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 1, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 2, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "STATEMENT_ID", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 2, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 4, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "STATEMENT_NAME", "type": 16, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 3, "char_length": 256, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 4, "column_type_utf8": "<PERSON><PERSON><PERSON>(64)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "SQL_TEXT", "type": 26, "is_nullable": false, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 4, "char_length": 4294967295, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAAAAAAA", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "longtext", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "OWNER_THREAD_ID", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 5, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 4, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "OWNER_EVENT_ID", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 6, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "OWNER_OBJECT_TYPE", "type": 22, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 7, "char_length": 36, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=5;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 4, "column_type_utf8": "enum('EVENT','FUNCTION','PROCEDURE','TABLE','TRIGGER')", "elements": [{"name": "RVZFTlQ=", "index": 1}, {"name": "RlVOQ1RJT04=", "index": 2}, {"name": "UFJPQ0VEVVJF", "index": 3}, {"name": "VEFCTEU=", "index": 4}, {"name": "VFJJR0dFUg==", "index": 5}], "collation_id": 255, "is_explicit_collation": false}, {"name": "OWNER_OBJECT_SCHEMA", "type": 16, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 8, "char_length": 256, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "<PERSON><PERSON><PERSON>(64)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "OWNER_OBJECT_NAME", "type": 16, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 9, "char_length": 256, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "<PERSON><PERSON><PERSON>(64)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "EXECUTION_ENGINE", "type": 22, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 10, "char_length": 36, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=2;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "enum('PRIMARY','SECONDARY')", "elements": [{"name": "UFJJTUFSWQ==", "index": 1}, {"name": "U0VDT05EQVJZ", "index": 2}], "collation_id": 255, "is_explicit_collation": false}, {"name": "TIMER_PREPARE", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 11, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "COUNT_REPREPARE", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 12, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "COUNT_EXECUTE", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 13, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "SUM_TIMER_EXECUTE", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 14, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "MIN_TIMER_EXECUTE", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 15, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "AVG_TIMER_EXECUTE", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 16, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "MAX_TIMER_EXECUTE", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 17, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "SUM_LOCK_TIME", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 18, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "SUM_ERRORS", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 19, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "SUM_WARNINGS", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 20, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "SUM_ROWS_AFFECTED", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 21, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "SUM_ROWS_SENT", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 22, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "SUM_ROWS_EXAMINED", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 23, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "SUM_CREATED_TMP_DISK_TABLES", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 24, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "SUM_CREATED_TMP_TABLES", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 25, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "SUM_SELECT_FULL_JOIN", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 26, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "SUM_SELECT_FULL_RANGE_JOIN", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 27, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "SUM_SELECT_RANGE", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 28, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "SUM_SELECT_RANGE_CHECK", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 29, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "SUM_SELECT_SCAN", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 30, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "SUM_SORT_MERGE_PASSES", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 31, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "SUM_SORT_RANGE", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 32, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "SUM_SORT_ROWS", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 33, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "SUM_SORT_SCAN", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 34, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "SUM_NO_INDEX_USED", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 35, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "SUM_NO_GOOD_INDEX_USED", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 36, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "SUM_CPU_TIME", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 37, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "MAX_CONTROLLED_MEMORY", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 38, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "MAX_TOTAL_MEMORY", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 39, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "COUNT_SECONDARY", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": true, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 40, "char_length": 20, "numeric_precision": 20, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": true, "default_value_null": false, "srs_id_null": true, "srs_id": 0, "default_value": "AAAAAAAAAAA=", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "bigint unsigned", "elements": [], "collation_id": 255, "is_explicit_collation": false}], "schema_ref": "performance_schema", "se_private_id": 18446744073709551615, "engine": "PERFORMANCE_SCHEMA", "last_checked_for_upgrade_version_id": 0, "comment": "", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "row_format": 2, "partition_type": 0, "partition_expression": "", "partition_expression_utf8": "", "default_partitioning": 0, "subpartition_type": 0, "subpartition_expression": "", "subpartition_expression_utf8": "", "default_subpartitioning": 0, "indexes": [{"name": "PRIMARY", "hidden": false, "is_generated": false, "ordinal_position": 1, "comment": "", "options": "flags=0;", "se_private_data": "", "type": 1, "algorithm": 4, "is_algorithm_explicit": false, "is_visible": true, "engine": "PERFORMANCE_SCHEMA", "engine_attribute": "", "secondary_engine_attribute": "", "elements": [{"ordinal_position": 1, "length": 8, "order": 1, "hidden": false, "column_opx": 0}]}, {"name": "OWNER_THREAD_ID", "hidden": false, "is_generated": false, "ordinal_position": 2, "comment": "", "options": "flags=0;", "se_private_data": "", "type": 2, "algorithm": 4, "is_algorithm_explicit": false, "is_visible": true, "engine": "PERFORMANCE_SCHEMA", "engine_attribute": "", "secondary_engine_attribute": "", "elements": [{"ordinal_position": 1, "length": 8, "order": 1, "hidden": false, "column_opx": 4}, {"ordinal_position": 2, "length": 8, "order": 1, "hidden": false, "column_opx": 5}]}, {"name": "STATEMENT_ID", "hidden": false, "is_generated": false, "ordinal_position": 3, "comment": "", "options": "flags=0;", "se_private_data": "", "type": 3, "algorithm": 4, "is_algorithm_explicit": false, "is_visible": true, "engine": "PERFORMANCE_SCHEMA", "engine_attribute": "", "secondary_engine_attribute": "", "elements": [{"ordinal_position": 1, "length": 8, "order": 1, "hidden": false, "column_opx": 1}]}, {"name": "STATEMENT_NAME", "hidden": false, "is_generated": false, "ordinal_position": 4, "comment": "", "options": "flags=0;", "se_private_data": "", "type": 3, "algorithm": 4, "is_algorithm_explicit": false, "is_visible": true, "engine": "PERFORMANCE_SCHEMA", "engine_attribute": "", "secondary_engine_attribute": "", "elements": [{"ordinal_position": 1, "length": 256, "order": 1, "hidden": false, "column_opx": 2}]}, {"name": "OWNER_OBJECT_TYPE", "hidden": false, "is_generated": false, "ordinal_position": 5, "comment": "", "options": "flags=0;", "se_private_data": "", "type": 3, "algorithm": 4, "is_algorithm_explicit": false, "is_visible": true, "engine": "PERFORMANCE_SCHEMA", "engine_attribute": "", "secondary_engine_attribute": "", "elements": [{"ordinal_position": 1, "length": 1, "order": 1, "hidden": false, "column_opx": 6}, {"ordinal_position": 2, "length": 256, "order": 1, "hidden": false, "column_opx": 7}, {"ordinal_position": 3, "length": 256, "order": 1, "hidden": false, "column_opx": 8}]}], "foreign_keys": [], "check_constraints": [], "partitions": [], "collation_id": 255}}