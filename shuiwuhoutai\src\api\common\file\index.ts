import request from "@/utils/request";
import { FileInfo } from "./model";

class FileAPI {
  // 上传文件
  static upload(file: File) {
    const formData = new FormData();
    formData.append("file", file);
    return request<any, FileInfo>({
      url: "/upload",
      method: "post",
      data: formData,
      headers: {
        Authorization: "Bearer " + localStorage.getItem("TOKEN_KEY"),
        "Content-Type": "multipart/form-data",
      },
    });
  }

  // 删除文件
  static deleteByPath(filePath?: string) {
    return request({
      url: "/file",
      method: "delete",
      params: { filePath: filePath },
    });
  }
}

export default FileAPI;
