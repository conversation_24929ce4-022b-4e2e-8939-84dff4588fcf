"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const requestIp = require("request-ip");
const swagger_1 = require("@nestjs/swagger");
const auth_JwtAuthGuard_1 = require("./auth/auth.JwtAuthGuard");
const auth_service_1 = require("./auth/auth.service");
const app_service_1 = require("./app.service");
const roles_guard_1 = require("./roles.guard");
const roles_decorator_1 = require("./roles.decorator");
const role_enum_1 = require("./role.enum");
const platform_express_1 = require("@nestjs/platform-express");
class loginDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户名', example: 'admin' }),
    __metadata("design:type", String)
], loginDto.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '密码', example: '123456' }),
    __metadata("design:type", String)
], loginDto.prototype, "password", void 0);
let AppController = class AppController {
    constructor(authService, appService) {
        this.authService = authService;
        this.appService = appService;
    }
    async login(loginDto, req) {
        const ip = requestIp.getClientIp(req);
        const result = await this.authService.login(req.user, ip);
        return result;
    }
    async logout(req) {
        await this.authService.logout(req.user);
        return {
            code: 0,
            data: null,
            msg: '登出成功',
        };
    }
    async getProfile(req) {
        return {
            code: 0,
            data: Object.assign(Object.assign({}, req.user), { permissions: req.user.permissions || [], avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif' }),
            msg: '',
        };
    }
    async getRoutesList(req) {
        console.log(req.user);
        const routes = [{}];
        return {
            code: 0,
            data: routes,
            msg: 'ok',
        };
    }
    async uploadFile(file) {
        const result = await this.appService.uploadFile(file);
        return { code: 0, data: result, msg: 'ok' };
    }
};
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '用户登录' }),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('local')),
    (0, common_1.Post)('auth/login'),
    (0, swagger_1.ApiBody)({ type: loginDto }),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AppController.prototype, "login", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '用户登出' }),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(auth_JwtAuthGuard_1.JwtAuthGuard),
    (0, common_1.Post)('auth/logout'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AppController.prototype, "logout", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '返回当前用户id 和姓名' }),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(auth_JwtAuthGuard_1.JwtAuthGuard),
    (0, common_1.Get)('profile'),
    (0, common_1.CacheKey)('user-profile'),
    (0, common_1.CacheTTL)(1),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AppController.prototype, "getProfile", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '获取动态路由' }),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(auth_JwtAuthGuard_1.JwtAuthGuard),
    (0, common_1.Get)('routes'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AppController.prototype, "getRoutesList", null);
__decorate([
    (0, common_1.Post)('upload'),
    (0, common_1.UseGuards)(auth_JwtAuthGuard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.Admin, role_enum_1.Role.SuperAdmin),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '上传文件' }),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    }),
    __param(0, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AppController.prototype, "uploadFile", null);
AppController = __decorate([
    (0, common_1.Controller)(),
    (0, swagger_1.ApiTags)('基本功能'),
    __metadata("design:paramtypes", [auth_service_1.AuthService,
        app_service_1.AppService])
], AppController);
exports.AppController = AppController;
//# sourceMappingURL=app.controller.js.map