/**
 * 登录用户信息
 */
export interface UserInfo {
  userId?: number;
  username?: string;
  nickname?: string;
  avatar?: string;
  roles: string[];
  perms: string[];
}

/**
 * 用户查询对象类型
 */
export interface UserQuery {
  pageNo: number;
  pageSize: number;
  keywords?: string;
}

/**
 * 用户分页对象
 */
export interface UserPageVO {
  /**
   * 用户头像地址
   */
  avatar?: string;
  /**
   * 创建时间
   */
  create_time?: string;
  /**
   * 部门名称
   */
  deptName?: string;
  /**
   * 用户邮箱
   */
  email?: string;
  /**
   * 性别
   */
  genderLabel?: string;
  /**
   * 用户ID
   */
  id?: number;
  /**
   * 手机号
   */
  mobile?: string;
  /**
   * 用户昵称
   */
  nickname?: string;
  /**
   * 角色名称，多个使用英文逗号(,)分割
   */
  roleNames?: string;
  /**
   * 用户状态(1:启用;0:禁用)
   */
  status?: number | string | null;
  /**
   * 用户名
   */
  bot_name?: string;
  description?: string;
  username?: string;
  role?: string;
  admin_id?: number;
  admin_name?: string;
}

/**
 * 用户表单类型
 */
export interface UserForm {
  /**
   * 用户头像
   */
  avatar?: string;
  /**
   * 部门ID
   */
  deptId?: number;
  /**
   * 邮箱
   */
  email?: string;
  /**
   * 性别
   */
  gender?: number;
  /**
   * 用户ID
   */
  id?: number | null;
  mobile?: string;
  /**
   * 昵称
   */
  nickname?: string;
  /**
   * 角色ID集合
   */
  role?: string;
  /**
   * 用户状态(1:正常;0:禁用)
   */
  status?: number;
  /**
   * 用户名
   */
  username?: string;
  /**
   * 密码
   */
  password?: string;
  bot_name?: string;
  description?: string;
  admin_id?: number | string;
  key_api?: string;
}

/**
 * 汇总数据实体
 */
export interface SummaryData {
  /**
   * 主键ID（内部字段）
   */
  id?: number;

  /**
   * 年份
   */
  year: string;

  /**
   * 汇总数据
   */
  income: {
    total: string;
    industry: string;
    useable: string;
  };
  type: {
    items: Array<{
      source: string;
      amount: string;
    }>;
  };
  amount: {
    items: Array<{
      source: string;
      amount: string;
    }>;
  };
  spending: Record<string, string>;

  /**
   * 添加时间（内部字段）
   * @internal
   */
  addTime?: Date;
}
