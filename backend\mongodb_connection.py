# /f:/水利站/backend/mongodb_connection.py
from pymongo import MongoClient
from pymongo.collection import Collection
from pymongo.database import Database
import datetime
from typing import Optional, Dict, Any, List

# MongoDB连接配置
MONGODB_HOST = "localhost"
MONGODB_PORT = 27017
MONGODB_DB = "water_station_realtime"
MONGODB_USERNAME = "water_user"
MONGODB_PASSWORD = "water123"

class MongoDBConnection:
    """MongoDB连接管理类"""
    
    def __init__(self):
        self.client: Optional[MongoClient] = None
        self.db: Optional[Database] = None
        self._connect()
    
    def _connect(self):
        """建立MongoDB连接"""
        try:
            # 构建连接URL
            connection_url = f"mongodb://{MONGODB_USERNAME}:{MONGODB_PASSWORD}@{MONGODB_HOST}:{MONGODB_PORT}/{MONGODB_DB}"
            
            # 创建客户端连接
            self.client = MongoClient(connection_url)
            self.db = self.client[MONGODB_DB]
            
            # 测试连接
            self.client.admin.command('ping')
            print(f"[mongodb] 成功连接到MongoDB数据库: {MONGODB_DB}")
            
        except Exception as e:
            print(f"[mongodb] MongoDB连接失败: {e}")
            raise
    
    def get_collection(self, collection_name: str) -> Collection:
        """获取指定的集合"""
        if self.db is None:
            raise Exception("MongoDB数据库连接未建立")
        return self.db[collection_name]
    
    def close(self):
        """关闭MongoDB连接"""
        if self.client:
            self.client.close()
            print("[mongodb] MongoDB连接已关闭")

# 全局MongoDB连接实例
mongo_conn = MongoDBConnection()

def get_mongodb_collection(collection_name: str) -> Collection:
    """获取MongoDB集合的便捷函数"""
    return mongo_conn.get_collection(collection_name)

# MongoDB操作类
class MongoDBOperations:
    """MongoDB数据操作类"""
    
    @staticmethod
    def create_device_data_log(device_sn: str, raw_data: dict) -> str:
        """
        创建设备数据日志记录
        :param device_sn: 设备序列号
        :param raw_data: 原始数据字典
        :return: 插入记录的ID
        """
        collection = get_mongodb_collection("device_data_5s")
        
        document = {
            "device_sn": device_sn,
            "timestamp": datetime.datetime.now(datetime.timezone.utc),
            "raw_data": raw_data
        }
        
        result = collection.insert_one(document)
        return str(result.inserted_id)
    
    @staticmethod
    def get_device_data_logs(
        device_sn: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
        field_filters: Optional[dict] = None,
        search_keyword: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        查询设备数据日志
        :param device_sn: 设备序列号
        :param skip: 跳过记录数
        :param limit: 返回记录数限制
        :param start_time: 开始时间
        :param end_time: 结束时间
        :param field_filters: 字段过滤条件
        :param search_keyword: 搜索关键字
        :return: 包含总数和数据列表的字典
        """
        collection = get_mongodb_collection("device_data_5s")
        
        # 构建查询条件
        query = {}
        
        if device_sn:
            query["device_sn"] = device_sn
        
        # 时间范围查询
        if start_time or end_time:
            time_query = {}
            if start_time:
                try:
                    start_dt = datetime.datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                    time_query["$gte"] = start_dt
                except ValueError:
                    pass
            if end_time:
                try:
                    end_dt = datetime.datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                    time_query["$lte"] = end_dt
                except ValueError:
                    pass
            if time_query:
                query["timestamp"] = time_query
        
        # 字段过滤
        if field_filters:
            for field_path, value in field_filters.items():
                if '.' in field_path:
                    # 嵌套字段查询，如 "water_pump1.status"
                    query[f"raw_data.{field_path}"] = value
                else:
                    # 顶级字段查询
                    query[f"raw_data.{field_path}"] = value
        
        # 关键字搜索
        if search_keyword:
            query["$text"] = {"$search": search_keyword}
        
        # 获取总数
        total = collection.count_documents(query)
        
        # 获取分页数据
        cursor = collection.find(query).sort("timestamp", -1).skip(skip).limit(limit)
        items = []
        
        for doc in cursor:
            # 将MongoDB文档转换为类似SQLAlchemy的格式
            item = {
                "id": str(doc["_id"]),
                "device_sn": doc["device_sn"],
                "timestamp": doc["timestamp"],
                "raw_data": doc["raw_data"]
            }
            items.append(item)
        
        return {"total": total, "items": items}
    
    @staticmethod
    def create_operation_log(
        operation_type: str,
        operation_details: str,
        execution_status: str = "pending",
        device_sn: Optional[str] = None,
        command_sent: Optional[str] = None,
        error_message: Optional[str] = None,
        additional_data: Optional[dict] = None,
    ) -> str:
        """
        创建操作日志记录
        :return: 插入记录的ID
        """
        collection = get_mongodb_collection("operation_logs")
        
        document = {
            "timestamp": datetime.datetime.now(datetime.timezone.utc),
            "operation_type": operation_type,
            "device_sn": device_sn,
            "operation_details": operation_details,
            "command_sent": command_sent,
            "execution_status": execution_status,
            "error_message": error_message,
            "additional_data": additional_data
        }
        
        result = collection.insert_one(document)
        return str(result.inserted_id)
    
    @staticmethod
    def update_operation_log_status(log_id: str, execution_status: str, error_message: Optional[str] = None) -> bool:
        """
        更新操作日志状态
        :param log_id: 日志记录ID
        :param execution_status: 新的执行状态
        :param error_message: 错误信息
        :return: 更新是否成功
        """
        from bson import ObjectId
        
        collection = get_mongodb_collection("operation_logs")
        
        update_data = {"execution_status": execution_status}
        if error_message:
            update_data["error_message"] = error_message
        
        result = collection.update_one(
            {"_id": ObjectId(log_id)},
            {"$set": update_data}
        )
        
        return result.modified_count > 0
    
    @staticmethod
    def get_operation_logs(
        operation_type: Optional[str] = None,
        device_sn: Optional[str] = None,
        execution_status: Optional[str] = None,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
        search_keyword: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> Dict[str, Any]:
        """
        查询操作日志记录
        :return: 包含总数和数据列表的字典
        """
        collection = get_mongodb_collection("operation_logs")
        
        # 构建查询条件
        query = {}
        
        if operation_type:
            query["operation_type"] = operation_type
        
        if device_sn:
            query["device_sn"] = device_sn
        
        if execution_status:
            query["execution_status"] = execution_status
        
        # 时间范围查询
        if start_time or end_time:
            time_query = {}
            if start_time:
                try:
                    start_dt = datetime.datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                    time_query["$gte"] = start_dt
                except ValueError:
                    pass
            if end_time:
                try:
                    end_dt = datetime.datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                    time_query["$lte"] = end_dt
                except ValueError:
                    pass
            if time_query:
                query["timestamp"] = time_query
        
        # 关键字搜索
        if search_keyword:
            query["$or"] = [
                {"operation_details": {"$regex": search_keyword, "$options": "i"}},
                {"command_sent": {"$regex": search_keyword, "$options": "i"}},
                {"error_message": {"$regex": search_keyword, "$options": "i"}}
            ]
        
        # 获取总数
        total = collection.count_documents(query)
        
        # 获取分页数据
        cursor = collection.find(query).sort("timestamp", -1).skip(skip).limit(limit)
        items = []
        
        for doc in cursor:
            # 将MongoDB文档转换为类似SQLAlchemy的格式
            item = {
                "id": str(doc["_id"]),
                "timestamp": doc["timestamp"],
                "operation_type": doc["operation_type"],
                "device_sn": doc.get("device_sn"),
                "operation_details": doc["operation_details"],
                "command_sent": doc.get("command_sent"),
                "execution_status": doc["execution_status"],
                "error_message": doc.get("error_message"),
                "additional_data": doc.get("additional_data")
            }
            items.append(item)
        
        return {"total": total, "items": items}

# 初始化MongoDB索引
def create_mongodb_indexes():
    """创建MongoDB索引以提高查询性能"""
    try:
        # 设备数据日志索引
        device_logs_collection = get_mongodb_collection("device_data_5s")
        device_logs_collection.create_index("device_sn")
        device_logs_collection.create_index("timestamp")
        device_logs_collection.create_index([("device_sn", 1), ("timestamp", -1)])
        
        # 操作日志索引
        operation_logs_collection = get_mongodb_collection("operation_logs")
        operation_logs_collection.create_index("operation_type")
        operation_logs_collection.create_index("device_sn")
        operation_logs_collection.create_index("timestamp")
        operation_logs_collection.create_index("execution_status")
        
        print("[mongodb] MongoDB索引创建完成")
        
    except Exception as e:
        print(f"[mongodb] 创建索引失败: {e}")