<template>
  <div class="area-site-analysis">
    <el-card shadow="hover" class="page-header">
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="区域选择">
          <el-select
            v-model="searchForm.area"
            placeholder="请选择区域"
            clearable
          >
            <el-option label="浦南区域" value="punan" />
            <el-option label="史北区域" value="shibei" />
            <el-option label="全部区域" value="all" />
          </el-select>
        </el-form-item>

        <el-form-item label="站点状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="正常运行" value="running" />
            <el-option label="维护中" value="maintenance" />
            <el-option label="故障" value="fault" />
            <el-option label="停机" value="stopped" />
          </el-select>
        </el-form-item>

        <el-form-item label="分析类型">
          <el-select
            v-model="searchForm.analysisType"
            placeholder="请选择分析类型"
            clearable
          >
            <el-option label="运行效率分析" value="efficiency" />
            <el-option label="负荷分析" value="load" />
            <el-option label="故障率分析" value="fault_rate" />
            <el-option label="综合评估" value="comprehensive" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="success" @click="exportData">数据导出</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 区域概览统计 -->
    <div class="area-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card punan">
            <div class="area-info">
              <div class="area-name">浦南区域</div>
              <div class="area-stats">
                <div class="stat-item">
                  <span class="stat-label">站点数量:</span>
                  <span class="stat-value">{{
                    areaStats.punan.siteCount
                  }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">运行率:</span>
                  <span class="stat-value"
                    >{{ areaStats.punan.runningRate }}%</span
                  >
                </div>
                <div class="stat-item">
                  <span class="stat-label">平均效率:</span>
                  <span class="stat-value"
                    >{{ areaStats.punan.avgEfficiency }}%</span
                  >
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card shibei">
            <div class="area-info">
              <div class="area-name">史北区域</div>
              <div class="area-stats">
                <div class="stat-item">
                  <span class="stat-label">站点数量:</span>
                  <span class="stat-value">{{
                    areaStats.shibei.siteCount
                  }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">运行率:</span>
                  <span class="stat-value"
                    >{{ areaStats.shibei.runningRate }}%</span
                  >
                </div>
                <div class="stat-item">
                  <span class="stat-label">平均效率:</span>
                  <span class="stat-value"
                    >{{ areaStats.shibei.avgEfficiency }}%</span
                  >
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card total">
            <div class="area-info">
              <div class="area-name">总体概况</div>
              <div class="area-stats">
                <div class="stat-item">
                  <span class="stat-label">总站点数:</span>
                  <span class="stat-value">{{
                    areaStats.total.siteCount
                  }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">总运行率:</span>
                  <span class="stat-value"
                    >{{ areaStats.total.runningRate }}%</span
                  >
                </div>
                <div class="stat-item">
                  <span class="stat-label">整体效率:</span>
                  <span class="stat-value"
                    >{{ areaStats.total.avgEfficiency }}%</span
                  >
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card performance">
            <div class="area-info">
              <div class="area-name">性能指标</div>
              <div class="area-stats">
                <div class="stat-item">
                  <span class="stat-label">优秀站点:</span>
                  <span class="stat-value">{{
                    performanceStats.excellent
                  }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">待优化:</span>
                  <span class="stat-value">{{
                    performanceStats.needOptimization
                  }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">风险站点:</span>
                  <span class="stat-value">{{ performanceStats.risk }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表分析区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>区域站点运行对比</span>
              </div>
            </template>
            <div id="areaComparisonChart" style="height: 400px"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>站点效率分布图</span>
              </div>
            </template>
            <div id="efficiencyDistributionChart" style="height: 400px"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="24">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>站点运行趋势分析</span>
                <el-radio-group v-model="trendType" size="small">
                  <el-radio-button label="efficiency">效率趋势</el-radio-button>
                  <el-radio-button label="load">负荷趋势</el-radio-button>
                  <el-radio-button label="runtime">运行时长</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div id="siteTrendChart" style="height: 400px"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="8">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>站点状态分布</span>
              </div>
            </template>
            <div id="siteStatusChart" style="height: 400px"></div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>区域负荷对比</span>
              </div>
            </template>
            <div id="areaLoadChart" style="height: 400px"></div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>性能评分雷达图</span>
              </div>
            </template>
            <div id="performanceRadarChart" style="height: 400px"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细分析表格 -->
    <el-card shadow="hover" class="table-card">
      <template #header>
        <div class="card-header">
          <span>站点运行分析详情</span>
          <div>
            <el-button size="small" type="warning" @click="batchOptimize"
              >批量优化建议</el-button
            >
            <el-button size="small" type="danger" @click="riskAnalysis"
              >风险分析</el-button
            >
          </div>
        </div>
      </template>

      <el-table :data="siteAnalysisData" stripe style="width: 100%">
        <el-table-column type="selection" width="55" />
        <el-table-column
          prop="siteName"
          label="站点名称"
          width="120"
          fixed="left"
        />
        <el-table-column prop="area" label="所属区域" width="100" />
        <el-table-column prop="siteType" label="站点类型" width="100" />
        <el-table-column prop="runningTime" label="运行时长(h)" width="120" />
        <el-table-column prop="efficiency" label="运行效率(%)" width="100">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.efficiency"
              :color="getEfficiencyColor(scope.row.efficiency)"
              :show-text="false"
            />
            <span style="margin-left: 10px">{{ scope.row.efficiency }}%</span>
          </template>
        </el-table-column>
        <el-table-column prop="loadRate" label="负荷率(%)" width="100" />
        <el-table-column prop="faultCount" label="故障次数" width="100" />
        <el-table-column prop="lastMaintenance" label="上次维保" width="120" />
        <el-table-column prop="status" label="运行状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="riskLevel" label="风险等级" width="100">
          <template #default="scope">
            <el-tag :type="getRiskType(scope.row.riskLevel)">
              {{ scope.row.riskLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="score" label="综合评分" width="100">
          <template #default="scope">
            <el-rate
              v-model="scope.row.score"
              :max="5"
              disabled
              show-score
              text-color="#ff9900"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="200">
          <template #default="scope">
            <el-button size="small" @click="viewSiteDetails(scope.row)"
              >详情</el-button
            >
            <el-button
              size="small"
              type="warning"
              @click="optimizeSite(scope.row)"
              >优化</el-button
            >
            <el-button
              size="small"
              type="primary"
              @click="generateSiteReport(scope.row)"
              >报告</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";

// 搜索表单数据
const searchForm = reactive({
  area: "",
  status: "",
  analysisType: "",
});

// 时间范围
const dateRange = ref([]);

// 趋势图类型
const trendType = ref("efficiency");

// 区域统计数据
const areaStats = reactive({
  punan: {
    siteCount: 28,
    runningRate: 89.3,
    avgEfficiency: 87.5,
  },
  shibei: {
    siteCount: 17,
    runningRate: 82.4,
    avgEfficiency: 83.2,
  },
  total: {
    siteCount: 45,
    runningRate: 86.7,
    avgEfficiency: 85.6,
  },
});

// 性能统计数据
const performanceStats = reactive({
  excellent: 32,
  needOptimization: 10,
  risk: 3,
});

// 站点分析数据
const siteAnalysisData = ref([
  {
    siteName: "浦南一号泵站",
    area: "浦南区域",
    siteType: "取水泵站",
    runningTime: 22.5,
    efficiency: 89,
    loadRate: 78.2,
    faultCount: 1,
    lastMaintenance: "2024-01-15",
    status: "正常运行",
    riskLevel: "低风险",
    score: 4.5,
  },
  {
    siteName: "史北二号泵站",
    area: "史北区域",
    siteType: "污水泵站",
    runningTime: 18.3,
    efficiency: 76,
    loadRate: 82.1,
    faultCount: 3,
    lastMaintenance: "2024-01-10",
    status: "维护中",
    riskLevel: "中风险",
    score: 3.2,
  },
]);

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 45,
});

// 获取效率颜色
const getEfficiencyColor = (efficiency: number) => {
  if (efficiency >= 85) return "#67c23a";
  if (efficiency >= 70) return "#e6a23c";
  return "#f56c6c";
};

// 获取状态标签类型
const getStatusType = (
  status: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const typeMap: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    正常运行: "success",
    维护中: "warning",
    故障: "danger",
    停机: "info",
  };
  return typeMap[status] || "info";
};

// 获取风险等级类型
const getRiskType = (
  riskLevel: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const typeMap: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    低风险: "success",
    中风险: "warning",
    高风险: "danger",
  };
  return typeMap[riskLevel] || "info";
};

// 搜索处理
const handleSearch = () => {
  ElMessage.success("查询成功");
};

// 重置搜索
const resetSearch = () => {
  searchForm.area = "";
  searchForm.status = "";
  searchForm.analysisType = "";
  dateRange.value = [];
  ElMessage.info("已重置搜索条件");
};

// 数据导出
const exportData = () => {
  ElMessage.success("数据导出成功");
};

// 查看站点详情
const viewSiteDetails = (row: any) => {
  ElMessage.info(`查看 ${row.siteName} 的详细信息`);
};

// 优化站点
const optimizeSite = (row: any) => {
  ElMessage.success(`正在为 ${row.siteName} 生成优化建议`);
};

// 生成站点报告
const generateSiteReport = (row: any) => {
  ElMessage.success(`正在为 ${row.siteName} 生成分析报告`);
};

// 批量优化建议
const batchOptimize = () => {
  ElMessage.success("批量优化建议生成中");
};

// 风险分析
const riskAnalysis = () => {
  ElMessage.warning("风险分析报告生成中");
};

// 分页处理
const handleSizeChange = (size: any) => {
  pagination.pageSize = size;
};

const handleCurrentChange = (page: any) => {
  pagination.currentPage = page;
};

// 组件挂载
onMounted(() => {
  initCharts();
});

// 初始化图表
const initCharts = () => {
  console.log("初始化区域站点运行分析图表");
};
</script>

<style scoped>
.area-site-analysis {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.search-form {
  margin-bottom: 0;
}

.area-overview {
  margin-bottom: 20px;
}

.overview-card {
  cursor: pointer;
  transition: all 0.3s;
  border-left: 4px solid;
}

.overview-card.punan {
  border-left-color: #67c23a;
}

.overview-card.shibei {
  border-left-color: #409eff;
}

.overview-card.total {
  border-left-color: #e6a23c;
}

.overview-card.performance {
  border-left-color: #f56c6c;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.area-info {
  padding: 10px 0;
}

.area-name {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 15px;
  text-align: center;
}

.area-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-card {
  height: 500px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
