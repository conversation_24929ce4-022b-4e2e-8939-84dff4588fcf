import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { DeviceDataHour } from '../entities/device-data-hour.entity';

@Injectable()
export class DeviceDataHourService {
  constructor(
    @InjectRepository(DeviceDataHour)
    private readonly deviceDataHourRepository: Repository<DeviceDataHour>,
  ) {}

  async findAll(): Promise<DeviceDataHour[]> {
    return this.deviceDataHourRepository.find({
      order: { timestamp: 'DESC' }
    });
  }

  async findByDeviceSn(deviceSn: string): Promise<DeviceDataHour[]> {
    return this.deviceDataHourRepository.find({
      where: { deviceSn },
      order: { timestamp: 'DESC' }
    });
  }

  async findByTimeRange(
    startTime: Date, 
    endTime: Date, 
    deviceSn?: string
  ): Promise<DeviceDataHour[]> {
    const where: any = {
      timestamp: Between(startTime, endTime)
    };
    
    if (deviceSn) {
      where.deviceSn = deviceSn;
    }

    return this.deviceDataHourRepository.find({
      where,
      order: { timestamp: 'ASC' }
    });
  }

  async getLatestData(deviceSn?: string): Promise<DeviceDataHour[]> {
    const where: any = {};
    if (deviceSn) {
      where.deviceSn = deviceSn;
    }

    return this.deviceDataHourRepository.find({
      where,
      order: { timestamp: 'DESC' },
      take: 24
    });
  }

  async getDataStatistics(deviceSn?: string) {
    const where: any = {};
    if (deviceSn) {
      where.deviceSn = deviceSn;
    }

    const [count, avgSampleCount] = await Promise.all([
      this.deviceDataHourRepository.count({ where }),
      this.deviceDataHourRepository
        .createQueryBuilder('data')
        .select('AVG(data.original_sample_count)', 'avg')
        .where(deviceSn ? 'data.device_sn = :deviceSn' : '1=1', deviceSn ? { deviceSn } : {})
        .getRawOne()
    ]);

    return {
      totalRecords: count,
      averageOriginalSampleCount: parseFloat(avgSampleCount.avg || '0')
    };
  }
}