<template>
  <div class="facility-alarm-statistics">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-button type="primary" @click="refreshData">刷新</el-button>
        </div>
      </template>

      <!-- 统计概览 -->
      <el-row :gutter="20" style="margin-bottom: 20px">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ totalAlarms }}</div>
              <div class="stat-label">总告警数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number urgent">{{ urgentAlarms }}</div>
              <div class="stat-label">紧急告警</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number important">{{ importantAlarms }}</div>
              <div class="stat-label">重要告警</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number normal">{{ normalAlarms }}</div>
              <div class="stat-label">一般告警</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 设施告警统计表格 -->
      <el-table
        :data="facilityStatistics"
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column prop="facilityName" label="设施名称" />
        <el-table-column prop="facilityType" label="设施类型" width="120" />
        <el-table-column prop="totalCount" label="总告警数" width="100" />
        <el-table-column prop="urgentCount" label="紧急" width="80">
          <template #default="scope">
            <span class="urgent">{{ scope.row.urgentCount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="importantCount" label="重要" width="80">
          <template #default="scope">
            <span class="important">{{ scope.row.importantCount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="normalCount" label="一般" width="80">
          <template #default="scope">
            <span class="normal">{{ scope.row.normalCount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="resolvedRate" label="处理率" width="100">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.resolvedRate"
              :stroke-width="8"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(scope.row)"
              >查看详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";

// 统计数据
const totalAlarms = ref(156);
const urgentAlarms = ref(12);
const importantAlarms = ref(45);
const normalAlarms = ref(99);

// 设施告警统计数据
const facilityStatistics = ref([
  {
    facilityName: "泵站001",
    facilityType: "泵站",
    totalCount: 23,
    urgentCount: 3,
    importantCount: 8,
    normalCount: 12,
    resolvedRate: 85,
  },
  {
    facilityName: "管网A区",
    facilityType: "管网",
    totalCount: 45,
    urgentCount: 5,
    importantCount: 15,
    normalCount: 25,
    resolvedRate: 75,
  },
]);

const loading = ref(false);

// 刷新数据
const refreshData = () => {
  loadData();
};

// 查看详情
const handleViewDetail = (row: any) => {
  console.log("查看设施告警详情:", row);
};

// 加载数据
const loadData = () => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 1000);
};

onMounted(() => {
  loadData();
});
</script>

<style scoped>
.facility-alarm-statistics {
  padding: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.stat-card {
  text-align: center;
}
.stat-content {
  padding: 20px;
}
.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
}
.stat-number.urgent {
  color: #f56c6c;
}
.stat-number.important {
  color: #e6a23c;
}
.stat-number.normal {
  color: #67c23a;
}
.stat-label {
  margin-top: 10px;
  color: #666;
}
.urgent {
  color: #f56c6c;
  font-weight: bold;
}
.important {
  color: #e6a23c;
  font-weight: bold;
}
.normal {
  color: #67c23a;
  font-weight: bold;
}
</style>
