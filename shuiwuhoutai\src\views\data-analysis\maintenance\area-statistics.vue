<template>
  <div class="area-maintenance-statistics">
    <el-card shadow="hover" class="page-header">
      <template #header>
        <div class="card-header">
          <div class="header-actions">
            <el-button
              type="primary"
              size="small"
              @click="generateAreaStatistics"
              >生成区域统计</el-button
            >
            <el-button type="success" size="small" @click="compareAreas"
              >区域对比</el-button
            >
          </div>
        </div>
      </template>

      <!-- 统计配置表单 -->
      <el-form :inline="true" :model="statisticsForm" class="statistics-form">
        <el-form-item label="选择区域">
          <el-select
            v-model="statisticsForm.areas"
            placeholder="请选择区域"
            multiple
            clearable
          >
            <el-option label="浦南区域" value="punan" />
            <el-option label="史北区域" value="shibei" />
            <el-option label="东部区域" value="east" />
            <el-option label="西部区域" value="west" />
          </el-select>
        </el-form-item>

        <el-form-item label="统计时间">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="统计指标">
          <el-select
            v-model="statisticsForm.metrics"
            placeholder="请选择指标"
            multiple
          >
            <el-option label="维保频次" value="frequency" />
            <el-option label="维保成本" value="cost" />
            <el-option label="设备可靠性" value="reliability" />
            <el-option label="维保效率" value="efficiency" />
            <el-option label="故障率" value="failure_rate" />
          </el-select>
        </el-form-item>

        <el-form-item label="统计维度">
          <el-select
            v-model="statisticsForm.dimension"
            placeholder="请选择维度"
          >
            <el-option label="按月统计" value="monthly" />
            <el-option label="按季度统计" value="quarterly" />
            <el-option label="按年统计" value="yearly" />
            <el-option label="按设备类型" value="equipment_type" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 区域维保概览 -->
    <div class="area-maintenance-overview">
      <el-row :gutter="20">
        <el-col
          :span="6"
          v-for="(area, index) in areaMaintenanceOverview"
          :key="index"
        >
          <el-card shadow="hover" :class="['area-maintenance-card', area.code]">
            <div class="area-header">
              <div class="area-name">{{ area.name }}</div>
              <div class="area-score">
                <el-tag :type="getScoreType(area.overallScore)" size="small">
                  {{ area.overallScore }}分
                </el-tag>
              </div>
            </div>
            <div class="area-metrics">
              <div class="metric-item">
                <span class="metric-label">设备数量:</span>
                <span class="metric-value">{{ area.equipmentCount }}</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">维保次数:</span>
                <span class="metric-value">{{ area.maintenanceCount }}</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">维保成本:</span>
                <span class="metric-value">{{ area.maintenanceCost }}万</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">可靠性:</span>
                <span class="metric-value">{{ area.reliability }}%</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">同比变化:</span>
                <span
                  :class="[
                    'metric-value',
                    area.yearOnYearChange >= 0 ? 'increase' : 'decrease',
                  ]"
                >
                  {{ area.yearOnYearChange >= 0 ? "+" : ""
                  }}{{ area.yearOnYearChange }}%
                </span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 区域维保统计图表 -->
    <div class="area-statistics-charts">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>区域维保趋势对比</span>
                <el-select v-model="trendMetric" size="small">
                  <el-option label="维保频次" value="frequency" />
                  <el-option label="维保成本" value="cost" />
                  <el-option label="设备可靠性" value="reliability" />
                </el-select>
              </div>
            </template>
            <div id="areaMaintenanceTrendChart" style="height: 350px"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>区域维保类型分布</span>
              </div>
            </template>
            <div id="areaMaintenanceTypeChart" style="height: 350px"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="8">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>区域维保成本对比</span>
              </div>
            </template>
            <div id="areaMaintenanceCostChart" style="height: 350px"></div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>区域设备可靠性</span>
              </div>
            </template>
            <div id="areaReliabilityChart" style="height: 350px"></div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>区域维保效率分析</span>
              </div>
            </template>
            <div id="areaEfficiencyChart" style="height: 350px"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 区域维保排行榜 -->
    <el-row :gutter="20" style="margin-bottom: 20px">
      <el-col :span="8">
        <el-card shadow="hover" class="ranking-card">
          <template #header>
            <div class="card-header">
              <span>维保频次排行</span>
              <el-tag type="info" size="small">次数</el-tag>
            </div>
          </template>
          <div class="ranking-list">
            <div
              v-for="(item, index) in maintenanceFrequencyRanking"
              :key="index"
              class="ranking-item"
            >
              <div class="ranking-number" :class="'rank-' + (index + 1)">
                {{ index + 1 }}
              </div>
              <div class="ranking-content">
                <div class="ranking-name">{{ item.areaName }}</div>
                <div class="ranking-value">{{ item.frequency }} 次</div>
              </div>
              <div class="ranking-badge">
                <el-tag :type="getRankingBadgeType(index)" size="small">
                  {{ getRankingBadgeLabel(index) }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover" class="ranking-card">
          <template #header>
            <div class="card-header">
              <span>可靠性排行</span>
              <el-tag type="success" size="small">%</el-tag>
            </div>
          </template>
          <div class="ranking-list">
            <div
              v-for="(item, index) in reliabilityRanking"
              :key="index"
              class="ranking-item"
            >
              <div class="ranking-number" :class="'rank-' + (index + 1)">
                {{ index + 1 }}
              </div>
              <div class="ranking-content">
                <div class="ranking-name">{{ item.areaName }}</div>
                <div class="ranking-value">{{ item.reliability }}%</div>
              </div>
              <div class="ranking-badge">
                <el-tag type="success" size="small">优秀</el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover" class="ranking-card">
          <template #header>
            <div class="card-header">
              <span>成本效益排行</span>
              <el-tag type="warning" size="small">性价比</el-tag>
            </div>
          </template>
          <div class="ranking-list">
            <div
              v-for="(item, index) in costEfficiencyRanking"
              :key="index"
              class="ranking-item"
            >
              <div class="ranking-number" :class="'rank-' + (index + 1)">
                {{ index + 1 }}
              </div>
              <div class="ranking-content">
                <div class="ranking-name">{{ item.areaName }}</div>
                <div class="ranking-value">{{ item.costEfficiency }}</div>
              </div>
              <div class="ranking-badge">
                <el-tag type="success" size="small">高效</el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 区域维保详细统计表格 -->
    <el-card shadow="hover" class="table-card">
      <template #header>
        <div class="card-header">
          <span>区域维保统计详情</span>
          <div class="table-actions">
            <el-button size="small" @click="exportAreaStatistics"
              >导出统计</el-button
            >
            <el-button size="small" type="warning" @click="setMaintenanceTarget"
              >设置目标</el-button
            >
            <el-button
              size="small"
              type="primary"
              @click="optimizeAreaMaintenance"
              >优化建议</el-button
            >
          </div>
        </div>
      </template>

      <el-table :data="areaStatisticsData" stripe style="width: 100%" border>
        <el-table-column type="selection" width="55" />
        <el-table-column
          prop="areaName"
          label="区域名称"
          width="100"
          fixed="left"
        />
        <el-table-column prop="stationCount" label="站点数量" width="80" />
        <el-table-column prop="equipmentCount" label="设备数量" width="80" />
        <el-table-column
          prop="totalMaintenanceCount"
          label="总维保次数"
          width="120"
        />
        <el-table-column
          prop="preventiveCount"
          label="预防性维保"
          width="120"
        />
        <el-table-column prop="correctiveCount" label="故障维修" width="100" />
        <el-table-column prop="plannedCount" label="计划维保" width="100" />
        <el-table-column prop="emergencyCount" label="应急维修" width="100" />
        <el-table-column prop="totalCost" label="总成本(万元)" width="120" />
        <el-table-column
          prop="avgCostPerDevice"
          label="设备均成本"
          width="120"
        />
        <el-table-column prop="totalDuration" label="总时长(h)" width="100" />
        <el-table-column prop="avgDuration" label="平均时长(h)" width="120" />
        <el-table-column prop="reliability" label="可靠性(%)" width="100">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.reliability"
              :color="getReliabilityColor(scope.row.reliability)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="efficiency" label="维保效率(%)" width="120">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.efficiency"
              :color="getEfficiencyColor(scope.row.efficiency)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="failureRate" label="故障率(%)" width="100" />
        <el-table-column prop="mtbf" label="MTBF(天)" width="100" />
        <el-table-column prop="mttr" label="MTTR(h)" width="100" />
        <el-table-column prop="performanceScore" label="综合评分" width="100">
          <template #default="scope">
            <el-rate
              v-model="scope.row.performanceScore"
              :max="5"
              disabled
              show-score
              text-color="#ff9900"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="200">
          <template #default="scope">
            <el-button size="small" @click="viewAreaDetail(scope.row)"
              >详情</el-button
            >
            <el-button
              size="small"
              type="warning"
              @click="optimizeArea(scope.row)"
              >优化</el-button
            >
            <el-button
              size="small"
              type="primary"
              @click="generateAreaReport(scope.row)"
              >报告</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[5, 10, 20, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 区域维保分析结论 -->
    <el-card shadow="hover" class="analysis-conclusion-card">
      <template #header>
        <div class="card-header">
          <span>区域维保分析结论</span>
          <span class="analysis-time">分析时间: {{ analysisTime }}</span>
        </div>
      </template>

      <el-row :gutter="30">
        <el-col :span="8">
          <div class="conclusion-section">
            <h4>📊 维保现状</h4>
            <ul class="status-list">
              <li v-for="(status, index) in maintenanceStatus" :key="index">
                {{ status }}
              </li>
            </ul>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="conclusion-section">
            <h4>⚠️ 问题识别</h4>
            <ul class="problem-list">
              <li v-for="(problem, index) in identifiedProblems" :key="index">
                {{ problem }}
              </li>
            </ul>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="conclusion-section">
            <h4>🎯 改进建议</h4>
            <ul class="improvement-list">
              <li
                v-for="(improvement, index) in improvementSuggestions"
                :key="index"
              >
                {{ improvement }}
              </li>
            </ul>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";

// 统计配置表单
const statisticsForm = reactive({
  areas: ["punan", "shibei", "east"],
  metrics: ["frequency", "cost", "reliability"],
  dimension: "monthly",
});

// 时间范围
const dateRange = ref([]);

// 图表配置
const trendMetric = ref("frequency");

// 分析时间
const analysisTime = ref("2024-01-20 14:30:25");

// 区域维保概览数据
const areaMaintenanceOverview = ref([
  {
    code: "punan",
    name: "浦南区域",
    overallScore: 88,
    equipmentCount: 156,
    maintenanceCount: 98,
    maintenanceCost: 45.6,
    reliability: 92.3,
    yearOnYearChange: -5.2,
  },
  {
    code: "shibei",
    name: "史北区域",
    overallScore: 82,
    equipmentCount: 124,
    maintenanceCount: 86,
    maintenanceCost: 52.8,
    reliability: 89.1,
    yearOnYearChange: 8.3,
  },
  {
    code: "east",
    name: "东部区域",
    overallScore: 90,
    equipmentCount: 98,
    maintenanceCount: 62,
    maintenanceCost: 32.4,
    reliability: 94.5,
    yearOnYearChange: -2.1,
  },
  {
    code: "west",
    name: "西部区域",
    overallScore: 85,
    equipmentCount: 112,
    maintenanceCount: 74,
    maintenanceCost: 38.9,
    reliability: 91.2,
    yearOnYearChange: 3.7,
  },
]);

// 排行榜数据
const maintenanceFrequencyRanking = ref([
  { areaName: "浦南区域", frequency: 98 },
  { areaName: "史北区域", frequency: 86 },
  { areaName: "西部区域", frequency: 74 },
  { areaName: "东部区域", frequency: 62 },
]);

const reliabilityRanking = ref([
  { areaName: "东部区域", reliability: 94.5 },
  { areaName: "浦南区域", reliability: 92.3 },
  { areaName: "西部区域", reliability: 91.2 },
  { areaName: "史北区域", reliability: 89.1 },
]);

const costEfficiencyRanking = ref([
  { areaName: "东部区域", costEfficiency: "A+" },
  { areaName: "浦南区域", costEfficiency: "A" },
  { areaName: "西部区域", costEfficiency: "A-" },
  { areaName: "史北区域", costEfficiency: "B+" },
]);

// 区域统计详细数据
const areaStatisticsData = ref([
  {
    areaName: "浦南区域",
    stationCount: 28,
    equipmentCount: 156,
    totalMaintenanceCount: 98,
    preventiveCount: 72,
    correctiveCount: 18,
    plannedCount: 6,
    emergencyCount: 2,
    totalCost: 45.6,
    avgCostPerDevice: 0.292,
    totalDuration: 392.5,
    avgDuration: 4.0,
    reliability: 92,
    efficiency: 88,
    failureRate: 2.3,
    mtbf: 165,
    mttr: 3.2,
    performanceScore: 4.4,
  },
  {
    areaName: "史北区域",
    stationCount: 17,
    equipmentCount: 124,
    totalMaintenanceCount: 86,
    preventiveCount: 58,
    correctiveCount: 22,
    plannedCount: 4,
    emergencyCount: 2,
    totalCost: 52.8,
    avgCostPerDevice: 0.426,
    totalDuration: 448.3,
    avgDuration: 5.2,
    reliability: 89,
    efficiency: 82,
    failureRate: 3.1,
    mtbf: 142,
    mttr: 4.1,
    performanceScore: 4.1,
  },
]);

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 4,
});

// 维保现状
const maintenanceStatus = ref([
  "各区域维保频次总体保持稳定，预防性维保占比较高",
  "东部区域设备可靠性最高，达94.5%",
  "史北区域维保成本相对较高，需要优化",
  "整体维保效率良好，平均完成率超过85%",
]);

// 问题识别
const identifiedProblems = ref([
  "史北区域维保成本偏高，单位成本比平均值高30%",
  "部分区域故障维修比例偏高，预防性维保待加强",
  "维保人员配置不均，影响维保及时性",
  "备件管理有待改善，影响维保效率",
]);

// 改进建议
const improvementSuggestions = ref([
  "优化史北区域维保策略，降低维保成本",
  "加强预防性维保，减少故障性维修",
  "统筹配置维保人员，提高区域协调",
  "建立智能维保管理系统，提升整体效率",
]);

// 获取评分类型
const getScoreType = (score) => {
  if (score >= 90) return "success";
  if (score >= 80) return "warning";
  return "danger";
};

// 获取排行徽章类型
const getRankingBadgeType = (index) => {
  if (index === 0) return "success";
  if (index === 1) return "warning";
  return "info";
};

const getRankingBadgeLabel = (index) => {
  if (index === 0) return "领先";
  if (index === 1) return "良好";
  return "一般";
};

// 获取可靠性颜色
const getReliabilityColor = (reliability) => {
  if (reliability >= 90) return "#67c23a";
  if (reliability >= 80) return "#e6a23c";
  return "#f56c6c";
};

// 获取效率颜色
const getEfficiencyColor = (efficiency) => {
  if (efficiency >= 85) return "#67c23a";
  if (efficiency >= 70) return "#e6a23c";
  return "#f56c6c";
};

// 查询处理
const handleQuery = () => {
  ElMessage.success("查询成功");
};

// 重置表单
const resetForm = () => {
  statisticsForm.areas = ["punan", "shibei", "east"];
  statisticsForm.metrics = ["frequency", "cost", "reliability"];
  statisticsForm.dimension = "monthly";
  dateRange.value = [];
  ElMessage.info("已重置查询条件");
};

// 生成区域统计
const generateAreaStatistics = () => {
  ElMessage.success("区域统计报表生成中，请稍候...");
};

// 区域对比
const compareAreas = () => {
  ElMessage.info("区域对比分析功能开发中");
};

// 查看区域详情
const viewAreaDetail = (row) => {
  ElMessage.info(`查看 ${row.areaName} 的详细信息`);
};

// 优化区域
const optimizeArea = (row) => {
  ElMessage.success(`正在为 ${row.areaName} 生成优化建议`);
};

// 生成区域报告
const generateAreaReport = (row) => {
  ElMessage.success(`正在为 ${row.areaName} 生成详细报告`);
};

// 导出区域统计
const exportAreaStatistics = () => {
  ElMessage.success("区域统计导出成功");
};

// 设置维保目标
const setMaintenanceTarget = () => {
  ElMessage.info("维保目标设置功能开发中");
};

// 优化区域维保
const optimizeAreaMaintenance = () => {
  ElMessage.success("区域维保优化建议生成中");
};

// 分页处理
const handleSizeChange = (size) => {
  pagination.pageSize = size;
};

const handleCurrentChange = (page) => {
  pagination.currentPage = page;
};

// 组件挂载
onMounted(() => {
  initCharts();
});

// 初始化图表
const initCharts = () => {
  console.log("初始化区域维保统计图表");
};
</script>

<style scoped>
.area-maintenance-statistics {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.statistics-form {
  margin-bottom: 0;
}

.area-maintenance-overview {
  margin-bottom: 20px;
}

.area-maintenance-card {
  cursor: pointer;
  transition: all 0.3s;
  border-left: 4px solid;
}

.area-maintenance-card.punan {
  border-left-color: #67c23a;
}

.area-maintenance-card.shibei {
  border-left-color: #e6a23c;
}

.area-maintenance-card.east {
  border-left-color: #409eff;
}

.area-maintenance-card.west {
  border-left-color: #f56c6c;
}

.area-maintenance-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.area-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.area-name {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.area-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-size: 14px;
  color: #606266;
}

.metric-value {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.metric-value.increase {
  color: #f56c6c;
}

.metric-value.decrease {
  color: #67c23a;
}

.area-statistics-charts {
  margin-bottom: 20px;
}

.chart-card {
  min-height: 450px;
}

.ranking-card {
  height: 400px;
  margin-bottom: 20px;
}

.ranking-list {
  padding: 10px 0;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-number {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  border-radius: 50%;
  margin-right: 15px;
}

.rank-1 {
  background-color: #ffd700;
  color: #fff;
}

.rank-2 {
  background-color: #c0c0c0;
  color: #fff;
}

.rank-3 {
  background-color: #cd7f32;
  color: #fff;
}

.rank-4 {
  background-color: #f5f5f5;
  color: #666;
}

.ranking-content {
  flex: 1;
}

.ranking-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.ranking-value {
  font-size: 14px;
  color: #666;
}

.ranking-badge {
  margin-left: 10px;
}

.table-card {
  margin-bottom: 20px;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.analysis-conclusion-card {
  margin-bottom: 20px;
}

.analysis-time {
  font-size: 14px;
  color: #666;
  background: #f5f5f5;
  padding: 4px 12px;
  border-radius: 4px;
}

.conclusion-section h4 {
  color: #303133;
  margin-bottom: 15px;
  font-size: 16px;
}

.status-list,
.problem-list,
.improvement-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.status-list li,
.problem-list li,
.improvement-list li {
  margin-bottom: 12px;
  font-size: 14px;
  line-height: 1.5;
  padding-left: 15px;
  position: relative;
}

.status-list li::before {
  content: "📊";
  position: absolute;
  left: 0;
}

.problem-list li::before {
  content: "⚠️";
  position: absolute;
  left: 0;
}

.improvement-list li::before {
  content: "🎯";
  position: absolute;
  left: 0;
}
</style>
