<template>
  <div class="work-order-statistics">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <el-button @click="handleRefresh">刷新数据</el-button>
        </div>
      </template>

      <!-- 时间选择器 -->
      <div class="time-selector">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="handleDateChange"
        />
        <el-radio-group v-model="timeType" @change="handleTimeTypeChange">
          <el-radio-button label="day">按天</el-radio-button>
          <el-radio-button label="week">按周</el-radio-button>
          <el-radio-button label="month">按月</el-radio-button>
        </el-radio-group>
      </div>

      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
          <el-card class="stat-card total">
            <div class="stat-item">
              <div class="stat-icon">
                <el-icon size="24"><Document /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ totalStats.total }}</div>
                <div class="stat-label">总工单数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card created">
            <div class="stat-item">
              <div class="stat-icon">
                <el-icon size="24"><Plus /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ totalStats.created }}</div>
                <div class="stat-label">新建工单</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card processing">
            <div class="stat-item">
              <div class="stat-icon">
                <el-icon size="24"><Loading /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ totalStats.processing }}</div>
                <div class="stat-label">处理中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card completed">
            <div class="stat-item">
              <div class="stat-icon">
                <el-icon size="24"><Check /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ totalStats.completed }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 图表区域 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <span>工单数量趋势</span>
            </template>
            <div id="trendChart" class="chart-container"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <span>工单类型分布</span>
            </template>
            <div id="pieChart" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 详细统计表格 -->
      <el-card class="table-card">
        <template #header>
          <span>详细统计数据</span>
        </template>
        <el-table :data="statisticsData" stripe>
          <el-table-column prop="date" label="日期" />
          <el-table-column prop="created" label="新建" />
          <el-table-column prop="processing" label="处理中" />
          <el-table-column prop="completed" label="已完成" />
          <el-table-column prop="total" label="总计" />
          <el-table-column prop="completionRate" label="完成率">
            <template #default="{ row }">
              <el-progress
                :percentage="row.completionRate"
                :color="getProgressColor(row.completionRate)"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { Document, Plus, Loading, Check } from "@element-plus/icons-vue";

// 时间范围
const dateRange = ref([]);
const timeType = ref("day");

// 总统计数据
const totalStats = reactive({
  total: 1256,
  created: 45,
  processing: 67,
  completed: 189,
});

// 统计数据
const statisticsData = ref([
  {
    date: "2024-01-01",
    created: 12,
    processing: 8,
    completed: 15,
    total: 35,
    completionRate: 75,
  },
  {
    date: "2024-01-02",
    created: 8,
    processing: 12,
    completed: 18,
    total: 38,
    completionRate: 82,
  },
  {
    date: "2024-01-03",
    created: 15,
    processing: 6,
    completed: 12,
    total: 33,
    completionRate: 68,
  },
]);

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage >= 80) return "#67C23A";
  if (percentage >= 60) return "#E6A23C";
  return "#F56C6C";
};

// 日期范围改变
const handleDateChange = (dates) => {
  console.log("日期范围改变", dates);
  loadStatistics();
};

// 时间类型改变
const handleTimeTypeChange = (type) => {
  console.log("时间类型改变", type);
  loadStatistics();
};

// 刷新数据
const handleRefresh = () => {
  console.log("刷新数据");
  loadStatistics();
};

// 加载统计数据
const loadStatistics = () => {
  // 模拟加载数据
  console.log("加载统计数据");
};

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 这里可以集成 ECharts 或其他图表库
    console.log("初始化图表");
  });
};

onMounted(() => {
  // 设置默认时间范围（最近7天）
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - 6);

  dateRange.value = [
    startDate.toISOString().split("T")[0],
    endDate.toISOString().split("T")[0],
  ];

  loadStatistics();
  initCharts();
});
</script>

<style scoped>
.work-order-statistics {
  padding: 20px;
}

.page-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.time-selector {
  margin-bottom: 20px;
  display: flex;
  gap: 20px;
  align-items: center;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 8px;
  overflow: hidden;
}

.stat-card.total {
  border-left: 4px solid #409eff;
}

.stat-card.created {
  border-left: 4px solid #67c23a;
}

.stat-card.processing {
  border-left: 4px solid #e6a23c;
}

.stat-card.completed {
  border-left: 4px solid #f56c6c;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stat-icon {
  margin-right: 15px;
  color: #409eff;
}

.stat-card.created .stat-icon {
  color: #67c23a;
}

.stat-card.processing .stat-icon {
  color: #e6a23c;
}

.stat-card.completed .stat-icon {
  color: #f56c6c;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  border-radius: 4px;
  color: #999;
}

.table-card {
  margin-top: 20px;
}
</style>
