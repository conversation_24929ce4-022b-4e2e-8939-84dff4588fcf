const net = require('net');
const http = require('http');
const url = require('url');

const HOST = '0.0.0.0';
const PORT = 8359;
const TARGET_URL = 'http://pjy.xinpanmen.com:9002/lotwater';
// const TARGET_URL = 'http://localhost:9001/lotwater';
//const  TARGET_URL="http://jscn.sz-hgy.com/test.php" // 测试地址

function delay(s) {
  return new Promise((resolve) => {
    setTimeout(resolve, s * 1000);
  });
}

function sendData(data) {
  return new Promise((resolve, reject) => {
    const parsedUrl = url.parse(TARGET_URL);
    const options = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port,
      path: parsedUrl.path,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const req = http.request(options, (res) => {
      console.log(`statusCode: ${res.statusCode}`);
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      res.on('end', () => {
        console.log('Response from target:', responseData);
        resolve(responseData);
      });
    });

    req.on('error', (error) => {
      console.error('Error sending data:', error);
      reject(error);
    });

    const jsonData = { data: data };
    req.write(JSON.stringify(jsonData));
    req.end();
  });
}

function isCompleteMessage(message) {
  if (message.includes('www.usr.cn##') && message.endsWith('www.usr.cn')) {
    return true;
  }

  if (message.startsWith('##') && message.includes('w01009-Flag=')) {
    return true;
  }

  return false;
}

function extractMessage(buffer) {
  if (buffer.includes('www.usr.cn##')) {
    return {
      message: buffer,
      remainingBuffer: buffer,
    };
  } else if (buffer.startsWith('##')) {
    const endIndex = buffer.indexOf('w01009-Flag=') + 'w01009-Flag=N'.length;
    return {
      message: buffer.slice(0, endIndex),
      remainingBuffer: buffer.slice(endIndex),
    };
  }
  return null;
}

net
  .createServer(function (sock) {
    let buffer = '';

    sock.on('data', async function (data) {
      buffer += data.toString().trim();
      console.log('\nReceived data:', data.toString().trim());
      console.log('\nCurrent buffer:', buffer);

      while (buffer.length > 0) {
        if (isCompleteMessage(buffer)) {
          console.log('Message is complete');
          const result = extractMessage(buffer);
          if (result) {
            const { message, remainingBuffer } = result;
            buffer = remainingBuffer;

            console.log(
              'Complete message received, sending to target:',
              message,
            );
            try {
              await sendData(message);
              buffer = '';
              console.log('Data sent successfully');
            } catch (error) {
              console.error('Failed to send data:', error);
            }
          } else {
            break;
          }
        } else {
          break;
        }
      }
    });

    sock.on('close', function (data) {
      console.log('CLOSED: ' + sock.remoteAddress + ' ' + sock.remotePort);
    });
  })
  .listen(PORT, HOST);

console.log(`Server listening on ${HOST}:${PORT}`);
