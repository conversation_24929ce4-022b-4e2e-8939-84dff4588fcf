<template>
  <div class="organization-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="header-actions">
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              新增部门
            </el-button>
            <el-button @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form">
        <el-form :model="searchForm" inline>
          <el-form-item label="部门名称">
            <el-input
              v-model="searchForm.departmentName"
              placeholder="请输入部门名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 120px"
            >
              <el-option label="启用" value="1" />
              <el-option label="禁用" value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 组织架构树 -->
      <div class="organization-tree">
        <el-tree
          ref="treeRef"
          :data="treeData"
          :props="treeProps"
          default-expand-all
          highlight-current
          node-key="id"
          :expand-on-click-node="false"
          @node-click="handleNodeClick"
        >
          <template #default="{ data }">
            <div class="custom-tree-node">
              <div class="node-label">
                <el-icon v-if="data.level === 1"><OfficeBuilding /></el-icon>
                <el-icon v-else><User /></el-icon>
                <span>{{ data.label }}</span>
                <el-tag
                  :type="data.status === '1' ? 'success' : 'danger'"
                  size="small"
                  style="margin-left: 8px"
                >
                  {{ data.status === "1" ? "启用" : "禁用" }}
                </el-tag>
              </div>
              <div class="node-actions">
                <el-button
                  size="small"
                  type="primary"
                  text
                  @click.stop="handleEdit(data)"
                >
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button
                  size="small"
                  type="success"
                  text
                  @click.stop="handleAddChild(data)"
                >
                  <el-icon><Plus /></el-icon>
                  添加子部门
                </el-button>
                <el-button
                  size="small"
                  type="danger"
                  text
                  @click.stop="handleDelete(data)"
                  v-if="data.children?.length === 0"
                >
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </template>
        </el-tree>
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="上级部门" prop="parentName">
          <el-input v-model="formData.parentName" disabled />
        </el-form-item>
        <el-form-item label="部门名称" prop="departmentName">
          <el-input
            v-model="formData.departmentName"
            placeholder="请输入部门名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="部门编码" prop="departmentCode">
          <el-input
            v-model="formData.departmentCode"
            placeholder="请输入部门编码"
            maxlength="20"
          />
        </el-form-item>
        <el-form-item label="负责人" prop="manager">
          <el-input
            v-model="formData.manager"
            placeholder="请输入负责人姓名"
            maxlength="20"
          />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input
            v-model="formData.phone"
            placeholder="请输入联系电话"
            maxlength="20"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="formData.sort"
            :min="0"
            :max="999"
            controls-position="right"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio label="1">启用</el-radio>
            <el-radio label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入部门描述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleSubmit"
            :loading="submitLoading"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Plus,
  Refresh,
  Search,
  Edit,
  Delete,
  OfficeBuilding,
  User,
} from "@element-plus/icons-vue";

// 搜索表单
const searchForm = reactive({
  departmentName: "",
  status: "",
});

// 树形数据
const treeRef = ref();
const treeData = ref([
  {
    id: "1",
    label: "总公司",
    level: 1,
    status: "1",
    parentId: "0",
    children: [
      {
        id: "2",
        label: "技术部",
        level: 2,
        status: "1",
        parentId: "1",
        children: [
          {
            id: "3",
            label: "前端组",
            level: 3,
            status: "1",
            parentId: "2",
            children: [],
          },
          {
            id: "4",
            label: "后端组",
            level: 3,
            status: "1",
            parentId: "2",
            children: [],
          },
        ],
      },
      {
        id: "5",
        label: "运营部",
        level: 2,
        status: "1",
        parentId: "1",
        children: [
          {
            id: "6",
            label: "市场组",
            level: 3,
            status: "1",
            parentId: "5",
            children: [],
          },
        ],
      },
      {
        id: "7",
        label: "财务部",
        level: 2,
        status: "0",
        parentId: "1",
        children: [],
      },
    ],
  },
]);

const treeProps = {
  children: "children",
  label: "label",
};

// 对话框
const dialogVisible = ref(false);
const dialogTitle = ref("");
const submitLoading = ref(false);
const formRef = ref();

const formData = reactive({
  id: "",
  parentId: "",
  parentName: "",
  departmentName: "",
  departmentCode: "",
  manager: "",
  phone: "",
  sort: 0,
  status: "1",
  description: "",
});

const formRules = {
  departmentName: [
    { required: true, message: "请输入部门名称", trigger: "blur" },
    { min: 2, max: 50, message: "长度在 2 到 50 个字符", trigger: "blur" },
  ],
  departmentCode: [
    { required: true, message: "请输入部门编码", trigger: "blur" },
    {
      pattern: /^[A-Za-z0-9]+$/,
      message: "编码只能包含字母和数字",
      trigger: "blur",
    },
  ],
  manager: [{ required: true, message: "请输入负责人姓名", trigger: "blur" }],
  phone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
};

// 方法
const handleSearch = () => {
  // TODO: 实现搜索逻辑
  console.log("搜索条件:", searchForm);
  ElMessage.success("搜索功能待实现");
};

const handleReset = () => {
  searchForm.departmentName = "";
  searchForm.status = "";
  ElMessage.success("搜索条件已重置");
};

const refreshData = () => {
  // TODO: 实现数据刷新
  ElMessage.success("数据已刷新");
};

const handleNodeClick = (data: any) => {
  console.log("点击节点:", data);
};

const handleAdd = () => {
  dialogTitle.value = "新增部门";
  resetFormData();
  formData.parentName = "根部门";
  formData.parentId = "0";
  dialogVisible.value = true;
};

const handleAddChild = (data: any) => {
  dialogTitle.value = "新增子部门";
  resetFormData();
  formData.parentName = data.label;
  formData.parentId = data.id;
  dialogVisible.value = true;
};

const handleEdit = (data: any) => {
  dialogTitle.value = "编辑部门";
  formData.id = data.id;
  formData.parentId = data.parentId;
  formData.departmentName = data.label;
  formData.departmentCode = data.departmentCode || "";
  formData.manager = data.manager || "";
  formData.phone = data.phone || "";
  formData.sort = data.sort || 0;
  formData.status = data.status;
  formData.description = data.description || "";

  // 查找父级名称
  const findParentName = (nodes: any[], parentId: string): string => {
    for (const node of nodes) {
      if (node.id === parentId) {
        return node.label;
      }
      if (node.children?.length > 0) {
        const result = findParentName(node.children, parentId);
        if (result) return result;
      }
    }
    return parentId === "0" ? "根部门" : "未知";
  };

  formData.parentName = findParentName(treeData.value, data.parentId);
  dialogVisible.value = true;
};

const handleDelete = (data: any) => {
  ElMessageBox.confirm(`确认删除部门 "${data.label}" 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // TODO: 实现删除逻辑
      ElMessage.success("删除成功");
    })
    .catch(() => {
      ElMessage.info("已取消删除");
    });
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitLoading.value = true;

    // TODO: 实现提交逻辑
    await new Promise((resolve) => setTimeout(resolve, 1000));

    ElMessage.success(
      dialogTitle.value.includes("新增") ? "新增成功" : "修改成功"
    );
    dialogVisible.value = false;
    refreshData();
  } catch (error) {
    console.error("表单验证失败:", error);
  } finally {
    submitLoading.value = false;
  }
};

const handleDialogClose = () => {
  resetFormData();
  formRef.value?.clearValidate();
};

const resetFormData = () => {
  formData.id = "";
  formData.parentId = "";
  formData.parentName = "";
  formData.departmentName = "";
  formData.departmentCode = "";
  formData.manager = "";
  formData.phone = "";
  formData.sort = 0;
  formData.status = "1";
  formData.description = "";
};

onMounted(() => {
  // 初始化数据
  refreshData();
});
</script>

<style scoped>
.organization-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.organization-tree {
  margin-top: 20px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.node-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.node-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-tree-node__content) {
  height: 40px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}
</style>
