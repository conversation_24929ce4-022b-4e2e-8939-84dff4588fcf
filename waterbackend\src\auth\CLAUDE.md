[根目录](../../../CLAUDE.md) > [src](../../) > [auth](../) > **auth**

# 认证模块 (Auth Module)

## 模块职责

用户认证与授权管理，提供JWT令牌生成、验证、登录登出等核心安全功能。

## 入口与启动

- **模块入口**: `auth.module.ts`
- **主要服务**: `AuthService`
- **策略文件**: 
  - `local.strategy.ts` - 本地用户名密码认证
  - `jwt.strategy.ts` - JWT令牌验证

## 对外接口

### 核心接口
- `POST /auth/login` - 用户登录
- `POST /auth/logout` - 用户登出  
- `GET /profile` - 获取当前用户信息
- `GET /routes` - 获取动态路由

### 认证守卫
- `JwtAuthGuard` - JWT令牌验证守卫
- `LocalAuthGuard` - 本地登录验证守卫

## 关键依赖与配置

### 外部依赖
- `@nestjs/passport` - Passport认证框架集成
- `@nestjs/jwt` - JWT令牌处理
- `passport-local` - 本地认证策略
- `passport-jwt` - JWT认证策略

### 配置参数
- JWT密钥: `secretKeya1234567` (constants.ts)
- 令牌有效期: 5天
- 默认策略: `jwt`

## 数据模型

认证模块不直接管理数据模型，依赖于 User 实体：

```typescript
interface JWTPayload {
  username: string;
  userId: number;  
  roles: string;
  permissions?: string[];
}
```

## 测试与质量

### 测试文件
- `auth.service.spec.ts` - 认证服务单元测试

### 安全考虑
- IP地址记录: 集成 request-ip 获取客户端真实IP
- 密码加密: 使用 Md5 + 盐值方式加密
- 角色权限: 支持三级权限体系 (user/admin/superadmin)

## 常见问题 (FAQ)

**Q: JWT令牌如何刷新？**
A: 当前版本未实现令牌刷新机制，令牌过期需重新登录

**Q: 如何修改JWT密钥？**  
A: 修改 `constants.ts` 中的 `jwtConstants.secret` 值

**Q: 支持多设备同时登录吗？**
A: 目前设计支持多设备登录，没有单点登录限制

## 相关文件清单

```
src/auth/
├── auth.module.ts           # 模块定义
├── auth.service.ts          # 核心认证服务
├── auth.service.spec.ts     # 单元测试
├── auth.JwtAuthGuard.ts     # JWT守卫
├── constants.ts             # 配置常量
├── jwt.strategy.ts          # JWT策略
├── local-auth.guard.ts      # 本地认证守卫
└── local.strategy.ts        # 本地认证策略
```

## 变更记录 (Changelog)
- **2025-08-28**: 初始文档创建，模块功能完整梳理