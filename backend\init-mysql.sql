-- /f:/水利站/backend/init-mysql.sql
-- MySQL初始化脚本

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS water_station CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE water_station;

-- 创建分钟级数据表
CREATE TABLE IF NOT EXISTS device_data_minute (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    device_sn VARCHAR(50) NOT NULL COMMENT '设备序列号',
    timestamp DATETIME(3) NOT NULL COMMENT '分钟时间戳',
    avg_data JSON NOT NULL COMMENT '平均值数据',
    max_data JSON NOT NULL COMMENT '最大值数据',
    min_data JSON NOT NULL COMMENT '最小值数据',
    sample_count INT NOT NULL COMMENT '原始样本数量',
    created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
    
    INDEX idx_device_time_minute (device_sn, timestamp),
    INDEX idx_timestamp_minute (timestamp),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分钟级设备数据表';

-- 创建小时级数据表
CREATE TABLE IF NOT EXISTS device_data_hour (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    device_sn VARCHAR(50) NOT NULL COMMENT '设备序列号',
    timestamp DATETIME(3) NOT NULL COMMENT '小时时间戳',
    avg_data JSON NOT NULL COMMENT '平均值数据',
    max_data JSON NOT NULL COMMENT '最大值数据',
    min_data JSON NOT NULL COMMENT '最小值数据',
    minute_sample_count INT NOT NULL COMMENT '分钟级样本数量',
    original_sample_count INT NOT NULL COMMENT '原始5秒级样本总数',
    created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
    
    INDEX idx_device_time_hour (device_sn, timestamp),
    INDEX idx_timestamp_hour (timestamp),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小时级设备数据表';

-- 创建数据处理任务记录表
CREATE TABLE IF NOT EXISTS data_processing_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    task_type VARCHAR(50) NOT NULL COMMENT '任务类型(downsampling/archive/cleanup)',
    device_sn VARCHAR(50) COMMENT '设备序列号',
    start_time DATETIME(3) NOT NULL COMMENT '处理开始时间',
    end_time DATETIME(3) NOT NULL COMMENT '处理结束时间',
    processed_records INT DEFAULT 0 COMMENT '处理的记录数',
    execution_status VARCHAR(20) NOT NULL DEFAULT 'success' COMMENT '执行状态',
    error_message TEXT COMMENT '错误信息',
    processing_duration_ms INT COMMENT '处理耗时(毫秒)',
    created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
    
    INDEX idx_task_type_time (task_type, created_at),
    INDEX idx_device_sn (device_sn),
    INDEX idx_execution_status (execution_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据处理任务日志表';

-- 插入测试数据
INSERT INTO device_data_minute (
    device_sn, timestamp, avg_data, max_data, min_data, sample_count
) VALUES (
    'INIT_TEST_001',
    DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:00'),
    JSON_OBJECT('float1', 25.5, 'float2', 30.2, 'water_pump1.status', 1),
    JSON_OBJECT('float1', 26.0, 'float2', 31.0, 'water_pump1.status', 1),
    JSON_OBJECT('float1', 25.0, 'float2', 29.5, 'water_pump1.status', 1),
    12
);

-- 创建分区（可选，用于提高大数据量查询性能）
-- 分钟级数据表按月分区
ALTER TABLE device_data_minute PARTITION BY RANGE (YEAR(timestamp) * 100 + MONTH(timestamp)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    PARTITION p202403 VALUES LESS THAN (202404),
    PARTITION p202404 VALUES LESS THAN (202405),
    PARTITION p202405 VALUES LESS THAN (202406),
    PARTITION p202406 VALUES LESS THAN (202407),
    PARTITION p202407 VALUES LESS THAN (202408),
    PARTITION p202408 VALUES LESS THAN (202409),
    PARTITION p202409 VALUES LESS THAN (202410),
    PARTITION p202410 VALUES LESS THAN (202411),
    PARTITION p202411 VALUES LESS THAN (202412),
    PARTITION p202412 VALUES LESS THAN (202501),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 小时级数据表按年分区
ALTER TABLE device_data_hour PARTITION BY RANGE (YEAR(timestamp)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p2026 VALUES LESS THAN (2027),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 创建存储过程用于数据清理
DELIMITER //

CREATE PROCEDURE CleanupOldMinuteData(IN retention_days INT)
BEGIN
    DECLARE deleted_count INT DEFAULT 0;
    DECLARE cutoff_time DATETIME;
    
    SET cutoff_time = DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    DELETE FROM device_data_minute WHERE timestamp < cutoff_time;
    SET deleted_count = ROW_COUNT();
    
    INSERT INTO data_processing_log (
        task_type, start_time, end_time, processed_records, execution_status
    ) VALUES (
        'cleanup_minute_data', cutoff_time, NOW(), deleted_count, 'success'
    );
    
    SELECT CONCAT('清理了 ', deleted_count, ' 条过期的分钟级数据') AS result;
END //

DELIMITER ;

-- 创建视图用于统计信息查询
CREATE VIEW data_statistics_view AS
SELECT 
    'minute_data' as data_type,
    COUNT(*) as record_count,
    COUNT(DISTINCT device_sn) as device_count,
    MIN(timestamp) as earliest_record,
    MAX(timestamp) as latest_record,
    ROUND(AVG(sample_count), 2) as avg_sample_count
FROM device_data_minute
UNION ALL
SELECT 
    'hour_data' as data_type,
    COUNT(*) as record_count,
    COUNT(DISTINCT device_sn) as device_count,
    MIN(timestamp) as earliest_record,
    MAX(timestamp) as latest_record,
    ROUND(AVG(original_sample_count), 2) as avg_sample_count
FROM device_data_hour;

-- 显示初始化完成信息
SELECT 'MySQL数据库初始化完成' AS status;
SELECT 'device_data_minute 表已创建' AS minute_table_status;
SELECT 'device_data_hour 表已创建' AS hour_table_status;
SELECT 'data_processing_log 表已创建' AS log_table_status;
SELECT '分区策略已应用' AS partition_status;
SELECT '存储过程和视图已创建' AS procedure_view_status;