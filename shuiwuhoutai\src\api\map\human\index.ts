import request from "@/utils/request";
import { UserQuery } from "./model";
class HumanAPI {
  // 获取人房信息分页列表
  static getHouseMember(queryParams: UserQuery) {
    return request<any, any>({
      url: "/humanhouse",
      method: "get",
      params: queryParams,
    });
  }

  // 获取人房信息表单详情
  static getHouseMemberFormData(userId: number) {
    return request<any, any>({
      url: "/humanhouse/" + userId,
      method: "get",
    });
  }

  // 添加人房信息
  static addHouseMember(data: any) {
    return request({
      url: "/humanhouse",
      method: "post",
      data: data,
    });
  }

  // 修改人房信息
  static updateHouseMember(id: number, data: any) {
    return request({
      url: "/humanhouse/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除人房信息
  static deleteHouseMemberByIds(ids: string) {
    return request({
      url: "/humanhouse/" + ids,
      method: "delete",
    });
  }

  // 获取汇总信息分页列表
  static getSummaryList(queryParams: any) {
    return request<any, any>({
      url: "/humanhousesummary",
      method: "get",
      params: queryParams,
    });
  }

  // 获取汇总信息详情
  static getSummaryDetail(id: number) {
    return request<any, any>({
      url: "/humanhousesummary/" + id,
      method: "get",
    });
  }

  // 添加汇总信息
  static addSummary(data: any) {
    return request({
      url: "/humanhousesummary",
      method: "post",
      data: data,
    });
  }

  // 修改汇总信息
  static updateSummary(id: number, data: any) {
    return request({
      url: "/humanhousesummary/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除汇总信息
  static deleteSummaryByIds(ids: string) {
    return request({
      url: "/humanhousesummary/" + ids,
      method: "delete",
    });
  }

  // 导入人房信息概况
  static importSummary(file: FormData) {
    return request({
      url: "/humanhousesummary/import",
      method: "post",
      data: file,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  // 导入人房信息
  static importHouseMember(file: FormData) {
    return request({
      url: "/humanhouse/import",
      method: "post",
      data: file,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  // 导出人房信息
  static exportHouseMember(params: {
    columns: string[];
    filters: {
      keywords?: string;
      village?: string;
    };
  }) {
    return request<any, ArrayBuffer>({
      url: "/humanhouse/export",
      method: "post",
      data: params,
      responseType: "blob",
    });
  }
}

export default HumanAPI;
