<template>
  <div class="operation-log">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>运维日志</span>
          <el-button type="primary" @click="refreshData">刷新</el-button>
        </div>
      </template>

      <!-- 搜索条件 -->
      <el-form :model="searchForm" label-width="80px" inline>
        <el-form-item label="操作类型">
          <el-select
            v-model="searchForm.operationType"
            placeholder="请选择操作类型"
          >
            <el-option label="全部" value="" />
            <el-option label="设备启停" value="device_control" />
            <el-option label="参数调整" value="parameter_adjust" />
            <el-option label="维护操作" value="maintenance" />
            <el-option label="故障处理" value="fault_handling" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作人">
          <el-input v-model="searchForm.operator" placeholder="请输入操作人" />
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 运维日志表格 -->
      <el-table :data="logList" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="日志ID" width="100" />
        <el-table-column prop="operationType" label="操作类型" width="120">
          <template #default="scope">
            <el-tag :type="getOperationTypeColor(scope.row.operationType)">
              {{ getOperationTypeText(scope.row.operationType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="description"
          label="操作描述"
          show-overflow-tooltip
        />
        <el-table-column prop="operator" label="操作人" width="100" />
        <el-table-column prop="target" label="操作对象" width="120" />
        <el-table-column prop="result" label="执行结果" width="100">
          <template #default="scope">
            <el-tag
              :type="scope.row.result === 'success' ? 'success' : 'danger'"
            >
              {{ scope.row.result === "success" ? "成功" : "失败" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="operationTime" label="操作时间" width="180" />
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleView(scope.row)"
              >查看详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        style="margin-top: 20px"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";

// 搜索表单
const searchForm = reactive({
  operationType: "",
  operator: "",
  dateRange: [],
});

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0,
});

// 运维日志列表数据
const logList = ref([
  {
    id: "LOG001",
    operationType: "device_control",
    description: "启动泵站001主泵",
    operator: "张三",
    target: "泵站001",
    result: "success",
    operationTime: "2024-01-15 14:30:00",
  },
  {
    id: "LOG002",
    operationType: "parameter_adjust",
    description: "调整管网002压力参数至2.5MPa",
    operator: "李四",
    target: "管网002",
    result: "success",
    operationTime: "2024-01-15 13:15:00",
  },
  {
    id: "LOG003",
    operationType: "maintenance",
    description: "执行设备003定期维护保养",
    operator: "王五",
    target: "设备003",
    result: "failed",
    operationTime: "2024-01-15 10:45:00",
  },
]);

const loading = ref(false);

// 获取操作类型颜色
const getOperationTypeColor = (
  type: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const colorMap: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    device_control: "primary",
    parameter_adjust: "warning",
    maintenance: "info",
    fault_handling: "danger",
  };
  return colorMap[type] || "info";
};

// 获取操作类型文本
const getOperationTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    device_control: "设备启停",
    parameter_adjust: "参数调整",
    maintenance: "维护操作",
    fault_handling: "故障处理",
  };
  return textMap[type] || "未知";
};

// 查询
const handleSearch = () => {
  console.log("搜索条件:", searchForm);
  loadData();
};

// 重置搜索
const resetSearch = () => {
  searchForm.operationType = "";
  searchForm.operator = "";
  searchForm.dateRange = [];
  loadData();
};

// 刷新数据
const refreshData = () => {
  loadData();
};

// 查看详情
const handleView = (row: any) => {
  console.log("查看运维日志详情:", row);
};

// 分页大小改变
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  loadData();
};

// 当前页改变
const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  loadData();
};

// 加载数据
const loadData = () => {
  loading.value = true;
  setTimeout(() => {
    pagination.total = 200;
    loading.value = false;
  }, 1000);
};

onMounted(() => {
  loadData();
});
</script>

<style scoped>
.operation-log {
  padding: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
