import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  CacheKey,
  CacheTTL,
} from '@nestjs/common';
import { MasterService } from './master.service';
import { CreateMasterDto } from './dto/create-master.dto';
import { UpdateMasterDto } from './dto/update-master.dto';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { Role } from 'src/role.enum';
import { Roles } from 'src/roles.decorator';
// import { AuthService } from '../../auth/auth.service';

@ApiBearerAuth()
@ApiTags('权限组管理')
@Controller('master')
export class MasterController {
  constructor(
    private readonly masterService: MasterService, // private readonly authService: AuthService,
  ) {}
  // @UseGuards(AuthGuard('jwt'))
  @ApiOperation({ summary: '添加权限组' })
  @Post()
  @Roles(Role.Admin, Role.SuperAdmin)
  create(@Body() createMasterDto: CreateMasterDto) {
    return this.masterService.create(createMasterDto);
  }

  @Get()
  @CacheKey('master')
  @CacheTTL(1)
  @ApiOperation({ summary: '获取全部权限组' })
  @UseGuards(AuthGuard('jwt'))
  findAll() {
    return this.masterService.findAll();
  }

  @ApiOperation({ summary: '获取指定id权限组' })
  @Get(':id')
  @CacheKey('master-id')
  @CacheTTL(1)
  @UseGuards(AuthGuard('jwt'))
  @ApiResponse({
    status: 200,
    description: '权限情况',
    type: CreateMasterDto,
  })
  findOne(@Param('id') id: string) {
    return this.masterService.findOne(id);
  }

  @ApiOperation({ summary: '更新指定id的权限' })
  @Patch(':id')
  @UseGuards(AuthGuard('jwt'))
  update(@Param('id') id: string, @Body() updateMasterDto: UpdateMasterDto) {
    return this.masterService.update(id, updateMasterDto);
  }
  @ApiOperation({ summary: '删除指定id' })
  @Delete(':id')
  @UseGuards(AuthGuard('jwt'))
  remove(@Param('id') id: string) {
    return this.masterService.remove(id);
  }
}
