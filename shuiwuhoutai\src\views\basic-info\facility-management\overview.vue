<template>
  <div class="facility-overview-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="header-actions">
            <el-button type="primary" @click="handleExport">
              <el-icon><Download /></el-icon>
              导出Excel
            </el-button>
            <el-button @click="handlePrint">
              <el-icon><Printer /></el-icon>
              打印
            </el-button>
            <el-button @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-form">
        <el-form :model="filterForm" inline>
          <el-form-item label="设施类型">
            <el-select
              v-model="filterForm.facilityType"
              placeholder="请选择设施类型"
              clearable
              style="width: 150px"
            >
              <el-option label="全部" value="" />
              <el-option label="水泵设施" value="pump" />
              <el-option label="监测设施" value="monitor" />
              <el-option label="管道设施" value="pipeline" />
              <el-option label="闸门设施" value="gate" />
              <el-option label="其他设施" value="other" />
            </el-select>
          </el-form-item>
          <el-form-item label="所属区域">
            <el-select
              v-model="filterForm.areaId"
              placeholder="请选择区域"
              clearable
              style="width: 150px"
            >
              <el-option label="全部" value="" />
              <el-option label="江苏省" value="1" />
              <el-option label="南京市" value="2" />
              <el-option label="苏州市" value="3" />
            </el-select>
          </el-form-item>
          <el-form-item label="运行状态">
            <el-select
              v-model="filterForm.runStatus"
              placeholder="请选择状态"
              clearable
              style="width: 120px"
            >
              <el-option label="全部" value="" />
              <el-option label="正常" value="normal" />
              <el-option label="故障" value="fault" />
              <el-option label="维护" value="maintenance" />
              <el-option label="停用" value="stopped" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">
              <el-icon><Search /></el-icon>
              筛选
            </el-button>
            <el-button @click="handleResetFilter">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 统计概览 -->
      <div class="statistics-overview">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card total">
              <div class="stat-icon">
                <el-icon><DataBoard /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.total }}</div>
                <div class="stat-label">设施总数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card normal">
              <div class="stat-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.normal }}</div>
                <div class="stat-label">正常运行</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card fault">
              <div class="stat-icon">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.fault }}</div>
                <div class="stat-label">故障设施</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card maintenance">
              <div class="stat-icon">
                <el-icon><Tools /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.maintenance }}</div>
                <div class="stat-label">维护中</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 设施一览表 -->
      <div class="overview-table">
        <el-table
          :data="filteredTableData"
          style="width: 100%"
          stripe
          border
          v-loading="tableLoading"
          size="small"
        >
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column
            prop="facilityCode"
            label="设施编码"
            width="120"
            sortable
          />
          <el-table-column
            prop="facilityName"
            label="设施名称"
            width="150"
            sortable
          />
          <el-table-column
            prop="facilityType"
            label="设施类型"
            width="120"
            sortable
          >
            <template #default="{ row }">
              <el-tag :type="getTypeTagType(row.facilityType)" size="small">
                {{ getTypeLabel(row.facilityType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="areaName"
            label="所属区域"
            width="120"
            sortable
          />
          <el-table-column prop="location" label="安装位置" width="150" />
          <el-table-column prop="coordinates" label="经纬度坐标" width="180">
            <template #default="{ row }">
              <span class="coordinates"
                >{{ row.longitude }}, {{ row.latitude }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="runStatus"
            label="运行状态"
            width="100"
            sortable
          >
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.runStatus)" size="small">
                {{ getStatusLabel(row.runStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="installDate"
            label="安装日期"
            width="120"
            sortable
          />
          <el-table-column
            prop="lastMaintenance"
            label="上次维护"
            width="120"
            sortable
          />
          <el-table-column prop="manager" label="负责人" width="100" />
          <el-table-column prop="phone" label="联系电话" width="130" />
          <el-table-column prop="manufacturer" label="制造商" width="150" />
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button
                size="small"
                type="primary"
                text
                @click="handleViewDetail(row)"
              >
                <el-icon><View /></el-icon>
                详情
              </el-button>
              <el-button
                size="small"
                type="success"
                text
                @click="handleViewLocation(row)"
              >
                <el-icon><Location /></el-icon>
                定位
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分组统计 -->
      <div class="group-statistics">
        <el-divider>分类统计</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card shadow="hover">
              <template #header>
                <div class="card-header">
                  <h4>按设施类型统计</h4>
                </div>
              </template>
              <div class="chart-placeholder">
                <el-table :data="typeStatistics" size="small">
                  <el-table-column prop="type" label="设施类型" />
                  <el-table-column prop="count" label="数量" />
                  <el-table-column prop="percentage" label="占比">
                    <template #default="{ row }">
                      <el-progress
                        :percentage="row.percentage"
                        :stroke-width="12"
                        :show-text="true"
                        :format="() => `${row.percentage}%`"
                      />
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="hover">
              <template #header>
                <div class="card-header">
                  <h4>按区域统计</h4>
                </div>
              </template>
              <div class="chart-placeholder">
                <el-table :data="areaStatistics" size="small">
                  <el-table-column prop="area" label="所属区域" />
                  <el-table-column prop="count" label="数量" />
                  <el-table-column prop="percentage" label="占比">
                    <template #default="{ row }">
                      <el-progress
                        :percentage="row.percentage"
                        :stroke-width="12"
                        :show-text="true"
                        :format="() => `${row.percentage}%`"
                      />
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 设施详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="设施详细信息" width="600px">
      <el-descriptions :column="2" border v-if="detailData">
        <el-descriptions-item label="设施编码">{{
          detailData.facilityCode
        }}</el-descriptions-item>
        <el-descriptions-item label="设施名称">{{
          detailData.facilityName
        }}</el-descriptions-item>
        <el-descriptions-item label="设施类型">{{
          getTypeLabel(detailData.facilityType)
        }}</el-descriptions-item>
        <el-descriptions-item label="所属区域">{{
          detailData.areaName
        }}</el-descriptions-item>
        <el-descriptions-item label="安装位置">{{
          detailData.location
        }}</el-descriptions-item>
        <el-descriptions-item label="运行状态">
          <el-tag :type="getStatusTagType(detailData.runStatus)">
            {{ getStatusLabel(detailData.runStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="经度">{{
          detailData.longitude
        }}</el-descriptions-item>
        <el-descriptions-item label="纬度">{{
          detailData.latitude
        }}</el-descriptions-item>
        <el-descriptions-item label="安装日期">{{
          detailData.installDate
        }}</el-descriptions-item>
        <el-descriptions-item label="负责人">{{
          detailData.manager
        }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{
          detailData.phone
        }}</el-descriptions-item>
        <el-descriptions-item label="制造商">{{
          detailData.manufacturer
        }}</el-descriptions-item>
        <el-descriptions-item label="上次维护">{{
          detailData.lastMaintenance
        }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{
          detailData.createTime
        }}</el-descriptions-item>
        <el-descriptions-item label="设施描述" :span="2">
          {{ detailData.description || "-" }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";
import {
  Download,
  Printer,
  Refresh,
  Search,
  View,
  Location,
  DataBoard,
  CircleCheck,
  CircleClose,
  Tools,
} from "@element-plus/icons-vue";

// 筛选表单
const filterForm = reactive({
  facilityType: "",
  areaId: "",
  runStatus: "",
});

// 表格数据
const tableLoading = ref(false);
const tableData = ref([
  {
    id: "1",
    facilityCode: "SB001",
    facilityName: "1号水泵站",
    facilityType: "pump",
    areaName: "南京市",
    location: "玄武湖南岸",
    longitude: 118.7969,
    latitude: 32.0689,
    runStatus: "normal",
    installDate: "2023-03-15",
    lastMaintenance: "2024-01-10",
    manager: "张三",
    phone: "13800138001",
    manufacturer: "南京水利设备公司",
    createTime: "2023-03-15 10:30:00",
    description: "主要用于玄武湖区域排水",
  },
  {
    id: "2",
    facilityCode: "JC002",
    facilityName: "水质监测点A",
    facilityType: "monitor",
    areaName: "苏州市",
    location: "金鸡湖东岸",
    longitude: 120.7021,
    latitude: 31.319,
    runStatus: "normal",
    installDate: "2023-04-20",
    lastMaintenance: "2024-02-15",
    manager: "李四",
    phone: "13800138002",
    manufacturer: "苏州环保科技",
    createTime: "2023-04-20 14:20:00",
    description: "24小时水质监测",
  },
  {
    id: "3",
    facilityCode: "ZM003",
    facilityName: "防洪闸门",
    facilityType: "gate",
    areaName: "南京市",
    location: "秦淮河段",
    longitude: 118.7834,
    latitude: 32.0378,
    runStatus: "maintenance",
    installDate: "2022-12-10",
    lastMaintenance: "2024-08-20",
    manager: "王五",
    phone: "13800138003",
    manufacturer: "江苏水利机械厂",
    createTime: "2022-12-10 09:15:00",
    description: "防洪调节闸门",
  },
  {
    id: "4",
    facilityCode: "GD004",
    facilityName: "主输水管道",
    facilityType: "pipeline",
    areaName: "江苏省",
    location: "南京-苏州段",
    longitude: 119.25,
    latitude: 31.7,
    runStatus: "normal",
    installDate: "2022-06-01",
    lastMaintenance: "2024-06-15",
    manager: "赵六",
    phone: "13800138004",
    manufacturer: "江苏管道工程公司",
    createTime: "2022-06-01 08:00:00",
    description: "省际主要输水管道",
  },
  {
    id: "5",
    facilityCode: "SB005",
    facilityName: "2号备用水泵",
    facilityType: "pump",
    areaName: "苏州市",
    location: "工业园区",
    longitude: 120.6592,
    latitude: 31.2891,
    runStatus: "fault",
    installDate: "2023-07-10",
    lastMaintenance: "2024-07-20",
    manager: "孙七",
    phone: "13800138005",
    manufacturer: "苏州水利设备厂",
    createTime: "2023-07-10 16:45:00",
    description: "工业园区备用水泵",
  },
]);

// 过滤后的数据
const filteredTableData = computed(() => {
  let filtered = tableData.value;

  if (filterForm.facilityType) {
    filtered = filtered.filter(
      (item) => item.facilityType === filterForm.facilityType
    );
  }

  if (filterForm.areaId) {
    const areaMap: Record<string, string> = {
      "1": "江苏省",
      "2": "南京市",
      "3": "苏州市",
    };
    const areaName = areaMap[filterForm.areaId];
    if (areaName) {
      filtered = filtered.filter((item) => item.areaName === areaName);
    }
  }

  if (filterForm.runStatus) {
    filtered = filtered.filter(
      (item) => item.runStatus === filterForm.runStatus
    );
  }

  return filtered;
});

// 统计数据
const statistics = computed(() => {
  const total = filteredTableData.value.length;
  const normal = filteredTableData.value.filter(
    (item) => item.runStatus === "normal"
  ).length;
  const fault = filteredTableData.value.filter(
    (item) => item.runStatus === "fault"
  ).length;
  const maintenance = filteredTableData.value.filter(
    (item) => item.runStatus === "maintenance"
  ).length;

  return { total, normal, fault, maintenance };
});

// 类型统计
const typeStatistics = computed(() => {
  const typeMap: Record<string, string> = {
    pump: "水泵设施",
    monitor: "监测设施",
    pipeline: "管道设施",
    gate: "闸门设施",
    other: "其他设施",
  };

  const typeCount: Record<string, number> = {};
  filteredTableData.value.forEach((item) => {
    typeCount[item.facilityType] = (typeCount[item.facilityType] || 0) + 1;
  });

  const total = filteredTableData.value.length;
  return Object.entries(typeCount).map(([type, count]) => ({
    type: typeMap[type] || type,
    count,
    percentage: total > 0 ? Math.round((count / total) * 100) : 0,
  }));
});

// 区域统计
const areaStatistics = computed(() => {
  const areaCount: Record<string, number> = {};
  filteredTableData.value.forEach((item) => {
    areaCount[item.areaName] = (areaCount[item.areaName] || 0) + 1;
  });

  const total = filteredTableData.value.length;
  return Object.entries(areaCount).map(([area, count]) => ({
    area,
    count,
    percentage: total > 0 ? Math.round((count / total) * 100) : 0,
  }));
});

// 详情对话框
const detailDialogVisible = ref(false);
const detailData = ref<any>(null);

// 方法
const getTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    pump: "水泵设施",
    monitor: "监测设施",
    pipeline: "管道设施",
    gate: "闸门设施",
    other: "其他设施",
  };
  return typeMap[type] || "未知";
};

const getTypeTagType = (
  type: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const typeTagMap: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    pump: "primary",
    monitor: "success",
    pipeline: "warning",
    gate: "info",
    other: "danger",
  };
  return typeTagMap[type] || "info";
};

const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    normal: "正常",
    fault: "故障",
    maintenance: "维护",
    stopped: "停用",
  };
  return statusMap[status] || "未知";
};

const getStatusTagType = (
  status: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const statusTagMap: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    normal: "success",
    fault: "danger",
    maintenance: "warning",
    stopped: "info",
  };
  return statusTagMap[status] || "info";
};

const handleFilter = () => {
  tableLoading.value = true;
  setTimeout(() => {
    tableLoading.value = false;
    ElMessage.success("筛选完成");
  }, 500);
};

const handleResetFilter = () => {
  filterForm.facilityType = "";
  filterForm.areaId = "";
  filterForm.runStatus = "";
  ElMessage.success("筛选条件已重置");
};

const refreshData = () => {
  tableLoading.value = true;
  setTimeout(() => {
    tableLoading.value = false;
    ElMessage.success("数据已刷新");
  }, 1000);
};

const handleExport = () => {
  ElMessage.info("Excel导出功能待实现");
};

const handlePrint = () => {
  window.print();
};

const handleViewDetail = (row: any) => {
  detailData.value = row;
  detailDialogVisible.value = true;
};

const handleViewLocation = (row: any) => {
  ElMessage.info(`查看设施位置: ${row.longitude}, ${row.latitude}`);
};

onMounted(() => {
  refreshData();
});
</script>

<style scoped>
.facility-overview-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3,
.card-header h4 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.statistics-overview {
  margin: 20px 0;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid;
}

.stat-card.total {
  border-left-color: #409eff;
}

.stat-card.normal {
  border-left-color: #67c23a;
}

.stat-card.fault {
  border-left-color: #f56c6c;
}

.stat-card.maintenance {
  border-left-color: #e6a23c;
}

.stat-icon {
  font-size: 36px;
  margin-right: 16px;
  color: #409eff;
}

.normal .stat-icon {
  color: #67c23a;
}

.fault .stat-icon {
  color: #f56c6c;
}

.maintenance .stat-icon {
  color: #e6a23c;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.overview-table {
  margin: 20px 0;
}

.coordinates {
  font-family: monospace;
  font-size: 12px;
}

.group-statistics {
  margin-top: 30px;
}

.chart-placeholder {
  height: 200px;
  overflow-y: auto;
}

:deep(.el-progress-bar__outer) {
  height: 12px !important;
}

:deep(.el-progress-bar__inner) {
  border-radius: 6px;
}

@media print {
  .header-actions,
  .filter-form {
    display: none !important;
  }

  .overview-table {
    page-break-inside: avoid;
  }
}
</style>
