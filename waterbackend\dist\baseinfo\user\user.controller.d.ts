import { UserService } from './user.service';
import { Role } from 'src/role.enum';
import { User } from './entities/user.entity';
export declare class UserController {
    private readonly userService;
    constructor(userService: UserService);
    create(user: User, req: any): Promise<{
        code: number;
        msg: string;
        data?: undefined;
    } | {
        code: number;
        data: User;
        msg: string;
    }>;
    findAll(pageno: any, pagesize: any, username: any, roles: any, id: any): Promise<{
        code: number;
        data: any;
        msg: string;
    }>;
    findOne(id: string): Promise<{
        code: number;
        data: User;
        msg: string;
    } | {
        code: number;
        msg: string;
        data?: undefined;
    }>;
    getRoleOptions(req: any): {
        code: number;
        data: {
            value: Role;
            label: string;
        }[];
        msg: string;
    };
    update(id: string, updateUserDto: any): {
        code: number;
        data: Promise<import("typeorm").UpdateResult>;
        msg: string;
    };
    selfupdate(req: any, updateUserDto: any): {
        code: number;
        data: Promise<import("typeorm").UpdateResult>;
        msg: string;
    };
    remove(id: string): {
        code: number;
        data: Promise<import("typeorm").DeleteResult>;
        msg: string;
    };
    count(): Promise<number>;
    putupdate(id: string, updateUserDto: any): Promise<{
        code: number;
        data: import("typeorm").UpdateResult;
        msg: string;
    }>;
    cancellation(req: any): Promise<import("typeorm").UpdateResult>;
    changePassword(req: any, passwordData: {
        oldPassword: string;
        newPassword: string;
    }): Promise<{
        code: number;
        msg: string;
    }>;
}
