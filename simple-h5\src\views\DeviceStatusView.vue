<template>
  <div class="container">
    <!-- 导航栏 -->
    <van-nav-bar :title="currentStation?.name || '水利智能管理平台'" left-arrow @click-left="goBack" fixed placeholder
      class="custom-nav-bar">
      <template #right>
        <van-button type="primary" size="mini" @click="router.push(historyRoute)">
          历史数据
        </van-button>
      </template>
    </van-nav-bar>

    <!-- 顶部信息卡片 -->
    <van-card class="status-overview-card">
      <template #title>
        <div class="station-title-section">
          <div class="station-meta">
            <van-tag :type="getSystemStatus() === '运行正常' ? 'success' : 'danger'" class="system-status-tag">
              <van-icon :name="getSystemStatus() === '运行正常' ? 'success' : 'warning-o'" />
              {{ getSystemStatus() }}
            </van-tag>
            <div class="time-display">
              <van-icon name="clock-o" size="12" />
              <span class="current-time">{{ currentTime }}</span>
            </div>
          </div>
        </div>
      </template>
      <template #desc>
        <!-- 集成浮球状态和温湿度信息 -->
        <div class="integrated-info">
          <van-row gutter="8">
            <van-col span="8">
              <div class="info-item gradient-card water-level">
                <span class="info-label">浮球状态</span>
                <van-tag :type="getFloatStatusText().includes('高') ? 'warning' : 'success'" class="status-indicator">
                  {{ getFloatStatusText().split('（')[0] }}
                </van-tag>
              </div>
            </van-col>
            <van-col span="8">
              <div class="info-item gradient-card temperature-card">
                <span class="info-label">温度</span>
                <div class="value-display">{{ getTemperatureDisplay() }}</div>
              </div>
            </van-col>
            <van-col span="8">
              <div class="info-item gradient-card humidity-card">
                <span class="info-label">湿度</span>
                <div class="value-display">{{ getHumidityDisplay() }}</div>
              </div>
            </van-col>
          </van-row>
        </div>
      </template>
    </van-card>

    <!-- 设备运行状态区 -->
    <van-divider content-position="left" class="section-divider">
      <van-icon name="setting-o" />
      设备运行状态
    </van-divider>
    <van-row gutter="8">
      <van-col span="12" v-for="device in deviceStatusList" :key="device.id">
        <van-card :desc="`电流: ${device.current}`" :tag="getDeviceVoltage(device.name)"
          :class="['device-status-card', getDeviceTypeClass(device.name)]">
          <template #title>
            <div class="device-title">
              <div class="device-name-section">
                <span>{{ device.name }}</span>
                <van-tag class="voltage-tag">{{ getDeviceVoltage(device.name) }}</van-tag>
              </div>
              <van-tag :type="device.runStatus.class === 'status-running' ? 'success' : 'default'"
                class="run-status-tag">
                <van-icon :name="device.runStatus.class === 'status-running' ? 'play-circle-o' : 'pause-circle-o'"
                  size="12" />
                {{ device.runStatus.text }}
              </van-tag>
            </div>
          </template>

          <template #tags>
            <van-tag :type="getGearTagType(device.gearStatus.class)" class="gear-status-tag">
              <van-icon :name="getGearIcon(device.gearStatus.class)" size="12" />
              {{ device.gearStatus.text }}
            </van-tag>
          </template>

          <template #footer>
            <div class="device-check enhanced-check">
              <van-icon name="success" color="#07c160" size="12" />
              <span class="check-text">自检正常</span>
              <div class="check-time">{{ formatTime(device.lastCheckTime) }}</div>
            </div>
          </template>
        </van-card>
      </van-col>
    </van-row>

    <!-- 能耗数据看板区 -->
    <van-divider content-position="left" class="section-divider compact-divider">
      <van-icon name="bar-chart-o" />
      能耗数据看板
    </van-divider>
    <van-grid :column-num="4" :border="false" :gutter="4" class="energy-grid compact-grid">
      <van-grid-item v-for="(energy, index) in energyData" :key="energy.label"
        :class="`energy-grid-item energy-item-${index}`">
        <div class="energy-item compact-energy-item">
          <div class="energy-value">{{ energy.value }}</div>
          <div class="energy-meta">
            <span class="energy-label">{{ energy.label }}</span>
            <span class="energy-unit">{{ energy.unit }}</span>
          </div>
        </div>
      </van-grid-item>
    </van-grid>

    <!-- 电力数据看板区 -->
    <van-divider content-position="left" class="section-divider compact-divider">
      <van-icon name="lightning-o" />
      电力数据
    </van-divider>
    <van-grid :column-num="4" :border="false" :gutter="4" class="power-grid compact-grid">
      <van-grid-item v-for="(power, index) in powerData" :key="power.label"
        :class="`power-grid-item power-item-${index}`">
        <div class="energy-item compact-energy-item">
          <div class="energy-value">{{ power.value }}</div>
          <div class="energy-meta">
            <span class="energy-label">{{ power.label }}</span>
            <span class="energy-unit">{{ power.unit }}</span>
          </div>
        </div>
      </van-grid-item>
    </van-grid>

    <!-- 任务调度区 -->
    <van-divider content-position="left" class="section-divider">
      <van-icon name="calendar-o" />
      任务调度
    </van-divider>

    <van-button type="success" block @click="showTaskCreation = !showTaskCreation"
      :icon="showTaskCreation ? 'arrow-up' : 'plus'" class="new-task-button enhanced-button">
      {{ showTaskCreation ? '收起任务创建' : '新建任务' }}
    </van-button>

    <!-- 任务状态摘要 -->
    <van-cell-group inset class="task-summary-group">
      <van-cell title="单次任务" :value="getSingleTaskSummary()" :label="getSingleTaskSummary() !== '无' ? '剩余任务' : ''"
        class="task-summary-cell">
        <template #icon>
          <van-icon name="clock-o" color="#ff976a" size="16" />
        </template>
      </van-cell>
      <van-cell title="循环任务" :value="getCycleTaskSummary()" :label="getCycleTaskSummary() !== '无' ? '运行中' : ''"
        class="task-summary-cell">
        <template #icon>
          <van-icon name="replay" color="#1989fa" size="16" />
        </template>
      </van-cell>
      <van-cell title="顺序任务" :value="getSequenceTaskSummary()" :label="getSequenceTaskSummary() !== '无' ? '交替运行' : ''"
        class="task-summary-cell">
        <template #icon>
          <van-icon name="exchange" color="#07c160" size="16" />
        </template>
      </van-cell>
    </van-cell-group>

    <!-- 正在进行中的任务 -->
    <van-card v-if="hasRunningTasks" title="正在进行中的任务" class="running-tasks-card">
      <template #tags>
        <van-tag type="warning" class="task-count-tag">
          <van-icon name="play-circle-o" size="12" />
          {{ activeTasksData?.active_tasks_count || 0 }}
        </van-tag>
      </template>

      <div class="active-task-list">
        <van-cell-group v-for="task in getActiveTasksDisplay()" :key="task.id" inset>
          <van-cell :title="task.deviceName" :value="task.statusText"
            :label="task.remainingTime ? `剩余时间: ${task.remainingTime}` : ''">
            <template #icon>
              <van-tag :type="getTaskTypeTagType(task.type)">
                {{ task.type }}
              </van-tag>
            </template>
            <template #right-icon>
              <van-button type="danger" size="mini" @click="cancelTask(task.id, task.type)">
                取消
              </van-button>
            </template>
          </van-cell>

          <van-cell v-if="task.nextAction && task.nextAction !== '未知'">
            <template #title>
              <div class="task-next-action">
                <van-icon name="clock-o" size="14" color="#1989fa" />
                <span>下一步: {{ task.nextAction }}</span>
              </div>
            </template>
          </van-cell>

          <van-cell v-if="task.isHealthy === false">
            <template #title>
              <div class="task-health-warning">
                <van-icon name="warning-o" size="14" color="#ee0a24" />
                <span>任务异常</span>
              </div>
            </template>
          </van-cell>
        </van-cell-group>
      </div>
    </van-card>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed  } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 接收路由参数
const props = defineProps<{
  stationId: string
}>()

// 新增响应式变量
const showTaskCreation = ref(false)
const currentTime = ref(new Date().toLocaleString())

// 标签类型映射函数
function getGearTagType(gearClass: string): 'primary' | 'success' | 'danger' | 'warning' | 'default' {
  switch (gearClass) {
    case 'gear-auto':
      return 'primary'
    case 'gear-manual':
      return 'success'
    case 'gear-stop':
      return 'danger'
    default:
      return 'warning'
  }
}

function getTaskTypeTagType(taskType: string): 'primary' | 'success' | 'danger' | 'warning' | 'default' {
  switch (taskType) {
    case '循环任务':
      return 'primary'
    case '顺序任务':
      return 'success'
    case '单次任务':
      return 'warning'
    default:
      return 'default'
  }
}

// 美化功能辅助函数
function getGearIcon(gearClass: string): string {
  switch (gearClass) {
    case 'gear-auto':
      return 'setting-o'
    case 'gear-manual':
      return 'edit'
    case 'gear-stop':
      return 'stop-circle-o'
    default:
      return 'question-o'
  }
}

function formatTime(timeString: string): string {
  if (!timeString) return ''
  return timeString.split(' ')[1] || timeString
}

// 更新当前时间
onMounted(() => {
  const updateTime = () => {
    currentTime.value = new Date().toLocaleString()
  }
  setInterval(updateTime, 1000)
})

// 站点信息接口
interface StationInfo {
  id: string
  name: string
  sn: string
  baseUrl: string
}

// 当前站点信息
const currentStation = ref<StationInfo | null>(null)

// 从路由参数获取站点信息
function getCurrentStationFromRoute(): StationInfo | null {
  const { sn, stationName } = route.query

  if (sn && stationName) {
    return {
      id: props.stationId,
      name: stationName as string,
      sn: sn as string,
      baseUrl: 'http://49.235.191.145:8500' // 使用现有的后端服务地址
    }
  }
  return null
}

// 返回站点选择页面
function goBack() {
  router.push('/')
}

// 历史数据路由
const historyRoute = computed(() => ({
  name: 'history',
  params: { stationId: props.stationId },
  query: route.query
}))

// Define interfaces for our data structures to avoid using 'any'
interface DeviceStatus {
  auto_status?: number;
  stop_status?: number;
  manual_status?: number;
}

interface DeviceControlItem {
  id: string;
  label: string;
  status: 'auto' | 'stop' | 'manual' | null;
  current?: number;
}



// 从API返回的完整设备数据结构
interface DeviceData {
  float_switches: { float1: number };
  water_pump1: DeviceStatus;
  water_pump2: DeviceStatus;
  air_pump1: DeviceStatus;
  air_pump2: DeviceStatus;
  device_info: {
    sn: string;
    fw_version: string;
    imei: string;
  };
  last_updated: string;
  dianliucaiji2: {
    curr1_ch1?: number;
    curr1_ch2?: number;
    curr1_ch3?: number;
    curr1_ch4?: number;
    curr2_ch1?: number;
    curr2_ch2?: number;
    curr2_ch3?: number;
    curr2_ch4?: number;
  };
  wenshidu: {
    humidity: number;
    temperature: number;
  };
  diannengbiao: {
    voltages: { Ua: number; Ub: number; Uc: number };
    currents: { Ia: number; Ib: number; Ic: number };
    active_power: { total: number };
    reactive_power: { total: number };
    active_energy?: number;
    reverse_active_energy?: number;
  };
  timestamp: string;
  // DO状态指示
  DO21_status?: number;
  DO22_status?: number;
  DO23_status?: number;
  DO24_status?: number;
}

// 定义常量 - 从路由参数动态获取
const SN = computed(() => currentStation.value?.sn || '02800125081400008508') // 默认设备SN
const BASE_URL = computed(() => currentStation.value?.baseUrl || 'http://49.235.191.145:8500') // 默认API基础地址

// 使用 ref 创建响应式状态
const deviceData = ref<DeviceData | null>(null)

// DO控制和任务调度相关状态
const doControls = ref([
  { name: 'DO21', label: '水泵1' },
  { name: 'DO22', label: '水泵2' },
  { name: 'DO23', label: '气泵1' },
  { name: 'DO24', label: '气泵2' }
])
const scheduledTasks = ref<Record<string, { run_date: string; args: [string, string, number] }>>({})
const cyclicTasks = ref<
  Record<string, { do_name: string; on_minutes: number; off_minutes: number }>
>({})
// 新增：顺序任务相关状态
const sequenceTasks = ref<
  Record<
    string,
    { do_a_name: string; do_a_minutes: number; do_b_name: string; do_b_minutes: number }
  >
>({})

// 新增：活动任务数据
interface ActiveTaskItem {
  id: string;
  type: 'cycle' | 'sequence';
  device_name: string;
  status_text: string;
  remaining_time: string;
  next_action: string;
  device_sn: string;
  cycle_info?: {
    on_minutes: number;
    off_minutes: number;
  };
  sequence_info?: {
    device_a: string;
    device_b: string;
    do_a_minutes: number;
    do_b_minutes: number;
    healthy: boolean;
  };
}

interface ActiveTasksResponse {
  success: boolean;
  timestamp: string;
  active_tasks_count: number;
  active_tasks: ActiveTaskItem[];
}

const activeTasksData = ref<ActiveTasksResponse | null>(null)  // 存储从/schedule/tasks/active获取的完整数据

// onMounted 生命周期钩子中调用接口获取数据
onMounted(() => {
  // 获取当前站点信息
  currentStation.value = getCurrentStationFromRoute()

  fetchDeviceData()
  fetchPowerConsumptionStats()
  // 同时获取所有任务类型
  fetchScheduledTasks()
  fetchCyclicTasks()
  fetchSequenceTasks()
  fetchActiveTasksData()

  // 定时刷新设备数据和任务数据
  setInterval(() => {
    fetchDeviceData()
    fetchPowerConsumptionStats()
    fetchScheduledTasks()
    fetchCyclicTasks()
    fetchSequenceTasks()
    fetchActiveTasksData()
  }, 30000)
})

/**
 * 从API获取设备数据
 */
async function fetchDeviceData() {
  const apiUrl = `${BASE_URL.value}/device-data/${SN.value}.json`

  try {
    const response = await fetch(apiUrl)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    // JSON文件返回的是单个对象格式
    const jsonData = await response.json()
    if (jsonData && typeof jsonData === 'object') {
      deviceData.value = jsonData
      console.log('✅ 设备数据获取成功:', {
        timestamp: jsonData.timestamp,
        设备SN: jsonData.device_info?.sn,
        浮球状态: jsonData.float_switches?.float1,
        温度: jsonData.wenshidu?.temperature,
        湿度: jsonData.wenshidu?.humidity,
        水泵1状态: jsonData.water_pump1,
        水泵2状态: jsonData.water_pump2,
        气泵1状态: jsonData.air_pump1,
        气泵2状态: jsonData.air_pump2,
        电流数据: jsonData.dianliucaiji2,
        电能表数据: jsonData.diannengbiao,
        DO状态: {
          DO21: jsonData.DO21_status,
          DO22: jsonData.DO22_status,
          DO23: jsonData.DO23_status,
          DO24: jsonData.DO24_status
        }
      })
    } else {
      throw new Error('设备数据格式错误或无数据')
    }
  } catch (error) {
    console.error('获取设备数据失败:', error)
  }
}

/**
 * 获取活动任务状态
 */
async function fetchActiveTasksData() {
  const apiUrl = `${BASE_URL.value}/schedule/tasks/active`
  try {
    const response = await fetch(apiUrl)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const result: ActiveTasksResponse = await response.json()
    activeTasksData.value = result
  } catch (error) {
    console.error('获取活动任务失败:', error)
    activeTasksData.value = null
  }
}

/**
 * 获取计划任务列表
 */
async function fetchScheduledTasks() {
  const apiUrl = `${BASE_URL.value}/schedule/tasks`
  try {
    const response = await fetch(apiUrl)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    scheduledTasks.value = await response.json()
  } catch (error) {
    console.error('获取计划任务失败:', error)
  }
}

/**
 * 获取循环任务列表
 */
async function fetchCyclicTasks() {
  const apiUrl = `${BASE_URL.value}/schedule/cycles`
  try {
    const response = await fetch(apiUrl)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    cyclicTasks.value = await response.json()
  } catch (error) {
    console.error('获取循环任务失败:', error)
  }
}

/**
 * 获取顺序任务列表
 */
async function fetchSequenceTasks() {
  const apiUrl = `${BASE_URL.value}/schedule/sequences`
  try {
    const response = await fetch(apiUrl)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    sequenceTasks.value = await response.json()
  } catch (error) {
    console.error('获取顺序任务失败:', error)
  }
}

/**
 * 取消计划任务
 * @param jobId - 任务ID
 */
async function cancelScheduledTask(jobId: string) {
  const apiUrl = `${BASE_URL.value}/schedule/task/${jobId}`
  try {
    const response = await fetch(apiUrl, {
      method: 'DELETE'
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.detail || `HTTP error! status: ${response.status}`)
    }
    alert(result.message)
    // 刷新任务列表
    fetchScheduledTasks()
  } catch (error) {
    console.error(`取消任务 ${jobId} 失败:`, error)
    alert(`取消任务 ${jobId} 失败: ${error}`)
  }
}

/**
 * 取消循环任务
 * @param cycleId - 循环任务ID
 */
async function cancelCyclicTask(cycleId: string) {
  const apiUrl = `${BASE_URL.value}/schedule/cycle/${cycleId}`
  try {
    const response = await fetch(apiUrl, {
      method: 'DELETE'
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.detail || `HTTP error! status: ${response.status}`)
    }
    alert(result.message)
    fetchCyclicTasks() // 刷新列表
  } catch (error) {
    console.error(`取消循环任务 ${cycleId} 失败:`, error)
    alert(`取消循环任务 ${cycleId} 失败: ${error}`)
  }
}

/**
 * 取消顺序任务
 * @param sequenceId - 顺序任务ID
 */
async function cancelSequenceTask(sequenceId: string) {
  const apiUrl = `${BASE_URL.value}/schedule/sequence/${sequenceId}`
  try {
    const response = await fetch(apiUrl, {
      method: 'DELETE'
    })
    const result = await response.json()
    if (!response.ok) {
      throw new Error(result.detail || `HTTP error! status: ${response.status}`)
    }
    alert(result.message)
    fetchSequenceTasks() // 刷新列表
  } catch (error) {
    console.error(`取消顺序任务 ${sequenceId} 失败:`, error)
    alert(`取消顺序任务 ${sequenceId} 失败: ${error}`)
  }
}

/**
 * 根据DO名称获取其标签
 * @param doName - DO的名称, e.g., 'DO21'
 * @returns 对应的标签, e.g., '水泵1'
 */
function getDoLabel(doName: string): string {
  const doItem = doControls.value.find((item) => item.name === doName)
  return doItem ? doItem.label : doName
}

/**
 * 获取旋钮开关的状态
 * @param deviceStatus - 设备状态对象
 * @returns 'auto', 'stop', 'manual', or null
 */
function getKnobStatus(deviceStatus?: DeviceStatus): 'auto' | 'stop' | 'manual' | null {
  if (!deviceStatus) return null

  // 检查各种状态，优先级：manual > stop > auto
  if (deviceStatus.manual_status === 1) return 'manual'
  if (deviceStatus.stop_status === 1) return 'stop'
  if (deviceStatus.auto_status === 1) return 'auto'

  // 如果只有auto_status字段存在且为1，则默认为auto模式
  if (deviceStatus.auto_status !== undefined) {
    return deviceStatus.auto_status === 1 ? 'auto' : null
  }

  return null
}

// 使用 computed 属性来派生状态，使模板更简洁
const deviceControls = computed(() => {
  const c = deviceData.value?.dianliucaiji2
  return [
    {
      id: 'water_pump1',
      label: '水泵1',
      status: getKnobStatus(deviceData.value?.water_pump1),
      current: c?.curr1_ch1 ?? c?.curr2_ch1
    },
    {
      id: 'water_pump2',
      label: '水泵2',
      status: getKnobStatus(deviceData.value?.water_pump2),
      current: c?.curr1_ch2 ?? c?.curr2_ch2
    },
    {
      id: 'air_pump1',
      label: '气泵1',
      status: getKnobStatus(deviceData.value?.air_pump1),
      current: c?.curr1_ch3 ?? c?.curr2_ch3
    },
    {
      id: 'air_pump2',
      label: '气泵2',
      status: getKnobStatus(deviceData.value?.air_pump2),
      current: c?.curr1_ch4 ?? c?.curr2_ch4
    }
  ]
})

// 系统状态计算
const getSystemStatus = () => {
  if (!deviceData.value) return '数据加载中'

  // 检查是否有数据
  const hasData = deviceData.value.timestamp &&
                  deviceData.value.device_info?.sn

  if (!hasData) return '数据异常'

  // 检查设备状态
  const hasError = deviceControls.value.some(device => device.status === null)
  return hasError ? '运行异常' : '运行正常'
}

// 浮球状态文本和样式
const getFloatStatusText = () => {
  const float1 = deviceData.value?.float_switches?.float1
  if (float1 == null) return '--'
  return float1 === 1 ? '高水位（运行）' : '低水位（未运行）'
}

// 温湿度显示
const getTemperatureDisplay = () => {
  const temp = deviceData.value?.wenshidu?.temperature
  return temp != null ? `${temp.toFixed(1)}°C` : '--'
}

const getHumidityDisplay = () => {
  const hum = deviceData.value?.wenshidu?.humidity
  return hum != null ? `${hum.toFixed(1)}%` : '--'
}

// 设备状态列表
const deviceStatusList = computed(() => {
  const controls = deviceControls.value
  return controls.map(device => ({
    id: device.id,
    name: device.label,
    runStatus: getDeviceRunStatus(device),
    gearStatus: getDeviceGearStatus(device),
    current: device.current != null ? `${device.current.toFixed(3)}A` : '--',
    selfCheck: { text: '正常', class: 'check-normal' },
    lastCheckTime: new Date().toLocaleString()
  }))
})

// 获取设备运行状态
function getDeviceRunStatus(device: DeviceControlItem) {
  if (!device.status) return { text: '未知', class: 'status-unknown' }

  // 检查DO状态来判断运行状态
  const doStatusMap: Record<string, string> = {
    'water_pump1': 'DO21_status',
    'water_pump2': 'DO22_status',
    'air_pump1': 'DO23_status',
    'air_pump2': 'DO24_status'
  }

  const doStatusKey = doStatusMap[device.id]
  const isRunning = deviceData.value?.[doStatusKey as keyof DeviceData] === 1

  if (isRunning) {
    return { text: '运行中', class: 'status-running' }
  } else {
    return { text: '停止', class: 'status-stopped' }
  }
}

// 获取设备档位状态
function getDeviceGearStatus(device: DeviceControlItem) {
  if (!device.status) return { text: '未知', class: 'gear-unknown' }

  switch (device.status) {
    case 'auto':
      return { text: '自动', class: 'gear-auto' }
    case 'manual':
      return { text: '手动', class: 'gear-manual' }
    case 'stop':
      return { text: '停止', class: 'gear-stop' }
    default:
      return { text: '未知', class: 'gear-unknown' }
  }
}

// 能耗数据状态
const energyStats = ref<{
  statistics?: {
    today?: { consumption: number };
    yesterday?: { consumption: number };
    this_month?: { consumption: number };
    last_month?: { consumption: number }
  }
} | null>(null)

// 获取用电统计数据
async function fetchPowerConsumptionStats() {
  const apiUrl = `${BASE_URL.value}/power-consumption/stats?device_sn=${SN.value}`

  try {
    const response = await fetch(apiUrl)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    energyStats.value = await response.json()
  } catch (error) {
    console.error('获取用电统计失败:', error)
    // 发生错误时energyStats保持null，显示"--"状态
  }
}

// 能耗数据（保留原来的用电统计数据）
const energyData = computed(() => {
  if (energyStats.value?.statistics) {
    const stats = energyStats.value.statistics
    return [
      {
        label: '今日用电',
        value: stats.today?.consumption?.toFixed(2) ?? '--',
        unit: '度'
      },
      {
        label: '昨日用电',
        value: stats.yesterday?.consumption?.toFixed(2) ?? '--',
        unit: '度'
      },
      {
        label: '本月用电',
        value: stats.this_month?.consumption?.toFixed(2) ?? '--',
        unit: '度'
      },
      {
        label: '上月用电',
        value: stats.last_month?.consumption?.toFixed(2) ?? '--',
        unit: '度'
      }
    ]
  }

  // 接口未响应时显示加载状态
  return [
    { label: '今日用电', value: '--', unit: '度' },
    { label: '昨日用电', value: '--', unit: '度' },
    { label: '本月用电', value: '--', unit: '度' },
    { label: '上月用电', value: '--', unit: '度' }
  ]
})

// 三相电力数据（新增的三相电压、电流、功率数据）
const powerData = computed(() => {
  const p = deviceData.value?.diannengbiao

  // 如果设备数据存在，显示三相电力数据
  if (p) {
    return [
      {
        label: 'A相电压',
        value: p.voltages?.Ua?.toFixed(1) ?? '--',
        unit: 'V'
      },
      {
        label: 'B相电压',
        value: p.voltages?.Ub?.toFixed(1) ?? '--',
        unit: 'V'
      },
      {
        label: 'C相电压',
        value: p.voltages?.Uc?.toFixed(1) ?? '--',
        unit: 'V'
      },
      {
        label: '总功率',
        value: p.active_power?.total?.toFixed(0) ?? '--',
        unit: 'W'
      },
      {
        label: 'A相电流',
        value: p.currents?.Ia?.toFixed(2) ?? '--',
        unit: 'A'
      },
      {
        label: 'B相电流',
        value: p.currents?.Ib?.toFixed(2) ?? '--',
        unit: 'A'
      },
      {
        label: 'C相电流',
        value: p.currents?.Ic?.toFixed(2) ?? '--',
        unit: 'A'
      },
      {
        label: '用电度数',
        value: p.active_energy?.toFixed(2) ?? '--',
        unit: 'kWh'
      }
    ]
  }

  // 设备数据未加载时显示加载状态
  return [
    { label: 'A相电压', value: '--', unit: 'V' },
    { label: 'B相电压', value: '--', unit: 'V' },
    { label: 'C相电压', value: '--', unit: 'V' },
    { label: '总功率', value: '--', unit: 'W' },
    { label: 'A相电流', value: '--', unit: 'A' },
    { label: 'B相电流', value: '--', unit: 'A' },
    { label: 'C相电流', value: '--', unit: 'A' },
    { label: '用电度数', value: '--', unit: 'kWh' }
  ]
})

// 任务相关计算属性
const hasRunningTasks = computed(() => {
  return (activeTasksData.value?.active_tasks_count || 0) > 0
})


// 获取任务摘要显示 - 基于新的活动任务接口
function getSingleTaskSummary(): string {
  // 单次任务现在通过 /schedule/tasks 接口获取，保持原有逻辑
  const tasks = Object.entries(scheduledTasks.value)
  if (tasks.length === 0) return '无'

  const task = tasks[0][1] as { run_date: string; args: [string, string, number]; remaining_minutes?: number; do_name?: string; value?: number }
  // 兼容旧数据结构和新数据结构
  const remainingMinutes = task.remaining_minutes || 0
  const doName = task.do_name || (task.args && task.args[1]) || ''
  const value = task.value !== undefined ? task.value : (task.args && task.args[2])

  if (remainingMinutes <= 0) return '无'

  return `${getDoLabel(doName)} ${remainingMinutes}'后${value === 1 ? '开启' : '关闭'}`
}

function getCycleTaskSummary(): string {
  // 使用新的活动任务接口数据
  if (!activeTasksData.value?.active_tasks) return '无'

  const cycleTasks = activeTasksData.value.active_tasks.filter(task => task.type === 'cycle')
  if (cycleTasks.length === 0) return '无'

  const task = cycleTasks[0]
  if (!task.cycle_info) return '无'

  const onHours = Math.floor(task.cycle_info.on_minutes / 60)
  const onMins = task.cycle_info.on_minutes % 60
  const offHours = Math.floor(task.cycle_info.off_minutes / 60)
  const offMins = task.cycle_info.off_minutes % 60

  const onDisplay = onHours > 0 ? `${onHours}h${onMins > 0 ? onMins + 'm' : ''}` : `${onMins}m`
  const offDisplay = offHours > 0 ? `${offHours}h${offMins > 0 ? offMins + 'm' : ''}` : `${offMins}m`

  return `${task.device_name} ${onDisplay}/${offDisplay}`
}

function getSequenceTaskSummary(): string {
  // 使用新的活动任务接口数据
  if (!activeTasksData.value?.active_tasks) return '无'

  const sequenceTasks = activeTasksData.value.active_tasks.filter(task => task.type === 'sequence')
  if (sequenceTasks.length === 0) return '无'

  const task = sequenceTasks[0]
  if (!task.sequence_info) return '无'

  const aHours = Math.floor(task.sequence_info.do_a_minutes / 60)
  const aDisplay = aHours > 0 ? `${aHours}h` : `${task.sequence_info.do_a_minutes}m`

  return `${task.sequence_info.device_a}↔${task.sequence_info.device_b} ${aDisplay}轮换`
}

// 获取当前活动任务的显示信息
function getActiveTasksDisplay() {
  if (!activeTasksData.value?.active_tasks) {
    return []
  }

  return activeTasksData.value.active_tasks.map(task => ({
    id: task.id,
    deviceName: task.device_name,
    statusText: task.status_text,
    remainingTime: task.remaining_time !== '运行中' ? task.remaining_time : undefined,
    type: task.type === 'cycle' ? '循环任务' : '顺序任务',
    nextAction: task.next_action,
    deviceSn: task.device_sn,
    isHealthy: task.type === 'sequence' ? task.sequence_info?.healthy : true
  }))
}

// 获取设备类型样式
function getDeviceTypeClass(deviceName: string) {
  if (deviceName.includes('水泵')) {
    return 'water-pump'
  } else if (deviceName.includes('气泵')) {
    return 'air-pump'
  }
  return ''
}

// 获取设备额定电压
function getDeviceVoltage(deviceName: string) {
  const sn = SN.value

  // 联丰村站点 02800125081400008508
  if (sn === '02800125081400008508') {
    if (deviceName.includes('水泵')) {
      return '380V'
    } else if (deviceName.includes('气泵')) {
      return '220V'
    }
  }
  // 大船港村曹村站点 02800125071500004977
  else if (sn === '02800125071500004977') {
    if (deviceName === '水泵1') {
      return '380V'
    } else if (deviceName === '水泵2' || deviceName.includes('气泵')) {
      return '220V'
    }
  }
  // 默认电压配置
  else {
    if (deviceName.includes('水泵')) {
      return '380V'
    } else if (deviceName.includes('气泵')) {
      return '220V'
    }
  }

  return ''
}

// 任务操作方法
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function modifyTask(_taskId: string) {
  alert('修改任务功能开发中...')
}

function cancelTask(taskId: string, taskType: string) {
  switch (taskType) {
    case '单次任务':
      cancelScheduledTask(taskId)
      break
    case '循环任务':
      cancelCyclicTask(taskId)
      break
    case '顺序任务':
      cancelSequenceTask(taskId)
      break
    default:
      console.warn('未知的任务类型:', taskType)
      alert('无法取消未知类型的任务')
  }
}
</script>

<style scoped>
/* 清新主题样式 */
.container {
  padding: 0;
  background: linear-gradient(135deg, #e3f2fd 0%, #f1f8e9 50%, #e8f5e8 100%);
  min-height: 100vh;
  position: relative;
}

.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(129, 199, 132, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(100, 181, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(174, 213, 129, 0.08) 0%, transparent 50%);
  z-index: -1;
}

/* 导航栏清新配色 */
.custom-nav-bar {
  background: linear-gradient(135deg, #42a5f5 0%, #66bb6a 100%) !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

:deep(.custom-nav-bar .van-nav-bar__title) {
  font-weight: 600;
  font-size: 18px;
}

/* 卡片清新样式 */
:deep(.van-card) {
  margin-bottom: 12px;
  /* 减少卡片底部间距 */
  border-radius: 16px !important;
  overflow: hidden;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.9) !important;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.5);
}

:deep(.van-card:hover) {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.08);
}

/* 状态概览卡片清新配色 */
.status-overview-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 255, 248, 0.9) 100%) !important;
  border: 1px solid rgba(129, 199, 132, 0.2);
  margin-bottom: 8px;
  /* 进一步减少概览卡片底部间距 */
}

.station-title-section {
  padding: 6px 0;
  /* 减少顶部内边距 */
}

.station-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
  /* 减少底部间距 */
}

.system-status-tag {
  font-weight: 600;
}

.time-display {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #646566;
  font-size: 12px;
}

.current-time {
  font-weight: 500;
}

/* 集成信息卡片 */
.integrated-info {
  margin-top: 12px;
  /* 减少顶部间距 */
}

.gradient-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(240, 248, 255, 0.85) 100%);
  border-radius: 12px;
  padding: 10px;
  /* 减少内边距 */
  border: 1px solid rgba(100, 181, 246, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.gradient-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #42a5f5, #66bb6a);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gradient-card:hover::before {
  opacity: 1;
}

.gradient-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(100, 181, 246, 0.15);
}

.water-level {
  border-left: 3px solid #42a5f5;
}

.temperature-card {
  border-left: 3px solid #ff8a65;
}

.humidity-card {
  border-left: 3px solid #66bb6a;
}

.info-item {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.info-label {
  font-size: 11px;
  color: #969799;
  font-weight: 500;
}

.value-display {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.status-indicator {
  font-size: 10px;
  font-weight: 600;
}

/* 分割线美化 */
.section-divider {
  color: #323233;
  font-weight: 600;
  margin: 12px 0 10px 0;
  /* 进一步减少分割线间距 */
}

.compact-divider {
  color: #323233;
  font-weight: 600;
  margin: 10px 0 8px 0;
  /* 进一步减少分割线间距 */
}

:deep(.section-divider .van-divider__content) {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #42a5f5, #66bb6a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

:deep(.compact-divider .van-divider__content) {
  display: flex;
  align-items: center;
  gap: 6px;
  /* 减少图标和文字间距 */
  background: linear-gradient(135deg, #42a5f5, #66bb6a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 14px;
  /* 减小分割线字体 */
}

/* 设备状态卡片 */
.device-status-card {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  margin-bottom: 6px;
  /* 进一步减少设备状态卡片间距 */
}

.device-status-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, #42a5f5, #66bb6a);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.device-status-card:hover::before {
  opacity: 1;
}

.device-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.device-name-section {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.run-status-tag,
.gear-status-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}

.enhanced-check {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 0;
  /* 减少检查信息的内边距 */
  border-top: 1px solid #f5f5f5;
}

.check-text {
  font-size: 12px;
  color: #07c160;
  font-weight: 500;
}

.check-time {
  font-size: 10px;
  color: #969799;
}

/* 能耗数据美化 */
.energy-grid {
  margin-bottom: 8px;
  /* 减少能耗网格底部间距 */
}

.power-grid {
  margin-bottom: 8px;
  /* 减少电力网格底部间距 */
}

.compact-grid {
  margin-bottom: 8px;
  /* 进一步减少底部间距 */
}

.enhanced-energy-item {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(240, 248, 255, 0.8) 100%);
  border-radius: 12px;
  padding: 12px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(100, 181, 246, 0.15);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.compact-energy-item {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(240, 248, 255, 0.8) 100%);
  border-radius: 8px;
  /* 减小圆角 */
  padding: 8px 4px;
  /* 减少内边距 */
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(100, 181, 246, 0.15);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  min-height: 50px;
  /* 设置最小高度确保一致性 */
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.enhanced-energy-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 25px rgba(100, 181, 246, 0.12);
}

.compact-energy-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(100, 181, 246, 0.1);
}

.energy-icon {
  margin-bottom: 8px;
}

.energy-value-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.energy-value {
  font-size: 16px;
  font-weight: 700;
  color: #323233;
}

.compact-energy-item .energy-value {
  font-size: 14px;
  /* 减小字体 */
  font-weight: 600;
  color: #323233;
  line-height: 1.2;
}

.energy-meta {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.energy-label {
  font-size: 10px;
  color: #969799;
  font-weight: 500;
}

.compact-energy-item .energy-label {
  font-size: 9px;
  /* 进一步减小标签字体 */
  color: #969799;
  font-weight: 500;
  line-height: 1.1;
}

.energy-unit {
  font-size: 9px;
  color: #c8c9cc;
}

.compact-energy-item .energy-unit {
  font-size: 8px;
  /* 进一步减小单位字体 */
  color: #c8c9cc;
}

/* 电压标签样式 */
.voltage-tag {
  margin-left: 8px;
  background-color: #f0f9ff !important;
  color: #0369a1 !important;
  border: 1px solid #e0f2fe;
  font-size: 10px;
  font-weight: 500;
}

/* 任务按钮清新配色 */
.new-task-button {
  margin-bottom: 16px;
  background: linear-gradient(135deg, #66bb6a 0%, #42a5f5 100%) !important;
  border: none !important;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(102, 187, 106, 0.25);
  transition: all 0.3s ease;
}

.new-task-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 187, 106, 0.35);
}

.task-summary-group {
  margin-bottom: 16px;
}

.task-summary-cell {
  transition: all 0.3s ease;
}

.task-summary-cell:hover {
  background-color: #f8f9fa;
}

.running-tasks-card {
  border-left: 4px solid #81c784;
}

.task-count-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 600;
}

/* 任务项动画 */
.task-next-action,
.task-health-warning {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.task-next-action {
  color: #42a5f5;
}

.task-health-warning {
  color: #ee0a24;
}

/* Vant组件深度样式 */
:deep(.van-cell-group) {
  margin-bottom: 12px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

:deep(.van-cell) {
  transition: all 0.3s ease;
}

:deep(.van-cell:hover) {
  background-color: #f8f9fa;
}

:deep(.van-divider) {
  margin: 20px 0;
}

:deep(.van-collapse-item__content) {
  padding: 16px;
  background: rgba(255, 255, 255, 0.95);
}

:deep(.van-tabs--card .van-tab) {
  border-color: #ebedf0;
  transition: all 0.3s ease;
}

:deep(.van-tabs--card .van-tab--active) {
  background: linear-gradient(135deg, #42a5f5, #66bb6a);
  color: white;
}

:deep(.van-field__control) {
  font-size: 14px;
}

:deep(.van-button--mini) {
  font-size: 11px;
  padding: 4px 8px;
  transition: all 0.3s ease;
}

:deep(.van-button--mini:hover) {
  transform: translateY(-1px);
}

:deep(.van-tag) {
  transition: all 0.3s ease;
}

:deep(.van-tag:hover) {
  transform: scale(1.05);
}

/* 移动端响应式优化 */
@media (max-width: 640px) {
  .container {
    padding: 0 8px;
  }

  :deep(.van-nav-bar__title) {
    font-size: 16px;
  }

  :deep(.van-card__content) {
    padding: 12px;
  }

  .gradient-card {
    padding: 10px;
  }

  .enhanced-energy-item {
    padding: 10px;
  }

  .compact-energy-item {
    padding: 6px 3px;
    /* 移动端进一步压缩 */
    min-height: 45px;
    /* 减小移动端最小高度 */
  }

  .compact-energy-item .energy-value {
    font-size: 12px;
    /* 移动端减小数值字体 */
  }

  .compact-energy-item .energy-label {
    font-size: 8px;
    /* 移动端减小标签字体 */
  }

  .compact-energy-item .energy-unit {
    font-size: 7px;
    /* 移动端减小单位字体 */
  }

  .animated-number {
    font-size: 14px;
  }

  .energy-label {
    font-size: 9px;
  }

  /* 压缩移动端的分割线间距 */
  .compact-divider {
    margin: 12px 0 8px 0;
  }

  .section-divider {
    margin: 16px 0 12px 0;
  }
}

/* 加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.van-card {
  animation: fadeInUp 0.6s ease-out;
}

.van-card:nth-child(2) {
  animation-delay: 0.1s;
}

.van-card:nth-child(3) {
  animation-delay: 0.2s;
}

.van-card:nth-child(4) {
  animation-delay: 0.3s;
}

/* 悬浮效果 */
.enhanced-button:active {
  transform: scale(0.98);
}

/* 状态颜色 - 简洁明了 */
.status-running {
  color: #4caf50;
  font-weight: 600;
}

.status-stopped {
  color: #9e9e9e;
  font-weight: 500;
}

.status-unknown {
  color: #ff9800;
  font-weight: 500;
}

.gear-auto {
  color: #2196f3;
  font-weight: 600;
}

.gear-manual {
  color: #4caf50;
  font-weight: 600;
}

.gear-stop {
  color: #f44336;
  font-weight: 600;
}

.gear-unknown {
  color: #ff9800;
  font-weight: 500;
}

/* 设备类型区分 - 简化版 */
.water-pump {
  background: rgba(33, 150, 243, 0.08) !important;
  border-left: 3px solid #2196f3 !important;
}

.air-pump {
  background: rgba(76, 175, 80, 0.08) !important;
  border-left: 3px solid #4caf50 !important;
}

/* 运行状态标签增强 */
:deep(.van-tag--success) {
  background-color: #4caf50 !important;
  color: white !important;
  font-weight: 600;
}

:deep(.van-tag--primary) {
  background-color: #2196f3 !important;
  color: white !important;
  font-weight: 600;
}

:deep(.van-tag--danger) {
  background-color: #f44336 !important;
  color: white !important;
  font-weight: 600;
}

:deep(.van-tag--warning) {
  background-color: #ff9800 !important;
  color: white !important;
  font-weight: 600;
}
</style>
