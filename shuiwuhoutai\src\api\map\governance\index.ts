import request from "@/utils/request";
import { UserQuery } from "./model";

class GovernanceAPI {
  // 删除设备类型
  static deleteDeviceTypeByIds(ids: string) {
    return request({
      url: "/devicetype/" + ids,
      method: "delete",
    });
  }

  // 获取设备分页列表
  static getDevice(queryParams: UserQuery) {
    return request<any, any>({
      url: "/device",
      method: "get",
      params: queryParams,
    });
  }

  // 获取设备表单详情
  static getDeviceFormData(id: number) {
    return request<any, any>({
      url: "/device/" + id,
      method: "get",
    });
  }

  // 添加设备
  static addDevice(data: any) {
    return request({
      url: "/device",
      method: "post",
      data: data,
    });
  }

  // 修改设备
  static updateDevice(id: number, data: any) {
    return request({
      url: "/device/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除设备
  static deleteDeviceByIds(ids: string) {
    return request({
      url: "/device/" + ids,
      method: "delete",
    });
  }

  // 获取网格信息分页列表
  static getGrid(queryParams: UserQuery) {
    return request<any, any>({
      url: "/gridinformation",
      method: "get",
      params: queryParams,
    });
  }

  // 获取网格信息表单详情
  static getGridFormData(id: number) {
    return request<any, any>({
      url: "/gridinformation/" + id,
      method: "get",
    });
  }

  // 添加网格信息
  static addGrid(data: any) {
    return request({
      url: "/gridinformation",
      method: "post",
      data: data,
    });
  }

  // 修改网格信息
  static updateGrid(id: number, data: any) {
    return request({
      url: "/gridinformation/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除网格信息
  static deleteGridByIds(ids: string) {
    return request({
      url: "/gridinformation/" + ids,
      method: "delete",
    });
  }

  // 获取垃圾分类站分页列表
  static getGarbage(queryParams: UserQuery) {
    return request<any, any>({
      url: "/garbage",
      method: "get",
      params: queryParams,
    });
  }

  // 获取垃圾分类站表单详情
  static getGarbageFormData(id: number) {
    return request<any, any>({
      url: "/garbage/" + id,
      method: "get",
    });
  }

  // 添加垃圾分类站
  static addGarbage(data: any) {
    return request({
      url: "/garbage",
      method: "post",
      data: data,
    });
  }

  // 修改垃圾分类站
  static updateGarbage(id: number, data: any) {
    return request({
      url: "/garbage/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除垃圾分类站
  static deleteGarbageByIds(ids: string) {
    return request({
      url: "/garbage/" + ids,
      method: "delete",
    });
  }

  // 导入垃圾分类站
  static importGarbage(file: FormData) {
    return request({
      url: "/garbage/import",
      method: "post",
      data: file,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  // 导出垃圾分类站
  static exportGarbage(params: {
    columns: string[];
    filters: {
      keywords?: string;
      village?: string;
    };
  }) {
    return request<any, ArrayBuffer>({
      url: "/garbage/export",
      method: "post",
      data: params,
      responseType: "blob",
    });
  }

  // 获取农业管理分页列表
  static getAgricultural(queryParams: UserQuery) {
    return request<any, any>({
      url: "/agricultural",
      method: "get",
      params: queryParams,
    });
  }

  // 获取农业管理表单详情
  static getAgriculturalFormData(id: number) {
    return request<any, any>({
      url: "/agricultural/" + id,
      method: "get",
    });
  }

  // 添加农业管理
  static addAgricultural(data: any) {
    return request({
      url: "/agricultural",
      method: "post",
      data: data,
    });
  }

  // 修改农业管理
  static updateAgricultural(id: number, data: any) {
    return request({
      url: "/agricultural/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除农业管理
  static deleteAgriculturalByIds(ids: string) {
    return request({
      url: "/agricultural/" + ids,
      method: "delete",
    });
  }

  // 获取污水监测点分页列表
  static getSewage(queryParams: UserQuery) {
    return request<any, any>({
      url: "/sewage",
      method: "get",
      params: queryParams,
    });
  }

  // 获取污水监测点表单详情
  static getSewageFormData(id: number) {
    return request<any, any>({
      url: "/sewage/" + id,
      method: "get",
    });
  }

  // 添加污水监测点
  static addSewage(data: any) {
    return request({
      url: "/sewage",
      method: "post",
      data: data,
    });
  }

  // 修改污水监测点
  static updateSewage(id: number, data: any) {
    return request({
      url: "/sewage/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除污水监测点
  static deleteSewageByIds(ids: string) {
    return request({
      url: "/sewage/" + ids,
      method: "delete",
    });
  }

  // 获取监测点位分页列表
  static getMonitorpoint(queryParams: UserQuery) {
    return request<any, any>({
      url: "/monitorpoint",
      method: "get",
      params: queryParams,
    });
  }

  // 获取监测点位表单详情
  static getMonitorpointFormData(id: number) {
    return request<any, any>({
      url: "/monitorpoint/" + id,
      method: "get",
    });
  }

  // 添加监测点位
  static addMonitorpoint(data: any) {
    return request({
      url: "/monitorpoint",
      method: "post",
      data: data,
    });
  }

  // 修改监测点位
  static updateMonitorpoint(id: number, data: any) {
    return request({
      url: "/monitorpoint/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除监测点位
  static deleteMonitorpointByIds(ids: string) {
    return request({
      url: "/monitorpoint/" + ids,
      method: "delete",
    });
  }

  // 获取智慧安防监控点位分页列表
  static getMonitor(queryParams: UserQuery) {
    return request<any, any>({
      url: "/monitor",
      method: "get",
      params: queryParams,
    });
  }

  // 获取智慧安防监控点位表单详情
  static getMonitorFormData(id: number) {
    return request<any, any>({
      url: "/monitor/" + id,
      method: "get",
    });
  }

  // 添加智慧安防监控点位
  static addMonitor(data: any) {
    return request({
      url: "/monitor",
      method: "post",
      data: data,
    });
  }

  // 修改智慧安防监控点位
  static updateMonitor(id: number, data: any) {
    return request({
      url: "/monitor/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除智慧安防监控点位
  static deleteMonitorByIds(ids: string) {
    return request({
      url: "/monitor/" + ids,
      method: "delete",
    });
  }

  // 导入智慧安防监控点位
  static importMonitor(file: FormData) {
    return request({
      url: "/monitor/import",
      method: "post",
      data: file,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  // 导出智慧安防监控点位
  static exportMonitor(params: {
    columns: string[];
    filters: {
      keywords?: string;
    };
  }) {
    return request<any, ArrayBuffer>({
      url: "/monitor/export",
      method: "post",
      data: params,
      responseType: "blob",
    });
  }

  // 获取智慧养老数据分页列表
  static getElderly(queryParams: UserQuery) {
    return request<any, any>({
      url: "/elderly",
      method: "get",
      params: queryParams,
    });
  }

  // 获取智慧养老数据表单详情
  static getElderlyFormData(id: number) {
    return request<any, any>({
      url: "/elderly/" + id,
      method: "get",
    });
  }

  // 添加智慧养老数据
  static addElderly(data: any) {
    return request({
      url: "/elderly",
      method: "post",
      data: data,
    });
  }

  // 修改智慧养老数据
  static updateElderly(id: number, data: any) {
    return request({
      url: "/elderly/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除智慧养老数据
  static deleteElderlyByIds(ids: string) {
    return request({
      url: "/elderly/" + ids,
      method: "delete",
    });
  }

  // 获取关爱活动信息分页列表
  static getCareActivity(queryParams: UserQuery) {
    return request<any, any>({
      url: "/careactivity",
      method: "get",
      params: queryParams,
    });
  }

  // 获取关爱活动信息表单详情
  static getCareActivityFormData(id: number) {
    return request<any, any>({
      url: "/careactivity/" + id,
      method: "get",
    });
  }

  // 添加关爱活动信息
  static addCareActivity(data: any) {
    return request({
      url: "/careactivity",
      method: "post",
      data: data,
    });
  }

  // 修改关爱活动信息
  static updateCareActivity(id: number, data: any) {
    return request({
      url: "/careactivity/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除关爱活动信息
  static deleteCareActivityByIds(ids: string) {
    return request({
      url: "/careactivity/" + ids,
      method: "delete",
    });
  }

  // 获取党支部分页列表 (为关爱活动提供选项)
  static getPartyBranch(queryParams: UserQuery) {
    return request<any, any>({
      url: "/partybranch",
      method: "get",
      params: queryParams,
    });
  }

  // 获取活动类型分页列表 (为关爱活动提供选项)
  static getActivityType(queryParams: UserQuery) {
    return request<any, any>({
      url: "/activitytype",
      method: "get",
      params: queryParams,
    });
  }

  // 获取制度与框架分页列表
  static getCivilization(queryParams: UserQuery) {
    return request<any, any>({
      url: "/civilization",
      method: "get",
      params: queryParams,
    });
  }

  // 获取制度与框架表单详情
  static getCivilizationFormData(id: number) {
    return request<any, any>({
      url: "/civilization/" + id,
      method: "get",
    });
  }

  // 添加制度与框架
  static addCivilization(data: any) {
    return request({
      url: "/civilization",
      method: "post",
      data: data,
    });
  }

  // 修改制度与框架
  static updateCivilization(id: number, data: any) {
    return request({
      url: "/civilization/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除制度与框架
  static deleteCivilizationByIds(ids: string) {
    return request({
      url: "/civilization/" + ids,
      method: "delete",
    });
  }

  // 获取文明实践活动分页列表
  static getCivilizedActivity(queryParams: UserQuery) {
    return request<any, any>({
      url: "/civilizedactivity",
      method: "get",
      params: queryParams,
    });
  }

  // 获取文明实践活动表单详情
  static getCivilizedActivityFormData(id: number) {
    return request<any, any>({
      url: "/civilizedactivity/" + id,
      method: "get",
    });
  }

  // 添加文明实践活动
  static addCivilizedActivity(data: any) {
    return request({
      url: "/civilizedactivity",
      method: "post",
      data: data,
    });
  }

  // 修改文明实践活动
  static updateCivilizedActivity(id: number, data: any) {
    return request({
      url: "/civilizedactivity/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除文明实践活动
  static deleteCivilizedActivityByIds(ids: string) {
    return request({
      url: "/civilizedactivity/" + ids,
      method: "delete",
    });
  }

  // 获取奖项分页列表
  static getAward(queryParams: UserQuery) {
    return request<any, any>({
      url: "/award",
      method: "get",
      params: queryParams,
    });
  }

  // 获取奖项表单详情
  static getAwardFormData(id: number) {
    return request<any, any>({
      url: "/award/" + id,
      method: "get",
    });
  }

  // 添加奖项
  static addAward(data: any) {
    return request({
      url: "/award",
      method: "post",
      data: data,
    });
  }

  // 修改奖项
  static updateAward(id: number, data: any) {
    return request({
      url: "/award/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除奖项
  static deleteAwardByIds(ids: string) {
    return request({
      url: "/award/" + ids,
      method: "delete",
    });
  }

  // 获取页面导航分页列表
  static getNavigation(queryParams: UserQuery) {
    return request<any, any>({
      url: "/navigation",
      method: "get",
      params: queryParams,
    });
  }

  // 获取页面导航表单详情
  static getNavigationFormData(id: number) {
    return request<any, any>({
      url: "/navigation/" + id,
      method: "get",
    });
  }

  // 添加页面导航
  static addNavigation(data: any) {
    return request({
      url: "/navigation",
      method: "post",
      data: data,
    });
  }

  // 修改页面导航
  static updateNavigation(id: number, data: any) {
    return request({
      url: "/navigation/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除页面导航
  static deleteNavigationByIds(ids: string) {
    return request({
      url: "/navigation/" + ids,
      method: "delete",
    });
  }

  // 导入页面导航
  static importNavigation(file: FormData) {
    return request({
      url: "/navigation/import",
      method: "post",
      data: file,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  // 获取宣传视频分页列表
  static getFilm(queryParams: UserQuery) {
    return request<any, any>({
      url: "/film",
      method: "get",
      params: queryParams,
    });
  }

  // 获取宣传视频表单详情
  static getFilmFormData(id: number) {
    return request<any, any>({
      url: "/film/" + id,
      method: "get",
    });
  }

  // 添加宣传视频
  static addFilm(data: any) {
    return request({
      url: "/film",
      method: "post",
      data: data,
    });
  }

  // 修改宣传视频
  static updateFilm(id: number, data: any) {
    return request({
      url: "/film/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除宣传视频
  static deleteFilmByIds(ids: string) {
    return request({
      url: "/film/" + ids,
      method: "delete",
    });
  }
}

export default GovernanceAPI;
