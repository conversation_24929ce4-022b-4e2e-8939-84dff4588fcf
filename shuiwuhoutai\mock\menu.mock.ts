import { defineMock } from "./base";

export default defineMock([
  {
    url: "menus/routes",
    method: ["GET"],
    body: {
      code: "00000",
      data: [
        {
          path: "/system",
          component: "Layout",
          redirect: "/system/user",
          name: "/system",
          meta: {
            title: "系统管理",
            icon: "system",
            hidden: false,
            roles: ["ADMIN"],
            alwaysShow: false,
            params: null,
          },
          children: [
            {
              path: "user",
              component: "system/user/index",
              name: "User",
              meta: {
                title: "用户管理",
                icon: "user",
                hidden: false,
                roles: ["ADMIN"],
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "role",
              component: "system/role/index",
              name: "Role",
              meta: {
                title: "角色管理",
                icon: "role",
                hidden: false,
                roles: ["ADMIN"],
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "menu",
              component: "system/menu/index",
              name: "<PERSON><PERSON>",
              meta: {
                title: "菜单管理",
                icon: "menu",
                hidden: false,
                roles: ["ADMIN"],
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "dept",
              component: "system/dept/index",
              name: "Dept",
              meta: {
                title: "部门管理",
                icon: "tree",
                hidden: false,
                roles: ["ADMIN"],
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "dict",
              component: "system/dict/index",
              name: "Dict",
              meta: {
                title: "字典管理",
                icon: "dict",
                hidden: false,
                roles: ["ADMIN"],
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
          ],
        },
        {
          path: "/api",
          component: "Layout",
          name: "/api",
          meta: {
            title: "接口文档",
            icon: "api",
            hidden: false,
            roles: ["ADMIN"],
            alwaysShow: true,
            params: null,
          },
          children: [
            {
              path: "apifox",
              component: "demo/api/apifox",
              name: "Apifox",
              meta: {
                title: "Apifox",
                icon: "api",
                hidden: false,
                roles: ["ADMIN"],
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
          ],
        },
        {
          path: "/doc",
          component: "Layout",
          redirect: "https://juejin.cn/post/7228990409909108793",
          name: "/doc",
          meta: {
            title: "平台文档",
            icon: "document",
            hidden: false,
            roles: ["ADMIN"],
            alwaysShow: false,
            params: null,
          },
          children: [
            {
              path: "internal-doc",
              component: "demo/internal-doc",
              name: "InternalDoc",
              meta: {
                title: "平台文档(内嵌)",
                icon: "document",
                hidden: false,
                roles: ["ADMIN"],
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "https://juejin.cn/post/7228990409909108793",
              name: "Https://juejin.cn/post/7228990409909108793",
              meta: {
                title: "平台文档(外链)",
                icon: "link",
                hidden: false,
                roles: ["ADMIN"],
                alwaysShow: false,
                params: null,
              },
            },
          ],
        },
        {
          path: "/multi-level",
          component: "Layout",
          name: "/multiLevel",
          meta: {
            title: "多级菜单",
            icon: "cascader",
            hidden: false,
            roles: ["ADMIN"],
            alwaysShow: true,
            params: null,
          },
          children: [
            {
              path: "multi-level1",
              component: "demo/multi-level/level1",
              name: "MultiLevel1",
              meta: {
                title: "菜单一级",
                icon: "",
                hidden: false,
                roles: ["ADMIN"],
                alwaysShow: true,
                params: null,
              },
              children: [
                {
                  path: "multi-level2",
                  component: "demo/multi-level/children/level2",
                  name: "MultiLevel2",
                  meta: {
                    title: "菜单二级",
                    icon: "",
                    hidden: false,
                    roles: ["ADMIN"],
                    alwaysShow: false,
                    params: null,
                  },
                  children: [
                    {
                      path: "multi-level3-1",
                      component: "demo/multi-level/children/children/level3-1",
                      name: "MultiLevel31",
                      meta: {
                        title: "菜单三级-1",
                        icon: "",
                        hidden: false,
                        roles: ["ADMIN"],
                        keepAlive: true,
                        alwaysShow: false,
                        params: null,
                      },
                    },
                    {
                      path: "multi-level3-2",
                      component: "demo/multi-level/children/children/level3-2",
                      name: "MultiLevel32",
                      meta: {
                        title: "菜单三级-2",
                        icon: "",
                        hidden: false,
                        roles: ["ADMIN"],
                        keepAlive: true,
                        alwaysShow: false,
                        params: null,
                      },
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          path: "/component",
          component: "Layout",
          name: "/component",
          meta: {
            title: "组件封装",
            icon: "menu",
            hidden: false,
            roles: ["ADMIN"],
            alwaysShow: false,
            params: null,
          },
          children: [
            {
              path: "curd",
              component: "demo/curd/index",
              name: "Curd",
              meta: {
                title: "增删改查",
                icon: "",
                hidden: false,
                roles: ["ADMIN"],
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "table-select",
              component: "demo/table-select/index",
              name: "TableSelect",
              meta: {
                title: "列表选择器",
                icon: "",
                hidden: false,
                roles: ["ADMIN"],
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "wang-editor",
              component: "demo/wang-editor",
              name: "WangEditor",
              meta: {
                title: "富文本编辑器",
                icon: "",
                hidden: false,
                roles: ["ADMIN"],
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "upload",
              component: "demo/upload",
              name: "Upload",
              meta: {
                title: "图片上传",
                icon: "",
                hidden: false,
                roles: ["ADMIN"],
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "icon-selector",
              component: "demo/icon-selector",
              name: "IconSelector",
              meta: {
                title: "图标选择器",
                icon: "",
                hidden: false,
                roles: ["ADMIN"],
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "dict-demo",
              component: "demo/dict",
              name: "DictDemo",
              meta: {
                title: "字典组件",
                icon: "",
                hidden: false,
                roles: ["ADMIN"],
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
          ],
        },
        {
          path: "/route-param",
          component: "Layout",
          name: "/routeParam",
          meta: {
            title: "路由参数",
            icon: "el-icon-ElementPlus",
            hidden: false,
            roles: ["ADMIN"],
            alwaysShow: true,
            params: null,
          },
          children: [
            {
              path: "route-param-type1",
              component: "demo/route-param",
              name: "RouteParamType1",
              meta: {
                title: "参数(type=1)",
                icon: "el-icon-Star",
                hidden: false,
                roles: ["ADMIN"],
                keepAlive: true,
                alwaysShow: false,
                params: {
                  type: "1",
                },
              },
            },
            {
              path: "route-param-type2",
              component: "demo/route-param",
              name: "RouteParamType2",
              meta: {
                title: "参数(type=2)",
                icon: "el-icon-StarFilled",
                hidden: false,
                roles: ["ADMIN"],
                keepAlive: true,
                alwaysShow: false,
                params: {
                  type: "2",
                },
              },
            },
          ],
        },
        {
          path: "/function",
          component: "Layout",
          name: "/function",
          meta: {
            title: "功能演示",
            icon: "menu",
            hidden: false,
            roles: ["ADMIN"],
            alwaysShow: false,
            params: null,
          },
          children: [
            {
              path: "icon-demo",
              component: "demo/icons",
              name: "IconDemo",
              meta: {
                title: "Icons",
                icon: "el-icon-Notification",
                hidden: false,
                roles: ["ADMIN"],
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "/function/websocket",
              component: "demo/websocket",
              name: "/function/websocket",
              meta: {
                title: "Websocket",
                icon: "",
                hidden: false,
                roles: ["ADMIN"],
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "other/:id",
              component: "demo/other",
              name: "Other/:id",
              meta: {
                title: "敬请期待...",
                icon: "",
                hidden: false,
                roles: ["ADMIN"],
                alwaysShow: false,
                params: null,
              },
            },
          ],
        },
      ],
      msg: "一切ok",
    },
  },

  {
    url: "menus",
    method: ["GET"],
    body: {
      code: "00000",
      data: [
        {
          id: 1,
          parentId: 0,
          name: "系统管理",
          type: "CATALOG",
          path: "/system",
          component: "Layout",
          sort: 1,
          visible: 1,
          icon: "system",
          redirect: "/system/user",
          perm: null,
          children: [
            {
              id: 2,
              parentId: 1,
              name: "用户管理",
              type: "MENU",
              path: "user",
              component: "system/user/index",
              sort: 1,
              visible: 1,
              icon: "user",
              redirect: null,
              perm: null,
              children: [
                {
                  id: 31,
                  parentId: 2,
                  name: "用户新增",
                  type: "BUTTON",
                  path: "",
                  component: null,
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: "",
                  perm: "sys:user:add",
                  children: [],
                },
                {
                  id: 32,
                  parentId: 2,
                  name: "用户编辑",
                  type: "BUTTON",
                  path: "",
                  component: null,
                  sort: 2,
                  visible: 1,
                  icon: "",
                  redirect: "",
                  perm: "sys:user:edit",
                  children: [],
                },
                {
                  id: 33,
                  parentId: 2,
                  name: "用户删除",
                  type: "BUTTON",
                  path: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: "",
                  perm: "sys:user:delete",
                  children: [],
                },
                {
                  id: 88,
                  parentId: 2,
                  name: "重置密码",
                  type: "BUTTON",
                  path: "",
                  component: null,
                  sort: 4,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:user:password:reset",
                  children: [],
                },
              ],
            },
            {
              id: 3,
              parentId: 1,
              name: "角色管理",
              type: "MENU",
              path: "role",
              component: "system/role/index",
              sort: 2,
              visible: 1,
              icon: "role",
              redirect: null,
              perm: null,
              children: [
                {
                  id: 70,
                  parentId: 3,
                  name: "角色新增",
                  type: "BUTTON",
                  path: "",
                  component: null,
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:role:add",
                  children: [],
                },
                {
                  id: 71,
                  parentId: 3,
                  name: "角色编辑",
                  type: "BUTTON",
                  path: "",
                  component: null,
                  sort: 2,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:role:edit",
                  children: [],
                },
                {
                  id: 72,
                  parentId: 3,
                  name: "角色删除",
                  type: "BUTTON",
                  path: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:role:delete",
                  children: [],
                },
              ],
            },
            {
              id: 4,
              parentId: 1,
              name: "菜单管理",
              type: "MENU",
              path: "menu",
              component: "system/menu/index",
              sort: 3,
              visible: 1,
              icon: "menu",
              redirect: null,
              perm: null,
              children: [
                {
                  id: 73,
                  parentId: 4,
                  name: "菜单新增",
                  type: "BUTTON",
                  path: "",
                  component: null,
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:menu:add",
                  children: [],
                },
                {
                  id: 74,
                  parentId: 4,
                  name: "菜单编辑",
                  type: "BUTTON",
                  path: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:menu:edit",
                  children: [],
                },
                {
                  id: 75,
                  parentId: 4,
                  name: "菜单删除",
                  type: "BUTTON",
                  path: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:menu:delete",
                  children: [],
                },
              ],
            },
            {
              id: 5,
              parentId: 1,
              name: "部门管理",
              type: "MENU",
              path: "dept",
              component: "system/dept/index",
              sort: 4,
              visible: 1,
              icon: "tree",
              redirect: null,
              perm: null,
              children: [
                {
                  id: 76,
                  parentId: 5,
                  name: "部门新增",
                  type: "BUTTON",
                  path: "",
                  component: null,
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dept:add",
                  children: [],
                },
                {
                  id: 77,
                  parentId: 5,
                  name: "部门编辑",
                  type: "BUTTON",
                  path: "",
                  component: null,
                  sort: 2,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dept:edit",
                  children: [],
                },
                {
                  id: 78,
                  parentId: 5,
                  name: "部门删除",
                  type: "BUTTON",
                  path: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dept:delete",
                  children: [],
                },
              ],
            },
            {
              id: 6,
              parentId: 1,
              name: "字典管理",
              type: "MENU",
              path: "dict",
              component: "system/dict/index",
              sort: 5,
              visible: 1,
              icon: "dict",
              redirect: null,
              perm: null,
              children: [
                {
                  id: 79,
                  parentId: 6,
                  name: "字典类型新增",
                  type: "BUTTON",
                  path: "",
                  component: null,
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict_type:add",
                  children: [],
                },
                {
                  id: 81,
                  parentId: 6,
                  name: "字典类型编辑",
                  type: "BUTTON",
                  path: "",
                  component: null,
                  sort: 2,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict_type:edit",
                  children: [],
                },
                {
                  id: 84,
                  parentId: 6,
                  name: "字典类型删除",
                  type: "BUTTON",
                  path: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict_type:delete",
                  children: [],
                },
                {
                  id: 85,
                  parentId: 6,
                  name: "字典数据新增",
                  type: "BUTTON",
                  path: "",
                  component: null,
                  sort: 4,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict:add",
                  children: [],
                },
                {
                  id: 86,
                  parentId: 6,
                  name: "字典数据编辑",
                  type: "BUTTON",
                  path: "",
                  component: null,
                  sort: 5,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict:edit",
                  children: [],
                },
                {
                  id: 87,
                  parentId: 6,
                  name: "字典数据删除",
                  type: "BUTTON",
                  path: "",
                  component: null,
                  sort: 6,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict:delete",
                  children: [],
                },
              ],
            },
          ],
        },
        {
          id: 40,
          parentId: 0,
          name: "接口文档",
          type: "CATALOG",
          path: "/api",
          component: "Layout",
          sort: 7,
          visible: 1,
          icon: "api",
          redirect: "",
          perm: null,
          children: [
            {
              id: 41,
              parentId: 40,
              name: "Apifox",
              type: "MENU",
              path: "apifox",
              component: "demo/api/apifox",
              sort: 1,
              visible: 1,
              icon: "api",
              redirect: "",
              perm: null,
              children: [],
            },
            {
              id: 103,
              parentId: 40,
              name: "Swagger",
              type: "MENU",
              path: "swagger",
              component: "demo/api/swagger",
              sort: 2,
              visible: 0,
              icon: "api",
              redirect: "",
              perm: null,
              children: [],
            },
            {
              id: 104,
              parentId: 40,
              name: "Knife4j",
              type: "MENU",
              path: "knife4j",
              component: "demo/api/knife4j",
              sort: 3,
              visible: 0,
              icon: "api",
              redirect: "",
              perm: null,
              children: [],
            },
          ],
        },
        {
          id: 26,
          parentId: 0,
          name: "平台文档",
          type: "CATALOG",
          path: "/doc",
          component: "Layout",
          sort: 8,
          visible: 1,
          icon: "document",
          redirect: null,
          perm: null,
          children: [
            {
              id: 102,
              parentId: 26,
              name: "平台文档(内嵌)",
              type: "EXTLINK",
              path: "internal-doc",
              component: "demo/internal-doc",
              sort: 1,
              visible: 1,
              icon: "document",
              redirect: "",
              perm: null,
              children: [],
            },
            {
              id: 30,
              parentId: 26,
              name: "平台文档(外链)",
              type: "EXTLINK",
              path: "https://juejin.cn/post/7228990409909108793",
              component: "",
              sort: 2,
              visible: 1,
              icon: "link",
              redirect: "",
              perm: null,
              children: [],
            },
          ],
        },
        {
          id: 20,
          parentId: 0,
          name: "多级菜单",
          type: "CATALOG",
          path: "/multi-level",
          component: "Layout",
          sort: 9,
          visible: 1,
          icon: "cascader",
          redirect: "/multi-level/multi-level1",
          perm: null,
          children: [
            {
              id: 21,
              parentId: 20,
              name: "菜单一级",
              type: "MENU",
              path: "multi-level1",
              component: "demo/multi-level/level1",
              sort: 1,
              visible: 1,
              icon: "",
              redirect: "/multi-level/multi-level2",
              perm: null,
              children: [
                {
                  id: 22,
                  parentId: 21,
                  name: "菜单二级",
                  type: "MENU",
                  path: "multi-level2",
                  component: "demo/multi-level/children/level2",
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: "/multi-level/multi-level2/multi-level3-1",
                  perm: null,
                  children: [
                    {
                      id: 23,
                      parentId: 22,
                      name: "菜单三级-1",
                      type: "MENU",
                      path: "multi-level3-1",
                      component: "demo/multi-level/children/children/level3-1",
                      sort: 1,
                      visible: 1,
                      icon: "",
                      redirect: "",
                      perm: null,
                      children: [],
                    },
                    {
                      id: 24,
                      parentId: 22,
                      name: "菜单三级-2",
                      type: "MENU",
                      path: "multi-level3-2",
                      component: "demo/multi-level/children/children/level3-2",
                      sort: 2,
                      visible: 1,
                      icon: "",
                      redirect: "",
                      perm: null,
                      children: [],
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          id: 36,
          parentId: 0,
          name: "组件封装",
          type: "CATALOG",
          path: "/component",
          component: "Layout",
          sort: 10,
          visible: 1,
          icon: "menu",
          redirect: "",
          perm: null,
          children: [
            {
              id: 37,
              parentId: 36,
              name: "富文本编辑器",
              type: "MENU",
              path: "wang-editor",
              component: "demo/wang-editor",
              sort: 1,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: [],
            },
            {
              id: 38,
              parentId: 36,
              name: "图片上传",
              type: "MENU",
              path: "upload",
              component: "demo/upload",
              sort: 2,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: [],
            },
            {
              id: 39,
              parentId: 36,
              name: "图标选择器",
              type: "MENU",
              path: "icon-selector",
              component: "demo/icon-selector",
              sort: 3,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: [],
            },
            {
              id: 95,
              parentId: 36,
              name: "字典组件",
              type: "MENU",
              path: "dict-demo",
              component: "demo/dict",
              sort: 4,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: [],
            },
            {
              id: 93,
              parentId: 36,
              name: "签名",
              type: "MENU",
              path: "signature",
              component: "demo/signature",
              sort: 6,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: [],
            },
            {
              id: 94,
              parentId: 36,
              name: "表格",
              type: "MENU",
              path: "table",
              component: "demo/table",
              sort: 7,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: [],
            },
          ],
        },
        {
          id: 89,
          parentId: 0,
          name: "功能演示",
          type: "CATALOG",
          path: "/function",
          component: "Layout",
          sort: 11,
          visible: 1,
          icon: "menu",
          redirect: "",
          perm: null,
          children: [
            {
              id: 97,
              parentId: 89,
              name: "Icons",
              type: "MENU",
              path: "icon-demo",
              component: "demo/icons",
              sort: 2,
              visible: 1,
              icon: "el-icon-edit",
              redirect: "",
              perm: null,
              children: [],
            },
            {
              id: 90,
              parentId: 89,
              name: "Websocket",
              type: "MENU",
              path: "/function/websocket",
              component: "demo/websocket",
              sort: 3,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: [],
            },
            {
              id: 91,
              parentId: 89,
              name: "敬请期待...",
              type: "CATALOG",
              path: "other",
              component: "demo/other",
              sort: 4,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: [],
            },
          ],
        },
      ],
      msg: "一切ok",
    },
  },

  {
    url: "menus/options",
    method: ["GET"],
    body: {
      code: "00000",
      data: [
        {
          value: 1,
          label: "系统管理",
          children: [
            {
              value: 2,
              label: "用户管理",
              children: [
                {
                  value: 31,
                  label: "用户新增",
                },
                {
                  value: 32,
                  label: "用户编辑",
                },
                {
                  value: 33,
                  label: "用户删除",
                },
                {
                  value: 88,
                  label: "重置密码",
                },
              ],
            },
            {
              value: 3,
              label: "角色管理",
              children: [
                {
                  value: 70,
                  label: "角色新增",
                },
                {
                  value: 71,
                  label: "角色编辑",
                },
                {
                  value: 72,
                  label: "角色删除",
                },
              ],
            },
            {
              value: 4,
              label: "菜单管理",
              children: [
                {
                  value: 73,
                  label: "菜单新增",
                },
                {
                  value: 74,
                  label: "菜单编辑",
                },
                {
                  value: 75,
                  label: "菜单删除",
                },
              ],
            },
            {
              value: 5,
              label: "部门管理",
              children: [
                {
                  value: 76,
                  label: "部门新增",
                },
                {
                  value: 77,
                  label: "部门编辑",
                },
                {
                  value: 78,
                  label: "部门删除",
                },
              ],
            },
            {
              value: 6,
              label: "字典管理",
              children: [
                {
                  value: 79,
                  label: "字典类型新增",
                },
                {
                  value: 81,
                  label: "字典类型编辑",
                },
                {
                  value: 84,
                  label: "字典类型删除",
                },
                {
                  value: 85,
                  label: "字典数据新增",
                },
                {
                  value: 86,
                  label: "字典数据编辑",
                },
                {
                  value: 87,
                  label: "字典数据删除",
                },
              ],
            },
          ],
        },
        {
          value: 40,
          label: "接口文档",
          children: [
            {
              value: 41,
              label: "Apifox",
            },
            {
              value: 103,
              label: "Swagger",
            },
            {
              value: 104,
              label: "Knife4j",
            },
          ],
        },
        {
          value: 26,
          label: "平台文档",
          children: [
            {
              value: 102,
              label: "平台文档(内嵌)",
            },
            {
              value: 30,
              label: "平台文档(外链)",
            },
          ],
        },
        {
          value: 20,
          label: "多级菜单",
          children: [
            {
              value: 21,
              label: "菜单一级",
              children: [
                {
                  value: 22,
                  label: "菜单二级",
                  children: [
                    {
                      value: 23,
                      label: "菜单三级-1",
                    },
                    {
                      value: 24,
                      label: "菜单三级-2",
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          value: 36,
          label: "组件封装",
          children: [
            {
              value: 37,
              label: "富文本编辑器",
            },
            {
              value: 38,
              label: "图片上传",
            },
            {
              value: 39,
              label: "图标选择器",
            },
            {
              value: 95,
              label: "字典组件",
            },
            {
              value: 93,
              label: "签名",
            },
            {
              value: 94,
              label: "表格",
            },
          ],
        },
        {
          value: 89,
          label: "功能演示",
          children: [
            {
              value: 97,
              label: "Icons",
            },
            {
              value: 90,
              label: "Websocket",
            },
            {
              value: 91,
              label: "敬请期待...",
            },
          ],
        },
      ],
      msg: "一切ok",
    },
  },

  // 新增菜单
  {
    url: "menus",
    method: ["POST"],
    body({ body }) {
      return {
        code: "00000",
        data: null,
        msg: "新增菜单" + body.name + "成功",
      };
    },
  },

  // 获取菜单表单数据
  {
    url: "menus/:id/form",
    method: ["GET"],
    body: ({ params }) => {
      return {
        code: "00000",
        data: menuMap[params.id],
        msg: "一切ok",
      };
    },
  },

  // 修改菜单
  {
    url: "menus/:id",
    method: ["PUT"],
    body({ body }) {
      return {
        code: "00000",
        data: null,
        msg: "修改菜单" + body.name + "成功",
      };
    },
  },

  // 删除菜单
  {
    url: "menus/:id",
    method: ["DELETE"],
    body({ params }) {
      return {
        code: "00000",
        data: null,
        msg: "删除菜单" + params.id + "成功",
      };
    },
  },
]);

// 菜单映射表数据
const menuMap: Record<string, any> = {
  1: {
    id: 1,
    parentId: 0,
    name: "系统管理",
    type: "CATALOG",
    path: "/system",
    component: "Layout",
    perm: null,
    visible: 1,
    sort: 1,
    icon: "system",
    redirect: "/system/user",
    keepAlive: null,
    alwaysShow: null,
  },
  2: {
    id: 2,
    parentId: 1,
    name: "用户管理",
    type: "MENU",
    path: "user",
    component: "system/user/index",
    perm: null,
    visible: 1,
    sort: 1,
    icon: "user",
    redirect: null,
    keepAlive: 1,
    alwaysShow: null,
  },
  3: {
    id: 3,
    parentId: 1,
    name: "角色管理",
    type: "MENU",
    path: "role",
    component: "system/role/index",
    perm: null,
    visible: 1,
    sort: 2,
    icon: "role",
    redirect: null,
    keepAlive: 1,
    alwaysShow: null,
  },
  4: {
    id: 4,
    parentId: 1,
    name: "菜单管理",
    type: "MENU",
    path: "menu",
    component: "system/menu/index",
    perm: null,
    visible: 1,
    sort: 3,
    icon: "menu",
    redirect: null,
    keepAlive: 1,
    alwaysShow: null,
  },
  5: {
    id: 5,
    parentId: 1,
    name: "部门管理",
    type: "MENU",
    path: "dept",
    component: "system/dept/index",
    perm: null,
    visible: 1,
    sort: 4,
    icon: "tree",
    redirect: null,
    keepAlive: 1,
    alwaysShow: null,
  },
  6: {
    id: 6,
    parentId: 1,
    name: "字典管理",
    type: "MENU",
    path: "dict",
    component: "system/dict/index",
    perm: null,
    visible: 1,
    sort: 5,
    icon: "dict",
    redirect: null,
    keepAlive: 1,
    alwaysShow: null,
  },
};
