# /f:/水利站/backend/main.py
import threading
import uvicorn
import datetime
import asyncio
import sys
import os

# Windows兼容性处理
try:
    import resource
    HAS_RESOURCE = True
except ImportError:
    HAS_RESOURCE = False

# 从对应模块导入TCP服务器的启动函数和FastAPI应用实例
# 使用优化的异步TCP服务器
from optimized_tcp_server import start_server as start_async_tcp_server
from api_server import app

# 导入数据库创建函数
from database import create_db_and_tables

# 注意：已移除任务调度和状态监控服务

# 导入数据处理服务
from data_downsampling_service import start_data_services


def increase_file_limits():
    """增加系统文件句柄限制，提高并发承受能力"""
    try:
        # 尝试设置软限制和硬限制为10000
        if HAS_RESOURCE and os.name != 'nt':  # 非Windows系统且有resource模块
            current_soft, current_hard = resource.getrlimit(resource.RLIMIT_NOFILE)
            print(f"[main] 当前文件句柄限制: 软限制={current_soft}, 硬限制={current_hard}")
            
            # 尝试设置为10000
            try:
                new_limit = min(10000, current_hard)  # 不能超过硬限制
                resource.setrlimit(resource.RLIMIT_NOFILE, (new_limit, current_hard))
                updated_soft, updated_hard = resource.getrlimit(resource.RLIMIT_NOFILE)
                print(f"[main] 文件句柄限制已调整: 软限制={updated_soft}, 硬限制={updated_hard}")
            except ValueError as e:
                print(f"[main] 无法设置文件句柄限制: {e}")
                print(f"[main] 建议运行: ulimit -n 10000")
        else:
            # Windows系统或无resource模块
            print(f"[main] Windows系统，使用默认的socket连接池管理")
            print(f"[main] 系统将使用异步I/O处理大量并发连接")
            print(f"[main] 建议调整Windows最大TCP连接数：netsh int ipv4 set dynamicport tcp start=1024 num=64512")
            
    except Exception as e:
        print(f"[main] 调整文件句柄限制时出错: {e}")


def run_tcp_server():
    """在一个独立的线程中运行异步TCP服务器"""
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{current_time}] [main] 准备在后台线程启动异步TCP服务器...")
    
    # 创建新的事件循环
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        # 运行异步TCP服务器
        loop.run_until_complete(start_async_tcp_server())
    except KeyboardInterrupt:
        print(f"[{current_time}] [main] 异步TCP服务器收到停止信号")
    except Exception as e:
        print(f"[{current_time}] [main] 异步TCP服务器运行出错: {e}")
    finally:
        loop.close()


if __name__ == "__main__":
    # 首先调整系统文件句柄限制
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{current_time}] [main] 调整系统文件句柄限制...")
    increase_file_limits()

    # 在服务启动前，执行数据库初始化
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{current_time}] [main] 初始化数据库...")
    create_db_and_tables()
    print(f"[{current_time}] [main] 数据库初始化完成。")

    # 已移除后台任务调度器和状态监控守护进程，设备本地自主控制

    # 启动数据降采样和归档服务
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{current_time}] [main] 启动数据降采样和归档服务...")
    start_data_services()

    # 创建并启动TCP服务器的后台线程
    # 设置为守护线程(daemon=True)，这样当主程序(Uvicorn)退出时，该线程也会被自动终止
    tcp_server_thread = threading.Thread(target=run_tcp_server, daemon=True)
    tcp_server_thread.start()

    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{current_time}] [main] 异步TCP服务器已在后台启动。")
    print(f"[{current_time}] [main] 准备启动FastAPI服务器...")

    # 在主线程中启动Uvicorn来运行FastAPI应用
    # Uvicorn是一个ASGI服务器，用于生产环境部署FastAPI应用
    # host="0.0.0.0" 表示监听所有网络接口，服务可以被局域网或公网访问
    # port=8500 是API服务的端口
    # reload=True 会在代码变动后自动重启服务，方便开发调试 (生产环境建议关闭)
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8500,
    )

    print(f"[{current_time}] [main] FastAPI服务器已启动。")
    print("=" * 60)
    print("服务已启动！")
    print(f"  - 异步TCP服务器正在监听端口: 8889")
    print(f"  - API服务器正在运行于: http://0.0.0.0:8500")
    print(f"  - 查看实时数据请访问: http://127.0.0.1:8500/data")
    print(f"  - 查看历史数据请访问: http://127.0.0.1:8500/history/YOUR_DEVICE_SN")
    print(f"  - 查看API文档请访问: http://127.0.0.1:8500/docs")
    print("=" * 60)
