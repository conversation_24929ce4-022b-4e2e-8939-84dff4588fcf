"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const user_entity_1 = require("./entities/user.entity");
const typeorm_2 = require("@nestjs/typeorm");
const jwt_1 = require("@nestjs/jwt");
const role_enum_1 = require("../../role.enum");
let UserService = class UserService {
    constructor(jwtService, usersRepository) {
        this.jwtService = jwtService;
        this.usersRepository = usersRepository;
    }
    async create(user) {
        return await this.usersRepository.save(user);
    }
    async findUser(username) {
        const user = await this.usersRepository.findOne({ username });
        if (user) {
            if (user.roles === role_enum_1.Role.Admin || user.roles === role_enum_1.Role.SuperAdmin) {
                user.permissions = ['*'];
            }
            else {
                if (typeof user.permissions === 'string') {
                    user.permissions = user.permissions
                        .split(',')
                        .filter((p) => p);
                }
                if (!user.permissions || user.permissions.length === 0) {
                    if (user.roles === role_enum_1.Role.User) {
                        user.permissions = ['/dashboard'];
                    }
                    await this.usersRepository.save(user);
                }
            }
        }
        return user;
    }
    async findOne(id) {
        const out = await this.usersRepository.findOne({ id });
        return out;
    }
    async findAll({ pageNo, pageSize, username, roles, id }) {
        const where = {
            roles: (0, typeorm_1.Not)(role_enum_1.Role.SuperAdmin),
        };
        if (username) {
            where['username'] = username;
        }
        if (roles) {
            where['roles'] = roles;
        }
        if (id) {
            where['id'] = id;
        }
        const total = await this.usersRepository.count({ where });
        const list = await this.usersRepository.find({
            where,
            take: pageSize,
            skip: (pageNo - 1) * pageSize,
        });
        return {
            total,
            list: list.map((x) => {
                delete x.password;
                return x;
            }),
            pageNo,
            pageSize,
        };
    }
    async update(id, user) {
        console.log(id, user);
        return await this.usersRepository.update(id, user);
    }
    async delete(id) {
        if (id !== '1')
            return await this.usersRepository.delete(id);
    }
    async getuserbyopenid(openid) {
        console.log(openid);
        const out = await this.usersRepository.findOne({ where: { openid } });
        console.log(out);
        const payload = {
            username: out.username,
            sub: out.id,
            roles: [out.roles],
        };
        delete out.password;
        return Object.assign({ access_token: this.jwtService.sign(payload) }, out);
    }
    async count() {
        return await this.usersRepository.count();
    }
    async openid(id, openid) {
        console.log(id, openid);
        await (0, typeorm_1.getConnection)()
            .createQueryBuilder()
            .update('user')
            .set({ openid: null })
            .where('openid = :openid', { openid })
            .execute();
        return await (0, typeorm_1.getConnection)()
            .createQueryBuilder()
            .update('user')
            .set({ openid })
            .where('id = :id', { id })
            .execute();
    }
    async cancellation(id) {
        console.log(id);
        await (0, typeorm_1.getConnection)()
            .createQueryBuilder()
            .delete()
            .from('user')
            .where('id = :id', { id })
            .execute();
        return;
    }
    async clearopenid(id) {
        console.log(id);
        return await (0, typeorm_1.getConnection)()
            .createQueryBuilder()
            .update('user')
            .set({ openid: null })
            .where('id = :id', { id })
            .execute();
    }
};
UserService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_2.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        typeorm_1.Repository])
], UserService);
exports.UserService = UserService;
//# sourceMappingURL=user.service.js.map