import { ApiProperty } from '@nestjs/swagger';
import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

@Entity('data_processing_log')
export class DataProcessingLog {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number;

  @ApiProperty({ description: '任务类型', example: 'downsampling', enum: ['downsampling', 'archive', 'cleanup'] })
  @Column('varchar', { length: 50, name: 'task_type' })
  taskType: string;

  @ApiProperty({ description: '设备序列号', example: 'WS_001' })
  @Column('varchar', { length: 50, name: 'device_sn', nullable: true })
  deviceSn: string;

  @ApiProperty({ description: '处理开始时间', example: '2025-08-28T13:00:00.000Z' })
  @Column({ type: 'datetime', precision: 3, name: 'start_time' })
  startTime: Date;

  @ApiProperty({ description: '处理结束时间', example: '2025-08-28T13:05:00.000Z' })
  @Column({ type: 'datetime', precision: 3, name: 'end_time' })
  endTime: Date;

  @ApiProperty({ description: '处理的记录数', example: 100 })
  @Column('int', { name: 'processed_records', default: 0 })
  processedRecords: number;

  @ApiProperty({ description: '执行状态', example: 'success', enum: ['success', 'failed', 'running'] })
  @Column('varchar', { length: 20, name: 'execution_status', default: 'success' })
  executionStatus: string;

  @ApiProperty({ description: '错误信息', example: null })
  @Column('text', { name: 'error_message', nullable: true })
  errorMessage: string;

  @ApiProperty({ description: '处理耗时(毫秒)', example: 5000 })
  @Column('int', { name: 'processing_duration_ms', nullable: true })
  processingDurationMs: number;

  @ApiProperty({ description: '记录创建时间' })
  @Column({ 
    type: 'datetime', 
    precision: 3, 
    name: 'created_at',
    default: () => 'CURRENT_TIMESTAMP(3)' 
  })
  createdAt: Date;
}