import request from "@/utils/request";
import {
  PartyBranchQuery,
  PartyBranchPageVO,
  PartyBranchForm,
  PartyMemberQuery,
  PartyMemberPageVO,
  PartyMemberForm,
  ActivityTypeQuery,
  ActivityTypePageVO,
  ActivityTypeForm,
  PartyActivityQuery,
  PartyActivityPageVO,
  PartyActivityForm,
} from "./model";
class PartyAPI {
  // 获取党支部分页列表
  static getPartyBranch(queryParams: PartyBranchQuery) {
    return request<any, PageResult<PartyBranchPageVO[]>>({
      url: "/partybranch",
      method: "get",
      params: queryParams,
    });
  }

  // 获取党支部表单详情
  static getPartyBranchFormData(id: number) {
    return request<any, PartyBranchForm>({
      url: "/partybranch/" + id,
      method: "get",
    });
  }

  // 添加党支部
  static addPartyBranch(data: PartyBranchForm) {
    return request({
      url: "/partybranch",
      method: "post",
      data: data,
    });
  }

  // 修改党支部
  static updatePartyBranch(id: number, data: PartyBranchForm) {
    return request({
      url: "/partybranch/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除党支部
  static deletePartyBranchByIds(ids: string) {
    return request({
      url: "/partybranch/" + ids,
      method: "delete",
    });
  }

  // 获取党员信息分页列表
  static getPartyMember(queryParams: PartyMemberQuery) {
    return request<any, PageResult<PartyMemberPageVO[]>>({
      url: "/memberinformation",
      method: "get",
      params: queryParams,
    });
  }

  // 获取党员信息表单详情
  static getPartyMemberFormData(id: number) {
    return request<any, PartyMemberForm>({
      url: "/memberinformation/" + id,
      method: "get",
    });
  }

  // 添加党员信息
  static addPartyMember(data: PartyMemberForm) {
    return request({
      url: "/memberinformation",
      method: "post",
      data: data,
    });
  }

  // 修改党员信息
  static updatePartyMember(id: number, data: PartyMemberForm) {
    return request({
      url: "/memberinformation/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除党员信息
  static deletePartyMemberByIds(ids: string) {
    return request({
      url: "/memberinformation/" + ids,
      method: "delete",
    });
  }

  // 获取活动类型分页列表
  static getActivityType(queryParams: ActivityTypeQuery) {
    return request<any, PageResult<ActivityTypePageVO[]>>({
      url: "/activitytype",
      method: "get",
      params: queryParams,
    });
  }

  // 获取活动类型表单详情
  static getActivityTypeFormData(id: number) {
    return request<any, ActivityTypeForm>({
      url: "/activitytype/" + id,
      method: "get",
    });
  }

  // 添加活动类型
  static addActivityType(data: ActivityTypeForm) {
    return request({
      url: "/activitytype",
      method: "post",
      data: data,
    });
  }

  // 修改活动类型
  static updateActivityType(id: number, data: ActivityTypeForm) {
    return request({
      url: "/activitytype/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除活动类型
  static deleteActivityTypeByIds(ids: string) {
    return request({
      url: "/activitytype/" + ids,
      method: "delete",
    });
  }

  // 获取党建活动分页列表
  static getPartyActivity(queryParams: PartyActivityQuery) {
    return request<any, PageResult<PartyActivityPageVO[]>>({
      url: "/partyactivity",
      method: "get",
      params: queryParams,
    });
  }

  // 获取党建活动表单详情
  static getPartyActivityFormData(id: number) {
    return request<any, PartyActivityForm>({
      url: "/partyactivity/" + id,
      method: "get",
    });
  }

  // 添加党建活动
  static addPartyActivity(data: PartyActivityForm) {
    return request({
      url: "/partyactivity",
      method: "post",
      data: data,
    });
  }

  // 修改党建活动
  static updatePartyActivity(id: number, data: PartyActivityForm) {
    return request({
      url: "/partyactivity/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除党建活动
  static deletePartyActivityByIds(ids: string) {
    return request({
      url: "/partyactivity/" + ids,
      method: "delete",
    });
  }

  // 导入党员信息
  static importPartyMember(file: FormData) {
    return request({
      url: "/memberinformation/import",
      method: "post",
      data: file,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  // 导出党员信息
  static exportPartyMember(columns: string[]) {
    return request<any, ArrayBuffer>({
      url: "/memberinformation/export",
      method: "post",
      data: { columns },
      responseType: "blob",
    });
  }
}

export default PartyAPI;
