import { ApiProperty } from '@nestjs/swagger';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type DeviceData5sDocument = DeviceData5s & Document;

@Schema({ collection: 'device_data_5s', timestamps: false })
export class DeviceData5s {
  @ApiProperty({ description: '设备序列号', example: 'WS_001' })
  @Prop({ required: true, index: true })
  device_sn: string;

  @ApiProperty({ description: '数据时间戳', example: '2025-08-28T14:30:00.000Z' })
  @Prop({ required: true, index: -1 })
  timestamp: Date;

  @ApiProperty({ 
    description: '原始设备数据', 
    example: { 
      float1: 25.5, 
      float2: 30.2, 
      water_pump1: { status: 1 },
      water_pump2: { status: 0 }
    }
  })
  @Prop({ required: true, type: Object })
  raw_data: Record<string, any>;
}

export const DeviceData5sSchema = SchemaFactory.createForClass(DeviceData5s);