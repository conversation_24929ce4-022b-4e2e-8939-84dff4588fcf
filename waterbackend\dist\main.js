"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const swagger_1 = require("@nestjs/swagger");
const express = require("express");
const express_1 = require("express");
const app_module_1 = require("./app.module");
const platform_express_1 = require("@nestjs/platform-express");
const http = require("http");
async function bootstrap() {
    const server = express();
    const app = await core_1.NestFactory.create(app_module_1.AppModule, new platform_express_1.ExpressAdapter(server));
    app.use((0, express_1.json)({ limit: '500mb' }));
    app.use((0, express_1.urlencoded)({ extended: true, limit: '500mb' }));
    app.enableCors();
    const config = new swagger_1.DocumentBuilder()
        .addBearerAuth()
        .setTitle('吴江水务api文档')
        .setDescription('吴江水务接口文档')
        .setVersion('1.0')
        .addTag('吴江水务')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api-docs', app, document);
    await app.init();
    http.createServer(server).listen(8800);
}
bootstrap();
//# sourceMappingURL=main.js.map