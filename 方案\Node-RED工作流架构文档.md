# Node-RED工作流架构文档

## 概述

本文档详细描述了基于Node-RED的水泵气泵轮换控制系统的工作流架构、功能模块和实现逻辑。该系统运行在USR-M300边缘网关上，实现工业自动化控制和数据采集功能。

## 系统架构

### 工作流概览

```
定时触发 → 状态检查 → 控制逻辑 → 设备执行 → 数据采集 → 状态上报
    ↓         ↓         ↓         ↓         ↓         ↓
  5秒间隔   浮球状态   轮换判断   DO控制   多源采集   TCP上报
```

### 核心Tab配置

- **Tab ID**: `d3caf6daf68cc456`
- **名称**: 水泵气泵轮换控制系统
- **功能**: 实现浮球激活时水泵轮换启动，气泵故障切换和连续运行的控制逻辑

## 主要功能模块

### 1. 定时控制模块

#### 1.1 定时检查节点
```json
{
  "id": "timer_inject_node",
  "type": "inject",
  "name": "定时检查浮球状态",
  "repeat": "5",
  "once": true,
  "onceDelay": 0.1
}
```

**功能特性**:
- 每5秒自动触发一次检查
- 系统启动后0.1秒开始首次检查
- 持续监控浮球状态变化

#### 1.2 手动触发节点
- 支持手动触发检查功能
- 用于调试和测试
- 可模拟各种状态场景

### 2. 水泵轮换控制系统

#### 2.1 浮球状态检测
```javascript
// 浮球状态读取逻辑
var floatStatus = parseInt(msg.payload) || 0;
global.set('currentFloatStatus', floatStatus);

if (floatStatus === 1) {
    // 浮球激活，启动水泵轮换逻辑
    node.log('浮球激活，准备启动水泵');
} else {
    // 浮球复位，停止所有水泵
    node.log('浮球复位，停止所有水泵');
}
```

#### 2.2 自动档位检查
```javascript
// 检查设备是否处于自动档位
function checkAutoMode() {
    var waterPump1Auto = global.get('DI15') || 0;  // 水泵1自动档位
    var waterPump2Auto = global.get('DI16') || 0;  // 水泵2自动档位
    var airPump1Auto = global.get('DI17') || 0;    // 气泵1自动档位
    var airPump2Auto = global.get('DI18') || 0;    // 气泵2自动档位
    
    return {
        waterPump1: waterPump1Auto === 1,
        waterPump2: waterPump2Auto === 1,
        airPump1: airPump1Auto === 1,
        airPump2: airPump2Auto === 1
    };
}
```

#### 2.3 水泵轮换逻辑
```javascript
// 水泵轮换控制逻辑
function waterPumpRotation(floatStatus, autoStatus) {
    if (floatStatus === 1) {
        // 浮球激活时的轮换逻辑
        var lastPump = flow.get('lastActivePump') || 'pump1';
        var nextPump = (lastPump === 'pump1') ? 'pump2' : 'pump1';
        
        if (nextPump === 'pump1' && autoStatus.waterPump1) {
            // 启动水泵1
            flow.set('waterPump1Running', true);
            flow.set('waterPump2Running', false);
        } else if (nextPump === 'pump2' && autoStatus.waterPump2) {
            // 启动水泵2
            flow.set('waterPump1Running', false);
            flow.set('waterPump2Running', true);
        }
        
        flow.set('lastActivePump', nextPump);
    } else {
        // 浮球复位，停止所有水泵
        flow.set('waterPump1Running', false);
        flow.set('waterPump2Running', false);
    }
}
```

### 3. 气泵30分钟交替运行系统

#### 3.1 交替运行逻辑
```javascript
// 气泵30分钟交替运行
function airPumpAlternation() {
    var currentTime = Date.now();
    var lastSwitchTime = flow.get('lastAirPumpSwitchTime') || currentTime;
    var currentAirPump = flow.get('currentAirPump') || 'pump1';
    
    // 检查是否需要切换（30分钟 = 1800000毫秒）
    if (currentTime - lastSwitchTime >= 1800000) {
        var nextPump = (currentAirPump === 'pump1') ? 'pump2' : 'pump1';
        
        // 执行切换
        if (nextPump === 'pump1') {
            flow.set('airPump1Running', true);
            flow.set('airPump2Running', false);
        } else {
            flow.set('airPump1Running', false);
            flow.set('airPump2Running', true);
        }
        
        flow.set('currentAirPump', nextPump);
        flow.set('lastAirPumpSwitchTime', currentTime);
    }
}
```

#### 3.2 故障切换逻辑
```javascript
// 气泵故障检测和切换
function airPumpFailureHandling() {
    var pump1Fault = global.get('airPump1Fault') || false;
    var pump2Fault = global.get('airPump2Fault') || false;
    
    if (pump1Fault && !pump2Fault) {
        // 气泵1故障，气泵2持续运行
        flow.set('airPump1Running', false);
        flow.set('airPump2Running', true);
        node.log('气泵1故障，切换到气泵2持续运行');
    } else if (pump2Fault && !pump1Fault) {
        // 气泵2故障，气泵1持续运行
        flow.set('airPump1Running', true);
        flow.set('airPump2Running', false);
        node.log('气泵2故障，切换到气泵1持续运行');
    } else if (!pump1Fault && !pump2Fault) {
        // 两个气泵都正常，执行正常交替逻辑
        airPumpAlternation();
    }
}
```

### 4. 数据采集系统

#### 4.1 电流数据采集
```javascript
// 电流采集2数据收集
function collectCurrentData() {
    var messages = [
        { payload: '', channel: 'curr2_ch1', deviceName: 'dianliucaiji2' },
        { payload: '', channel: 'curr2_ch2', deviceName: 'dianliucaiji2' },
        { payload: '', channel: 'curr2_ch3', deviceName: 'dianliucaiji2' },
        { payload: '', channel: 'curr2_ch4', deviceName: 'dianliucaiji2' }
    ];
    
    return messages;
}
```

#### 4.2 温湿度数据采集
```javascript
// 温湿度数据采集
function collectTemperatureHumidity() {
    var messages = [
        { payload: '', dataType: 'humidity', deviceName: 'wenshidu' },
        { payload: '', dataType: 'temperature', deviceName: 'wenshidu' }
    ];
    
    return messages;
}
```

#### 4.3 电能表数据采集
```javascript
// 电能表数据采集
function collectPowerMeterData() {
    var messages = [
        { payload: '', param: 'Ua', deviceName: 'diannengbiao' },      // A相电压
        { payload: '', param: 'Ub', deviceName: 'diannengbiao' },      // B相电压
        { payload: '', param: 'Uc', deviceName: 'diannengbiao' },      // C相电压
        { payload: '', param: 'Ia', deviceName: 'diannengbiao' },      // A相电流
        { payload: '', param: 'Ib', deviceName: 'diannengbiao' },      // B相电流
        { payload: '', param: 'Ic', deviceName: 'diannengbiao' },      // C相电流
        { payload: '', param: 'Pa', deviceName: 'diannengbiao' },      // A相功率
        { payload: '', param: 'Pb', deviceName: 'diannengbiao' },      // B相功率
        { payload: '', param: 'Pc', deviceName: 'diannengbiao' },      // C相功率
        { payload: '', param: 'ImpEp', deviceName: 'diannengbiao' }    // 电能
    ];
    
    return messages;
}
```

### 5. DI/DO状态管理

#### 5.1 数字输入状态采集
```javascript
// DI状态采集
function collectDIStatus() {
    var diStatus = {};
    var points = ['DI01', 'DI15', 'DI16', 'DI17', 'DI18'];
    
    for (var i = 0; i < points.length; i++) {
        var point = points[i];
        var value = 0;
        
        if (point === 'DI01') {
            // DI01需要实时读取
            value = global.get('currentDI01') || 0;
        } else {
            value = global.get(point) || 0;
        }
        
        diStatus[point] = value;
    }
    
    diStatus.timestamp = Date.now();
    global.set('diStatus', diStatus);
    
    return diStatus;
}
```

#### 5.2 数字输出状态管理
```javascript
// DO状态管理
function manageDOStatus() {
    var doStatus = {};
    
    // 从flow context获取DO运行状态
    doStatus.DO21 = flow.get('waterPump1Running') ? 1 : 0;  // 水泵1
    doStatus.DO22 = flow.get('waterPump2Running') ? 1 : 0;  // 水泵2
    doStatus.DO23 = flow.get('airPump1Running') ? 1 : 0;    // 气泵1
    doStatus.DO24 = flow.get('airPump2Running') ? 1 : 0;    // 气泵2
    doStatus.DO01 = global.get('faultLightStatus') || 0;    // 故障灯
    
    doStatus.timestamp = Date.now();
    global.set('doStatus', doStatus);
    
    return doStatus;
}
```

## 硬件接口映射

### 数字输入接口 (DI)

| 接口 | 功能描述 | 数据类型 | 取值范围 |
|------|----------|----------|----------|
| DI01 | 浮球开关状态 | INT | 0=未激活, 1=激活 |
| DI15 | 水泵1自动档位 | INT | 0=手动, 1=自动 |
| DI16 | 水泵2自动档位 | INT | 0=手动, 1=自动 |
| DI17 | 气泵1自动档位 | INT | 0=手动, 1=自动 |
| DI18 | 气泵2自动档位 | INT | 0=手动, 1=自动 |

### 数字输出接口 (DO)

| 接口 | 功能描述 | 数据类型 | 取值范围 |
|------|----------|----------|----------|
| DO21 | 水泵1控制 | INT | 0=停止, 1=启动 |
| DO22 | 水泵2控制 | INT | 0=停止, 1=启动 |
| DO23 | 气泵1控制 | INT | 0=停止, 1=启动 |
| DO24 | 气泵2控制 | INT | 0=停止, 1=启动 |
| DO01 | 故障指示灯 | INT | 0=熄灭, 1=点亮 |

## 数据采集设备

### 电流采集2 (dianliucaiji2)

| 通道 | 功能描述 | 数据类型 | 单位 |
|------|----------|----------|------|
| curr2_ch1 | 第1路电流 | FLOAT | A |
| curr2_ch2 | 第2路电流 | FLOAT | A |
| curr2_ch3 | 第3路电流 | FLOAT | A |
| curr2_ch4 | 第4路电流 | FLOAT | A |

### 温湿度传感器 (wenshidu)

| 参数 | 功能描述 | 数据类型 | 单位 |
|------|----------|----------|------|
| temperature | 环境温度 | FLOAT | °C |
| humidity | 环境湿度 | FLOAT | %RH |

### 电能表 (diannengbiao)

| 参数 | 功能描述 | 数据类型 | 单位 |
|------|----------|----------|------|
| Ua, Ub, Uc | 三相电压 | FLOAT | V |
| Ia, Ib, Ic | 三相电流 | FLOAT | A |
| Pa, Pb, Pc | 三相功率 | FLOAT | W |
| ImpEp | 累计电能 | FLOAT | kWh |

## 系统配置和初始化

### 设备信息获取
```javascript
// 获取设备SN和IMEI
function getDeviceInfo() {
    // 启动时自动获取设备标识信息
    var messages = [
        { payload: '', purpose: 'get_sn' },
        { payload: '', purpose: 'get_imei' }
    ];
    
    return messages;
}
```

### 系统状态初始化
```javascript
// 系统初始化
function systemInitialization() {
    // 初始化所有状态变量
    flow.set('waterPump1Running', false);
    flow.set('waterPump2Running', false);
    flow.set('airPump1Running', false);
    flow.set('airPump2Running', false);
    flow.set('lastActivePump', 'pump1');
    flow.set('currentAirPump', 'pump1');
    flow.set('lastAirPumpSwitchTime', Date.now());
    
    global.set('faultLightStatus', 0);
    global.set('airPump1Fault', false);
    global.set('airPump2Fault', false);
    
    node.log('系统状态初始化完成');
}
```

## 调试和监控功能

### 调试节点配置
- **配置信息调试**: 输出系统配置信息
- **状态详情输出**: 完整的系统状态信息
- **数据采集调试**: 数据采集过程监控

### 故障模拟功能
- **气泵故障模拟**: 手动触发气泵故障状态
- **状态重置**: 清除故障状态，恢复正常运行
- **手动控制**: 支持手动控制各设备状态

## 数据上报格式

### TCP上报数据结构
```json
{
  "sn": "USR-M300-001",
  "timestamp": 1735732800000,
  "topic": "/UploadTopic",
  "data": {
    "dianliucaiji2": {
      "curr2_ch1": 10.5,
      "curr2_ch2": 8.2,
      "curr2_ch3": 12.1,
      "curr2_ch4": 9.8
    },
    "wenshidu": {
      "humidity": 65.2,
      "temperature": 25.8
    },
    "diannengbiao": {
      "voltages": {
        "Ua": 220.5,
        "Ub": 219.8,
        "Uc": 221.2
      },
      "currents": {
        "Ia": 15.2,
        "Ib": 14.8,
        "Ic": 15.5
      },
      "powers": {
        "Pa": 3354.6,
        "Pb": 3256.4,
        "Pc": 3428.6
      },
      "energy": {
        "ImpEp": 1234.56
      }
    },
    "DI_status": {
      "DI01": 1,
      "DI15": 1,
      "DI16": 1,
      "DI17": 1,
      "DI18": 1
    },
    "DO_status": {
      "DO21": 1,
      "DO22": 0,
      "DO23": 1,
      "DO24": 0,
      "DO01": 0
    }
  }
}
```

## 性能和可靠性

### 时序控制
- **检查间隔**: 5秒定时检查
- **响应时间**: 毫秒级控制响应
- **切换延时**: 30分钟气泵交替间隔

### 故障处理
- **自动切换**: 设备故障时自动切换备用设备
- **状态保持**: 系统重启后状态恢复
- **错误日志**: 详细的错误记录和追踪

### 数据完整性
- **实时采集**: 5秒间隔数据采集
- **多重验证**: 数据有效性验证
- **时间戳**: 所有数据带时间戳

## 扩展和维护

### 功能扩展
- **新设备接入**: 支持新的传感器和执行器
- **控制逻辑**: 可配置的控制算法
- **数据源**: 支持多种数据采集协议

### 系统维护
- **远程配置**: 支持远程参数调整
- **在线升级**: 工作流在线更新
- **状态监控**: 实时系统状态监控

本Node-RED工作流系统为工业自动化控制提供了完整、可靠、可扩展的解决方案，特别适用于水处理、污水处理等工业应用场景。




