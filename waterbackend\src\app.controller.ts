import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Controller,
  Get,
  Post,
  Request,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import * as requestIp from 'request-ip';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiProperty,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from './auth/auth.JwtAuthGuard';
import { AuthService } from './auth/auth.service';
import { AppService } from './app.service';
import { RolesGuard } from './roles.guard';
import { Roles } from './roles.decorator';
import { Role } from './role.enum';
import { FileInterceptor } from '@nestjs/platform-express';

class loginDto {
  @ApiProperty({ description: '用户名', example: 'admin' })
  username: string;
  @ApiProperty({ description: '密码', example: '123456' })
  password: string;
}
@Controller()
@ApiTags('基本功能')
export class AppController {
  constructor(
    private readonly authService: AuthService,
    private readonly appService: AppService,
  ) {}

  @ApiOperation({ summary: '用户登录' })
  @ApiBearerAuth()
  @UseGuards(AuthGuard('local'))
  @Post('auth/login')
  @ApiBody({ type: loginDto })
  //获取客户端ip
  async login(loginDto, @Request() req) {
    const ip = requestIp.getClientIp(req);
    const result = await this.authService.login(req.user, ip);
    return result;
  }

  @ApiOperation({ summary: '用户登出' })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @Post('auth/logout')
  async logout(@Request() req) {
    await this.authService.logout(req.user);

    return {
      code: 0,
      data: null,
      msg: '登出成功',
    };
  }

  @ApiOperation({ summary: '返回当前用户id 和姓名' })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @Get('profile')
  @CacheKey('user-profile')
  @CacheTTL(1)
  async getProfile(@Request() req) {
    return {
      code: 0,
      data: {
        ...req.user,
        permissions: req.user.permissions || [],
        avatar:
          'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
      },
      msg: '',
    };
  }

  @ApiOperation({ summary: '获取动态路由' })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @Get('routes')
  async getRoutesList(@Request() req) {
    console.log(req.user);
    const routes = [{}];
    return {
      code: 0,
      data: routes,
      msg: 'ok',
    };
  }

  @Post('upload')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(Role.Admin, Role.SuperAdmin)
  @ApiBearerAuth()
  @ApiOperation({ summary: '上传文件' })
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async uploadFile(@UploadedFile() file: Express.Multer.File) {
    const result = await this.appService.uploadFile(file);
    return { code: 0, data: result, msg: 'ok' };
  }
}
