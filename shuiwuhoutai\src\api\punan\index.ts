import request from "@/utils/request";
import {
  PunanBasicInfoQuery,
  PunanBasicInfoPageVO,
  PunanBasicInfoForm,
  PunanEnterpriseInfoQuery,
  PunanEnterpriseInfoPageVO,
  PunanEnterpriseInfoForm,
  PunanFarmlandInfoQuery,
  PunanFarmlandInfoPageVO,
  PunanFarmlandInfoForm,
  PunanFruitBasketInfoQuery,
  PunanFruitBasketInfoPageVO,
  PunanFruitBasketInfoForm,
  PunanVegetableBasketInfoQuery,
  PunanVegetableBasketInfoPageVO,
  PunanVegetableBasketInfoForm,
} from "./model";

class PunanBasicInfoAPI {
  // 获取浦南基本信息分页列表
  static getPage(queryParams: PunanBasicInfoQuery) {
    return request<any, PageResult<PunanBasicInfoPageVO[]>>({
      url: "/punan",
      method: "get",
      params: queryParams,
    });
  }

  // 获取浦南基本信息表单详情
  static getFormData(id: number) {
    return request<any, PunanBasicInfoForm>({
      url: "/punan/" + id,
      method: "get",
    });
  }

  // 添加浦南基本信息
  static add(data: PunanBasicInfoForm) {
    return request({
      url: "/punan",
      method: "post",
      data: data,
    });
  }

  // 修改浦南基本信息
  static update(id: number, data: PunanBasicInfoForm) {
    return request({
      url: "/punan/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除浦南基本信息
  static deleteByIds(ids: string) {
    return request({
      url: "/punan/" + ids,
      method: "delete",
    });
  }

  // 导入浦南基本信息
  static importData(formData: FormData) {
    return request({
      url: "/punan/import",
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }
}

class PunanEnterpriseInfoAPI {
  // 获取浦南企业信息分页列表
  static getPage(queryParams: PunanEnterpriseInfoQuery) {
    return request<any, PageResult<PunanEnterpriseInfoPageVO[]>>({
      url: "/punancompany",
      method: "get",
      params: queryParams,
    });
  }

  // 获取浦南企业信息表单详情
  static getFormData(id: number) {
    return request<any, PunanEnterpriseInfoForm>({
      url: "/punancompany/" + id,
      method: "get",
    });
  }

  // 添加浦南企业信息
  static add(data: PunanEnterpriseInfoForm) {
    return request({
      url: "/punancompany",
      method: "post",
      data: data,
    });
  }

  // 修改浦南企业信息
  static update(id: number, data: PunanEnterpriseInfoForm) {
    return request({
      url: "/punancompany/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除浦南企业信息
  static deleteByIds(ids: string) {
    return request({
      url: "/punancompany/" + ids,
      method: "delete",
    });
  }

  // 导入浦南企业信息
  static importData(formData: FormData) {
    return request({
      url: "/punancompany/import",
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }
}

class PunanFarmlandInfoAPI {
  // 获取浦南耕地信息分页列表
  static getPage(queryParams: PunanFarmlandInfoQuery) {
    return request<any, PageResult<PunanFarmlandInfoPageVO[]>>({
      url: "/punanland",
      method: "get",
      params: queryParams,
    });
  }

  // 获取浦南耕地信息表单详情
  static getFormData(id: number) {
    return request<any, PunanFarmlandInfoForm>({
      url: "/punanland/" + id,
      method: "get",
    });
  }

  // 添加浦南耕地信息
  static add(data: PunanFarmlandInfoForm) {
    return request({
      url: "/punanland",
      method: "post",
      data: data,
    });
  }

  // 修改浦南耕地信息
  static update(id: number, data: PunanFarmlandInfoForm) {
    return request({
      url: "/punanland/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除浦南耕地信息
  static deleteByIds(ids: string) {
    return request({
      url: "/punanland/" + ids,
      method: "delete",
    });
  }
}

class PunanFruitBasketInfoAPI {
  // 获取浦南果篮子信息分页列表
  static getPage(queryParams: PunanFruitBasketInfoQuery) {
    return request<any, PageResult<PunanFruitBasketInfoPageVO[]>>({
      url: "/punanfruit",
      method: "get",
      params: queryParams,
    });
  }

  // 获取浦南果篮子信息表单详情
  static getFormData(id: number) {
    return request<any, PunanFruitBasketInfoForm>({
      url: "/punanfruit/" + id,
      method: "get",
    });
  }

  // 添加浦南果篮子信息
  static add(data: PunanFruitBasketInfoForm) {
    return request({
      url: "/punanfruit",
      method: "post",
      data: data,
    });
  }

  // 修改浦南果篮子信息
  static update(id: number, data: PunanFruitBasketInfoForm) {
    return request({
      url: "/punanfruit/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除浦南果篮子信息
  static deleteByIds(ids: string) {
    return request({
      url: "/punanfruit/" + ids,
      method: "delete",
    });
  }
}

class PunanVegetableBasketInfoAPI {
  // 获取浦南菜篮子信息分页列表
  static getPage(queryParams: PunanVegetableBasketInfoQuery) {
    return request<any, PageResult<PunanVegetableBasketInfoPageVO[]>>({
      url: "/punanvegetable",
      method: "get",
      params: queryParams,
    });
  }

  // 获取浦南菜篮子信息表单详情
  static getFormData(id: number) {
    return request<any, PunanVegetableBasketInfoForm>({
      url: "/punanvegetable/" + id,
      method: "get",
    });
  }

  // 添加浦南菜篮子信息
  static add(data: PunanVegetableBasketInfoForm) {
    return request({
      url: "/punanvegetable",
      method: "post",
      data: data,
    });
  }

  // 修改浦南菜篮子信息
  static update(id: number, data: PunanVegetableBasketInfoForm) {
    return request({
      url: "/punanvegetable/" + id,
      method: "put",
      data: data,
    });
  }

  // 删除浦南菜篮子信息
  static deleteByIds(ids: string) {
    return request({
      url: "/punanvegetable/" + ids,
      method: "delete",
    });
  }
}

export default PunanBasicInfoAPI;
export {
  PunanEnterpriseInfoAPI,
  PunanFarmlandInfoAPI,
  PunanFruitBasketInfoAPI,
  PunanVegetableBasketInfoAPI,
};
