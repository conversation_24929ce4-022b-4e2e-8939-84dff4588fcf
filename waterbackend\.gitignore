# compiled output
/dist
/node_modules
/database
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
720全景.json
厕所.json
拆迁区域.json
党支部.json
出租户.json
党员信息.json
河道信息.json
汇总数据.json
公共服务机构.json
垃圾分类站.json
沿街店铺.json
企业详情.json
经营性收入.json
区域管理.json
人房汇总.json
美景美房.json
人房信息.json
农业管理.json
宣传片.json
设备管理.json
设备类型管理.json
网格信息管理.json
消防栓.json
crud自动化处理脚本.js
import_device.py
/data
