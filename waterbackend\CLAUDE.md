# 吴江水务水利站后端项目

## 变更记录 (Changelog)
- **2025-08-29**: 文档结构完善，补充了waterstation模块信息和开发规范
- **2025-08-28 13:32:27**: 初始化项目架构文档，完成全仓清点和模块扫描

## 项目愿景

吴江水务水利站后端项目是基于 NestJS 框架的村级管理应用系统，提供用户管理、权限控制、文件上传等核心功能，支持物联网设备管理。

## 架构总览

本项目采用经典的三层架构设计：
- **表现层**: 基于 NestJS Controller 的 REST API 接口
- **业务层**: Service 层处理业务逻辑
- **数据层**: TypeORM + MySQL 数据持久化

技术栈：
- **框架**: NestJS 7.x (Node.js + TypeScript)
- **数据库**: MySQL (localhost:3311)
- **认证**: JWT + Passport
- **API 文档**: Swagger
- **测试**: Jest
- **包管理**: pnpm

## 模块结构图

```mermaid
graph TD
    A["(根) waterbackend"] --> B["src"];
    B --> C["baseinfo"];
    B --> D["auth"];
    B --> E["file"];
    B --> F["waterstation"];
    C --> G["user"];
    C --> H["loginlog (已禁用)"];
    C --> I["master (已禁用)"];
    F --> J["entities"];
    F --> K["services"];
    F --> L["controllers"];
    F --> M["schemas"];

    click G "./src/baseinfo/user/CLAUDE.md" "查看 user 模块文档"
    click D "./src/auth/CLAUDE.md" "查看 auth 模块文档"  
    click E "./src/file/CLAUDE.md" "查看 file 模块文档"
    click F "./src/waterstation/CLAUDE.md" "查看 waterstation 模块文档"
```

## 模块索引

| 模块路径 | 职责描述 | 状态 | 入口文件 |
|----------|----------|------|----------|
| `/src/auth` | JWT认证、登录登出、权限验证 | ✅ 活跃 | auth.module.ts |
| `/src/baseinfo/user` | 用户管理、角色权限、密码修改 | ✅ 活跃 | user.module.ts |
| `/src/file` | 文件上传服务 | ✅ 活跃 | file.module.ts |
| `/src/waterstation` | 水利站设备数据采集与管理 | ✅ 活跃 | waterstation.module.ts |
| `/src/baseinfo/loginlog` | 登录日志记录 | ❌ 已禁用 | - |
| `/src/baseinfo/master` | 主数据管理 | ❌ 已禁用 | - |

## 运行与开发

### 环境要求
- Node.js 14.x+
- MySQL 5.7+
- pnpm

### 快速启动
```bash
# 安装依赖
pnpm install

# 开发模式启动
pnpm run start:dev

# 生产模式构建
pnpm run build
pnpm run start:prod

# API 文档访问
http://localhost:8800/api-docs/
```

### 数据库配置
- 主机: localhost:3311
- 用户: water_user
- 密码: water123
- 数据库: water_station_config

## 测试策略

- **单元测试**: Jest 配置完整，支持 *.spec.ts 文件
- **E2E测试**: 配置了端到端测试框架
- **覆盖率**: 支持代码覆盖率统计

运行测试：
```bash
pnpm test              # 单元测试
pnpm test:e2e          # E2E测试
pnpm test:cov          # 覆盖率测试
```

## 编码规范

- **TypeScript**: 严格模式，ESLint + Prettier
- **目录结构**: 按模块划分，每个模块包含 controller、service、dto、entity
- **API设计**: RESTful 风格，统一响应格式 {code, data, msg}
- **认证方式**: JWT Bearer Token

## AI 使用指引

### 常见开发任务
1. **添加新的API接口**: 参考 user.controller.ts 的结构和权限控制
2. **数据模型设计**: 参考 user.entity.ts 的 TypeORM 配置
3. **权限控制**: 使用 @Roles() 装饰器 + RolesGuard
4. **文件上传**: 集成现有的 file.service.ts
5. **设备数据处理**: 参考 waterstation 模块的数据采集和存储模式

### 开发命令速查
```bash
# 开发调试
pnpm run start:dev

# 生产构建
pnpm run build && pnpm run start:prod

# 代码检查和格式化
pnpm run lint
pnpm run format

# 测试执行
pnpm test
pnpm test:cov
```

### 注意事项
- JWT密钥配置在 constants.ts 中，生产环境需更换
- 数据库连接配置硬编码在 app.module.ts，建议使用环境变量
- 缓存配置较为简单，高并发场景需优化