# /f:/水利站/backend/crud.py
"""
数据库操作文件 - 混合架构版本
现在使用：
- MongoDB: 用于设备数据日志和操作日志
- MySQL: 用于任务元数据和系统键值对存储
"""

# 导入新的数据库操作模块
from mongodb_connection import MongoDBOperations
from mysql_connection import MySQLOperations

# 保持原有导入以兼容现有代码
from sqlalchemy.orm import Session
import models
from typing import Optional

# =============================================================================
# MongoDB操作 (设备数据日志和操作日志)
# =============================================================================

def create_device_data_log(db: Session, sn: str, data: dict):
    """
    在MongoDB中创建一条新的设备数据日志。
    注意：db参数已不再使用，保留是为了兼容现有调用
    
    :param db: 数据库会话对象 (已弃用，保留兼容性)
    :param sn: 设备序列号
    :param data: 完整的JSON数据字典
    :return: MongoDB文档ID
    """
    return MongoDBOperations.create_device_data_log(sn, data)


def get_device_data_logs_by_sn(
    db: Session, 
    sn: Optional[str] = None, 
    skip: int = 0, 
    limit: int = 100,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    field_filters: Optional[dict] = None,
    search_keyword: Optional[str] = None
):
    """
    从MongoDB中查询设备数据日志记录。
    注意：db参数已不再使用，保留是为了兼容现有调用
    
    :param db: 数据库会话对象 (已弃用，保留兼容性)
    :param sn: 要查询的设备序列号（可选）
    :param skip: 跳过的记录数（用于分页）
    :param limit: 返回的最大记录数（用于分页）
    :param start_time: 开始时间（ISO格式字符串）
    :param end_time: 结束时间（ISO格式字符串）
    :param field_filters: 字段筛选条件字典
    :param search_keyword: 搜索关键字
    :return: 包含总数和数据列表的字典
    """
    return MongoDBOperations.get_device_data_logs(
        device_sn=sn,
        skip=skip,
        limit=limit,
        start_time=start_time,
        end_time=end_time,
        field_filters=field_filters,
        search_keyword=search_keyword
    )


def create_operation_log(
    db: Session,
    operation_type: str,
    operation_details: str,
    execution_status: str = "pending",
    device_sn: Optional[str] = None,
    command_sent: Optional[str] = None,
    error_message: Optional[str] = None,
    additional_data: Optional[dict] = None,
):
    """
    在MongoDB中创建一条操作日志记录。
    注意：db参数已不再使用，保留是为了兼容现有调用
    
    :param db: 数据库会话对象 (已弃用，保留兼容性)
    :param operation_type: 操作类型
    :param operation_details: 操作详细描述
    :param execution_status: 执行状态
    :param device_sn: 相关设备序列号
    :param command_sent: 发送的命令内容
    :param error_message: 错误信息
    :param additional_data: 额外的上下文数据
    :return: MongoDB文档ID
    """
    return MongoDBOperations.create_operation_log(
        operation_type=operation_type,
        operation_details=operation_details,
        execution_status=execution_status,
        device_sn=device_sn,
        command_sent=command_sent,
        error_message=error_message,
        additional_data=additional_data
    )


def update_operation_log_status(
    db: Session, log_id: str, execution_status: str, error_message: Optional[str] = None
):
    """
    更新MongoDB中操作日志的执行状态。
    注意：db参数已不再使用，保留是为了兼容现有调用
    注意：log_id现在是MongoDB的字符串ID，不再是整数
    
    :param db: 数据库会话对象 (已弃用，保留兼容性)
    :param log_id: 日志记录ID (MongoDB字符串ID)
    :param execution_status: 新的执行状态
    :param error_message: 错误信息（如果有）
    :return: 更新是否成功
    """
    return MongoDBOperations.update_operation_log_status(
        log_id=log_id,
        execution_status=execution_status,
        error_message=error_message
    )


def get_operation_logs(
    db: Session,
    operation_type: Optional[str] = None,
    device_sn: Optional[str] = None,
    execution_status: Optional[str] = None,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    search_keyword: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
):
    """
    从MongoDB中查询操作日志记录。
    注意：db参数已不再使用，保留是为了兼容现有调用
    
    :param db: 数据库会话对象 (已弃用，保留兼容性)
    :param operation_type: 过滤操作类型
    :param device_sn: 过滤设备序列号
    :param execution_status: 过滤执行状态
    :param start_time: 开始时间
    :param end_time: 结束时间
    :param search_keyword: 搜索关键字
    :param skip: 跳过的记录数
    :param limit: 返回的最大记录数
    :return: 包含总数和数据列表的字典
    """
    return MongoDBOperations.get_operation_logs(
        operation_type=operation_type,
        device_sn=device_sn,
        execution_status=execution_status,
        start_time=start_time,
        end_time=end_time,
        search_keyword=search_keyword,
        skip=skip,
        limit=limit
    )


# =============================================================================
# MySQL操作 (任务元数据和系统键值对)
# =============================================================================

def create_task_metadata(db: Session, task_id: str, task_type: str, metadata: dict):
    """
    在MySQL中创建一条任务元数据记录。
    注意：db参数已不再使用，保留是为了兼容现有调用
    
    :param db: 数据库会话对象 (已弃用，保留兼容性)
    :param task_id: 任务ID
    :param task_type: 任务类型
    :param metadata: 任务元数据
    :return: 任务元数据对象
    """
    return MySQLOperations.create_task_metadata(task_id, task_type, metadata)


def get_all_task_metadata(db: Session):
    """
    从MySQL中获取所有任务元数据记录。
    注意：db参数已不再使用，保留是为了兼容现有调用
    
    :param db: 数据库会话对象 (已弃用，保留兼容性)
    :return: 任务元数据列表
    """
    return MySQLOperations.get_all_task_metadata()


def delete_task_metadata(db: Session, task_id: str):
    """
    从MySQL中根据任务ID删除一条元数据记录。
    注意：db参数已不再使用，保留是为了兼容现有调用
    
    :param db: 数据库会话对象 (已弃用，保留兼容性)
    :param task_id: 任务ID
    :return: 删除是否成功
    """
    return MySQLOperations.delete_task_metadata(task_id)


def get_kv(db: Session, key: str) -> Optional[str]:
    """
    从MySQL中获取一个键值对的值。
    注意：db参数已不再使用，保留是为了兼容现有调用
    
    :param db: 数据库会话对象 (已弃用，保留兼容性)
    :param key: 键名
    :return: 键值
    """
    return MySQLOperations.get_kv(key)


def set_kv(db: Session, key: str, value: str):
    """
    在MySQL中设置一个键值对的值 (如果存在则更新, 不存在则创建)。
    注意：db参数已不再使用，保留是为了兼容现有调用
    
    :param db: 数据库会话对象 (已弃用，保留兼容性)
    :param key: 键名
    :param value: 键值
    :return: 键值对对象
    """
    return MySQLOperations.set_kv(key, value)


# =============================================================================
# 新增便捷函数 (不依赖Session参数)
# =============================================================================

def get_all_system_kv() -> dict:
    """
    获取所有系统键值对
    :return: 键值对字典
    """
    return MySQLOperations.get_all_kv()


def delete_system_kv(key: str) -> bool:
    """
    删除指定的系统键值对
    :param key: 键名
    :return: 删除是否成功
    """
    return MySQLOperations.delete_kv(key)


# =============================================================================
# 兼容性说明
# =============================================================================

# 以下是为了保持与现有代码的兼容性而保留的函数签名。
# 实际操作已经迁移到相应的数据库连接模块中。
# 
# 如果您在其他地方看到这些函数的调用，它们仍然可以正常工作，
# 但建议在新代码中直接使用对应的数据库操作类：
#
# - MongoDBOperations (mongodb_connection.py) 用于：
#   * create_device_data_log
#   * get_device_data_logs_by_sn  
#   * create_operation_log
#   * update_operation_log_status
#   * get_operation_logs
#
# - MySQLOperations (mysql_connection.py) 用于：
#   * create_task_metadata
#   * get_all_task_metadata
#   * delete_task_metadata
#   * get_kv
#   * set_kv