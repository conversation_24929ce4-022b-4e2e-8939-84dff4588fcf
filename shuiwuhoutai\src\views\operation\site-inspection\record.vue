<template>
  <div class="site-inspection-record">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <el-button type="primary" @click="handleCreateRecord"
            >新增记录</el-button
          >
        </div>
      </template>

      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="记录编号">
          <el-input
            v-model="searchForm.recordNo"
            placeholder="请输入记录编号"
            clearable
          />
        </el-form-item>
        <el-form-item label="站点名称">
          <el-input
            v-model="searchForm.siteName"
            placeholder="请输入站点名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="巡检结果">
          <el-select
            v-model="searchForm.result"
            placeholder="请选择结果"
            clearable
          >
            <el-option label="正常" value="normal" />
            <el-option label="异常" value="abnormal" />
            <el-option label="需维修" value="repair" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="recordData" stripe>
        <el-table-column prop="recordNo" label="记录编号" width="120" />
        <el-table-column prop="siteName" label="站点名称" />
        <el-table-column prop="siteType" label="站点类型" width="120">
          <template #default="{ row }">
            {{ getSiteTypeText(row.siteType) }}
          </template>
        </el-table-column>
        <el-table-column prop="inspector" label="巡检员" width="100" />
        <el-table-column prop="inspectionTime" label="巡检时间" width="150" />
        <el-table-column prop="result" label="巡检结果" width="100">
          <template #default="{ row }">
            <el-tag :type="getResultType(row.result)">{{
              getResultText(row.result)
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="issueCount" label="问题数量" width="100" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)"
              >查看</el-button
            >
            <el-button type="success" size="small" @click="handleEdit(row)"
              >编辑</el-button
            >
            <el-button type="danger" size="small" @click="handleDelete(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";

const searchForm = reactive({
  recordNo: "",
  siteName: "",
  result: "",
});

const recordData = ref([
  {
    id: 1,
    recordNo: "SR202401001",
    siteName: "1号水泵站",
    siteType: "pump_station",
    inspector: "张三",
    inspectionTime: "2024-01-01 10:30:00",
    result: "normal",
    issueCount: 0,
  },
  {
    id: 2,
    recordNo: "SR202401002",
    siteName: "污水处理厂A厂",
    siteType: "treatment_plant",
    inspector: "李四",
    inspectionTime: "2024-01-01 14:20:00",
    result: "abnormal",
    issueCount: 2,
  },
]);

const getSiteTypeText = (type: string) => {
  const texts: Record<string, string> = {
    pump_station: "水泵站",
    treatment_plant: "处理厂",
    distribution_station: "配水站",
    monitoring_station: "监测站",
  };
  return texts[type] || "其他";
};

const getResultType = (
  result: string
): "success" | "primary" | "warning" | "info" | "danger" => {
  const types: Record<
    string,
    "success" | "primary" | "warning" | "info" | "danger"
  > = {
    normal: "success",
    abnormal: "warning",
    repair: "danger",
  };
  return types[result] || "info";
};

const getResultText = (result: string) => {
  const texts: Record<string, string> = {
    normal: "正常",
    abnormal: "异常",
    repair: "需维修",
  };
  return texts[result] || "未知";
};

const handleCreateRecord = () => console.log("新增记录");
const handleSearch = () => console.log("搜索记录", searchForm);
const handleReset = () =>
  Object.assign(searchForm, { recordNo: "", siteName: "", result: "" });
const handleView = (row: any) => console.log("查看记录", row);
const handleEdit = (row: any) => console.log("编辑记录", row);
const handleDelete = (row: any) => console.log("删除记录", row);

onMounted(() => {
  // 初始化
});
</script>

<style scoped>
.site-inspection-record {
  padding: 20px;
}

.page-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}
</style>
