# /f:/水利站/backend/mysql_models.py
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Float, JSON, Index, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.sql import func
from datetime import datetime, timedelta
import logging
from typing import Optional, Dict, List
import json

logger = logging.getLogger(__name__)

# MySQL Base类
MySQLBase = declarative_base()

class DeviceDataMinute(MySQLBase):
    """
    分钟级设备数据表 - 从5秒数据降采样而来
    保留时间：7天
    """
    __tablename__ = "device_data_minute"

    id = Column(Integer, primary_key=True, index=True, comment="记录ID")
    device_sn = Column(String(50), nullable=False, index=True, comment="设备序列号")
    timestamp = Column(DateTime(timezone=True), nullable=False, comment="分钟时间戳")
    
    # 统计数据字段
    avg_data = Column(JSON, nullable=False, comment="平均值数据")
    max_data = Column(JSON, nullable=False, comment="最大值数据")
    min_data = Column(JSON, nullable=False, comment="最小值数据")
    
    # 采样统计信息
    sample_count = Column(Integer, nullable=False, comment="原始样本数量")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="记录创建时间")

    # 创建复合索引
    __table_args__ = (
        Index('idx_device_time_minute', 'device_sn', 'timestamp'),
        Index('idx_timestamp_minute', 'timestamp'),
    )


class DeviceDataHour(MySQLBase):
    """
    小时级设备数据表 - 从分钟级数据进一步降采样
    保留时间：长期存储
    """
    __tablename__ = "device_data_hour"

    id = Column(Integer, primary_key=True, index=True, comment="记录ID")
    device_sn = Column(String(50), nullable=False, index=True, comment="设备序列号")
    timestamp = Column(DateTime(timezone=True), nullable=False, comment="小时时间戳")
    
    # 统计数据字段
    avg_data = Column(JSON, nullable=False, comment="平均值数据")
    max_data = Column(JSON, nullable=False, comment="最大值数据")
    min_data = Column(JSON, nullable=False, comment="最小值数据")
    
    # 采样统计信息
    minute_sample_count = Column(Integer, nullable=False, comment="分钟级样本数量")
    original_sample_count = Column(Integer, nullable=False, comment="原始5秒级样本总数")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="记录创建时间")

    # 创建复合索引
    __table_args__ = (
        Index('idx_device_time_hour', 'device_sn', 'timestamp'),
        Index('idx_timestamp_hour', 'timestamp'),
    )


# 保留原有的系统表，继承自原Base
from database import Base as OriginalBase

class TaskMetadata(OriginalBase):
    """任务元数据表 - 保持不变"""
    __tablename__ = "task_metadata"
    __table_args__ = {'extend_existing': True}
    
    task_id = Column(String, primary_key=True, index=True)
    task_type = Column(String, nullable=False, comment="任务类型")
    task_data = Column(JSON, nullable=False, comment="任务的完整元数据")


class SystemKVStore(OriginalBase):
    """系统键值存储表 - 保持不变"""
    __tablename__ = "system_kv_store"
    __table_args__ = {'extend_existing': True}
    
    key = Column(String, primary_key=True, comment="状态的唯一键")
    value = Column(String, nullable=False, comment="状态的值")


class OperationLog(OriginalBase):
    """操作日志表 - 保持不变"""
    __tablename__ = "operation_logs"
    __table_args__ = {'extend_existing': True}
    
    id = Column(Integer, primary_key=True, index=True, comment="日志记录ID")
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), comment="操作时间")
    operation_type = Column(String, nullable=False, index=True, comment="操作类型")
    device_sn = Column(String, index=True, nullable=True, comment="相关设备序列号")
    operation_details = Column(String, nullable=False, comment="操作详细描述")
    command_sent = Column(String, nullable=True, comment="发送的命令内容")
    execution_status = Column(String, nullable=False, comment="执行状态")
    error_message = Column(String, nullable=True, comment="错误信息")
    additional_data = Column(JSON, nullable=True, comment="额外的上下文数据")


class MySQLManager:
    """
    MySQL数据库管理器 - 用于存储降采样后的分钟级和小时级数据
    """
    
    def __init__(self, connection_string: str = None):
        """
        初始化MySQL连接
        
        Args:
            connection_string: MySQL连接字符串，格式：mysql+pymysql://user:password@host:port/database
        """
        if connection_string is None:
            # 默认连接字符串，使用Docker配置
            connection_string = "mysql+pymysql://water_user:water123@localhost:3311/water_station_config"
        
        self.engine = create_engine(
            connection_string,
            pool_pre_ping=True,  # 连接池预ping，确保连接有效
            pool_recycle=3600,   # 连接回收时间1小时
            echo=False           # 生产环境不输出SQL日志
        )
        
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # 创建表
        self._create_tables()
    
    def _create_tables(self):
        """创建数据库表"""
        try:
            MySQLBase.metadata.create_all(bind=self.engine)
            logger.info("MySQL数据表创建成功")
        except Exception as e:
            logger.error(f"MySQL数据表创建失败: {e}")
    
    def get_db_session(self) -> Session:
        """获取数据库会话"""
        return self.SessionLocal()
    
    def insert_minute_data(self, device_sn: str, timestamp: datetime, avg_data: dict, 
                          max_data: dict, min_data: dict, sample_count: int) -> Optional[int]:
        """
        插入分钟级数据
        
        Args:
            device_sn: 设备序列号
            timestamp: 分钟时间戳
            avg_data: 平均值数据
            max_data: 最大值数据
            min_data: 最小值数据
            sample_count: 原始样本数量
            
        Returns:
            int: 插入记录的ID，失败时返回None
        """
        db = self.get_db_session()
        try:
            minute_record = DeviceDataMinute(
                device_sn=device_sn,
                timestamp=timestamp,
                avg_data=avg_data,
                max_data=max_data,
                min_data=min_data,
                sample_count=sample_count
            )
            
            db.add(minute_record)
            db.commit()
            db.refresh(minute_record)
            
            logger.debug(f"分钟级数据插入成功: device_sn={device_sn}, timestamp={timestamp}")
            return minute_record.id
            
        except Exception as e:
            db.rollback()
            logger.error(f"分钟级数据插入失败: device_sn={device_sn}, error={e}")
            return None
        finally:
            db.close()
    
    def insert_hour_data(self, device_sn: str, timestamp: datetime, avg_data: dict,
                        max_data: dict, min_data: dict, minute_sample_count: int,
                        original_sample_count: int) -> Optional[int]:
        """
        插入小时级数据
        
        Args:
            device_sn: 设备序列号
            timestamp: 小时时间戳
            avg_data: 平均值数据
            max_data: 最大值数据
            min_data: 最小值数据
            minute_sample_count: 分钟级样本数量
            original_sample_count: 原始5秒级样本总数
            
        Returns:
            int: 插入记录的ID，失败时返回None
        """
        db = self.get_db_session()
        try:
            hour_record = DeviceDataHour(
                device_sn=device_sn,
                timestamp=timestamp,
                avg_data=avg_data,
                max_data=max_data,
                min_data=min_data,
                minute_sample_count=minute_sample_count,
                original_sample_count=original_sample_count
            )
            
            db.add(hour_record)
            db.commit()
            db.refresh(hour_record)
            
            logger.debug(f"小时级数据插入成功: device_sn={device_sn}, timestamp={timestamp}")
            return hour_record.id
            
        except Exception as e:
            db.rollback()
            logger.error(f"小时级数据插入失败: device_sn={device_sn}, error={e}")
            return None
        finally:
            db.close()
    
    def get_minute_data_for_hour_aggregation(self, start_time: datetime, end_time: datetime, 
                                           device_sn: Optional[str] = None) -> List[DeviceDataMinute]:
        """
        获取分钟级数据用于小时级聚合
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            device_sn: 设备序列号（可选）
            
        Returns:
            List[DeviceDataMinute]: 分钟级数据列表
        """
        db = self.get_db_session()
        try:
            query = db.query(DeviceDataMinute).filter(
                DeviceDataMinute.timestamp >= start_time,
                DeviceDataMinute.timestamp < end_time
            )
            
            if device_sn:
                query = query.filter(DeviceDataMinute.device_sn == device_sn)
            
            return query.order_by(DeviceDataMinute.timestamp).all()
            
        except Exception as e:
            logger.error(f"分钟级数据查询失败: {e}")
            return []
        finally:
            db.close()
    
    def delete_old_minute_data(self, cutoff_time: datetime) -> int:
        """
        删除超过7天的分钟级数据
        
        Args:
            cutoff_time: 删除此时间之前的数据
            
        Returns:
            int: 删除的记录数量
        """
        db = self.get_db_session()
        try:
            result = db.query(DeviceDataMinute).filter(
                DeviceDataMinute.timestamp < cutoff_time
            ).delete()
            
            db.commit()
            
            if result > 0:
                logger.info(f"删除旧分钟级数据: {result}条记录")
            
            return result
            
        except Exception as e:
            db.rollback()
            logger.error(f"删除旧分钟级数据失败: {e}")
            return 0
        finally:
            db.close()
    
    def get_aggregated_data(self, device_sn: Optional[str], start_time: datetime, 
                           end_time: datetime, granularity: str = "minute") -> List[Dict]:
        """
        获取聚合数据
        
        Args:
            device_sn: 设备序列号
            start_time: 开始时间
            end_time: 结束时间
            granularity: 数据粒度 ("minute" 或 "hour")
            
        Returns:
            List[Dict]: 聚合数据列表
        """
        db = self.get_db_session()
        try:
            if granularity == "hour":
                model = DeviceDataHour
            else:
                model = DeviceDataMinute
            
            query = db.query(model).filter(
                model.timestamp >= start_time,
                model.timestamp <= end_time
            )
            
            if device_sn:
                query = query.filter(model.device_sn == device_sn)
            
            records = query.order_by(model.timestamp).all()
            
            # 转换为字典格式
            result = []
            for record in records:
                data = {
                    "device_sn": record.device_sn,
                    "timestamp": record.timestamp.isoformat(),
                    "avg_data": record.avg_data,
                    "max_data": record.max_data,
                    "min_data": record.min_data,
                }
                
                if granularity == "hour":
                    data["minute_sample_count"] = record.minute_sample_count
                    data["original_sample_count"] = record.original_sample_count
                else:
                    data["sample_count"] = record.sample_count
                
                result.append(data)
            
            return result
            
        except Exception as e:
            logger.error(f"聚合数据查询失败: {e}")
            return []
        finally:
            db.close()

# 全局MySQL管理器实例
mysql_manager = None

def get_mysql_manager() -> MySQLManager:
    """获取MySQL管理器实例"""
    global mysql_manager
    if mysql_manager is None:
        mysql_manager = MySQLManager()
    return mysql_manager

def close_mysql_connection():
    """关闭MySQL连接"""
    global mysql_manager
    if mysql_manager:
        mysql_manager.engine.dispose()
        mysql_manager = None