import { Injectable } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class FileService {
  private uploadDir: string;
  private relativePath: string;

  constructor() {
    this.uploadDir = './client/uploads';
    this.relativePath = 'uploads';
  }

  async uploadFile(file: Express.Multer.File): Promise<{ filePath: string }> {
    if (!fs.existsSync(this.uploadDir)) {
      fs.mkdirSync(this.uploadDir, { recursive: true });
    }
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    const filename =
      file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname);
    const absoluteFilePath = path.join(this.uploadDir, filename);
    await fs.promises.writeFile(absoluteFilePath, file.buffer);
    const relativeFilePath = path.join(this.relativePath, filename);
    return { filePath: relativeFilePath.replace(/\\/g, '/') };
  }

  async deleteFile(filePath: string): Promise<{ success: boolean }> {
    try {
      const fullPath = path.join(this.uploadDir, filePath);
      await fs.promises.unlink(fullPath);
      return { success: true };
    } catch (error) {
      console.error('Error deleting file:', error);
      return { success: false };
    }
  }
}
