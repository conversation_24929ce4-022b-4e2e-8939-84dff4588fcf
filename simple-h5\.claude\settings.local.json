{"permissions": {"allow": ["Bash(npm install:*)", "Bash(git checkout -- src/views/DeviceStatusView.vue)", "Bash(node test/api-test.js)", "Bash(node test/node-api-test.js)", "Bash(node test/complete-api-test.js)", "Bash(node test/fixed-api-test.js)", "Bash(npm run dev)", "Bash(node:*)", "Bash(npm run type-check:*)", "Bash(npm run lint)", "Bash(npm run build)", "Bash(grep:*)", "Bash(rm:*)", "Bash(npm uninstall:*)", "Bash(git checkout:*)"], "deny": [], "defaultMode": "acceptEdits", "additionalDirectories": ["f:\\水利站\\simple-h5\\src\\views", "f:\\水利站\\simple-h5"]}}